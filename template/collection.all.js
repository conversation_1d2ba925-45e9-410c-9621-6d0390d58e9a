function collections_promotion() {
    var saleWrap = document.querySelector(".collection_header-banner__bt37");
    var collection_banner = document.getElementById("collection_banner_image");
    if (!saleWrap) return false;
    var saleCounting = document.getElementById("saleCounting");
    var _startDate = new Date(saleWrap.dataset.start).getTime();
    var _endDate = new Date(saleWrap.dataset.end).getTime();
    var _localDate = new Date().getTime();
    var tow = function (n) {
        return n >= 0 && n < 10 ? "0" + n : "" + n;
    };

    if (_endDate > _localDate && _startDate < _localDate) {
        if (Boulies.setting.screenSize == "small") {
            collection_banner.src = saleWrap.dataset.spbanner;
        } else {
            collection_banner.src = saleWrap.dataset.pcbanner;
        }
        document.querySelector(".home-saleBannerContent").style.display = "block";
        // If sale end date is less than 5 days, then show countdown.
        if (_endDate - _localDate < 432000000) {
            document.getElementById("saleEndDate").style.display = "block";
            document.querySelector(".bt5-sale-note").style.display = "none";
            var counting = setInterval(function () {
                var now = new Date().getTime();
                var distance = _endDate - now;
                // Time calculations for days, hours, minutes and seconds
                var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((distance % (1000 * 60)) / 1000);

                saleCounting.innerHTML = "<b>" + tow(days) + "</b>d<b>" + tow(hours) + "</b>h<b>" + tow(minutes) + "</b>m<b>" + tow(seconds) + "</b>s";
                // If the count down is over, write some text
                if (distance < 0) {
                    clearInterval(counting);
                    document.getElementById("saleEndDate").innerHTML = "Sale is ended.";
                }
            }, 1000);
        }
    } else {
        collection_banner.src = collection_banner.dataset.defaultBanner;
    }
    var colorThief = new ColorThief();
    collection_banner.addEventListener("load", function () {
        var color = colorThief.getColor(collection_banner).join();
        collection_banner.style.display = "block";
        if (Boulies.setting.screenSize == "small") {
            document.querySelector(".collection_header-title__bt37").style["background"] =
                "linear-gradient(180deg,rgba(" + color + ",1) 0,rgba(" + color + ",1) 50%,rgba(" + color + ",0) 100%)";
        } else {
            document.querySelector(".collection_header-title__bt37").style["background"] =
                "linear-gradient(90deg,rgba(" + color + ",1) 0,rgba(" + color + ",1) 50%,rgba(" + color + ",0) 100%)";
        }
    });
}
// banner
collections_promotion();

function initAddToCart() {
    document.querySelectorAll("#add-to-cart-btn__bt37").forEach(function (element) {
        element.addEventListener("click", function (e) {
            e.preventDefault();
            let form = element.closest("form");
            let formData = new FormData(form);
            addToCart(this, formData);
        });
    });
}

function addToCart(target, formData) {
    if (target.disabled == true) return false;
    let chairData = formData;
    // create main item data
    let itemsData = {
        items: [
            {
                id: chairData.get("id"),
                quantity: chairData.get("quantity"),
                properties: {
                    warranty: chairData.get("properties[warranty]"),
                    accessories: chairData.get("properties[accessories]"),
                },
            },
        ],
    };

    if (chairData.get("properties[Estimated_to_ship_out]")) {
        itemsData.items[0].properties["Estimated_to_ship_out"] = chairData.get("properties[Estimated_to_ship_out]");
    }

    // set cart discount attributes
    let attributesData = {
        attributes: {
            cart_discount_code: "",
            cart_discount_amount: "",
        },
    };
    attributesData.attributes["cart_discount_code"] = chairData?.get("attributes[cart_discount_code]");
    attributesData.attributes["cart_discount_amount"] = chairData?.get("attributes[cart_discount_amount]");

    // // set addon item data
    // document.querySelectorAll('.bt32-activedAddonItem.bt32-addonItem').forEach(function (ele) {
    //   itemsData.items.push({
    //     id: ele.querySelector('input[name=id]').value,
    //     quantity: 1,
    //   });
    // });

    $.ajax({
        type: "POST",
        dataType: "JSON",
        url: "/cart/add.js",
        data: itemsData,
        timeout: 5000,
        success: function () {
            // update cart attributes
            $.post(
                "/cart/update.js",
                attributesData,
                function (cartData) {
                    ajaxcart.updateCartItem(cartData);
                    target.disabled == false;
                    ajaxcart.openCart();
                },
                "json"
            );
            // let current_option = document.querySelector('input[value="' + chairData.get('id') + '"]');
            // gtag_report_conversion(parseInt(current_option.dataset.price) / 100);
        },
        error: function (err) {
            alert(err.responseJSON.description);
            target.disabled == false;
        },
        beforeSend: function () {
            target.disabled == true;
        },
    });
}

function bindFilterFormSubmit() {
    const form = document.getElementById("filter-form__bt37");
    const inputs = form.querySelectorAll("input, select");
    inputs.forEach((input) => {
        input.addEventListener("change", () => {
            form.submit();
        });
    });
}

function initFilterSidebar() {
    const openBtn = document.querySelector(".filter-open-btn");
    const sidebar = document.querySelector(".filter-container-for-mobile");
    const overlay = document.querySelector(".overlay-for-mobile-filter");
    const closeBtn = document.querySelector(".filter-close-btn");
    const body = document.body;

    function closeSidebar() {
        sidebar.style.right = "-78.33vw";
        overlay.style.display = "none";
        body.style.overflow = "auto";
    }

    function openSidebar() {
        sidebar.style.right = "0";
        overlay.style.display = "block";
        body.style.overflow = "hidden";
        window.scrollTo({
            top: 0,
            behavior: "smooth",
        });
    }

    openBtn?.addEventListener("click", openSidebar);
    closeBtn?.addEventListener("click", closeSidebar);
    overlay?.addEventListener("click", closeSidebar);
}

function initSelectFilterOptionToggle() {
    document.addEventListener("click", (event) => {
        if (event.target.closest(".filter_section_name__bt37")) {
            const parentSection = event.target.closest(".filter_section__bt37");
            const filterSelect = parentSection.querySelector(".other_filter_select__bt37");
            if (filterSelect) {
                filterSelect.classList.toggle("select-active");
            }
            const svgIcon = parentSection.querySelector("svg");
            if (filterSelect && svgIcon) {
                svgIcon.classList.toggle("rotated");
            }
        }
    });
}

function changeDisabledInputColor(params) {
    const disabledInputs = document.querySelectorAll("input:disabled");
    disabledInputs.forEach((input) => {
        const wrapperLabel = input.closest("label");
        if (wrapperLabel) {
            wrapperLabel.style.color = "#8c8c8c";
        }
    });
}

$(document).ready(function () {
    initFilterSidebar();
    initSelectFilterOptionToggle();
    bindFilterFormSubmit();
    initAddToCart();
    changeDisabledInputColor();
});
