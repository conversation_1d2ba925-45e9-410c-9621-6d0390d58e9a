<!-- banner -->
<div class="collection_header__bt37">
  <div class="collection_header-title__bt37">
    <p class="collection_header-text__bt37"><span>Boulies</span> Collections</p>
  </div>
  <div
    class="collection_header-banner__bt37"
    data-spbanner="{{ settings.promotion_image_sp }}"
    data-pcbanner="{{ settings.promotion_image_pc  | replace: '_x260', '_x350' }}"
    data-start="{{ settings.promotion_start_date }}"
    data-end="{{ settings.promotion_end_date }}"
  >
    <img
      id="collection_banner_image"
      style="display:none;"
      data-default-banner="{{ section.settings.banner_image.src | img_url: 'master' }}"
      src=""
      alt="Boulies Collections"
      crossOrigin="Anonymous"
    >
    <div class="home-saleBannerContent">
      <p class="home-saleBannerInfo" style="color:white;margin: 0;">
        <span class="bt5-sale-note">{{ settings.promotion_content }}</span>
        <span class="home-saleEndNotif" id="saleEndDate">ENDS IN <span id="saleCounting"></span></span>
      </p>
    </div>
  </div>
</div>

{% comment %} get url params {% endcomment %}
{%- capture contentForQuerystring -%}{{ content_for_header }}{%- endcapture -%}
{%- assign pageUrl = contentForQuerystring
  | split: '"pageurl":"'
  | last
  | split: '"'
  | first
  | split: '?'
  | last
  | replace: '%20', ' '
  | replace: '\u0026', '&'
-%}
{% assign params = pageUrl | split: '&' %}
{% assign availability_values = '' %}
{% comment %} get availability values {% endcomment %}
{% for param in params %}
  {% if param contains 'filter.v.availability' %}
    {% assign value = param | split: '=' | last %}
    {% assign availability_values = availability_values | append: value | append: ',' %}
  {% endif %}
{% endfor %}

{% assign availability_values = availability_values | strip | rstrip: ',' %}
{% assign availability_filters_array = availability_values | split: ',' %}
{% assign availability_filters_array = availability_filters_array | uniq %}

<div class="overlay-for-mobile-filter"></div>
{% comment %} filter form {% endcomment %}
<div class="filter-container">
  <form id="filter-form__bt37" class="filter-form">
    {%- for filter in collection.filters -%}
      {% comment %} Category  {% endcomment %}
      {%- if filter.label == 'Category' -%}
        <div class="filter-form-container category-filter-container">
          <div class="filter_section__bt37">
            <div class="filter_section_name__bt37 category-filter-section">
              {{ filter.label }}
              {% if filter.active_values.first %}
                : {{ filter.active_values.first.label }}
              {% else %}
                : All Chairs
              {% endif %}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                fill="none"
                version="1.1"
                width="32"
                height="32"
                viewBox="0 0 32 32"
              >
                <defs><clipPath id="master_svg0_47_7986"><rect x="0" y="0" width="32" height="32" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_47_7986)"><g><path d="M22.53336660308838,12.266666793823243C21.999966603088378,11.733333793823242,21.199966603088377,11.733333793823242,20.66666660308838,12.266666793823243C20.66666660308838,12.266666793823243,15.999996603088379,16.933336793823244,15.999996603088379,16.933336793823244C15.999996603088379,16.933336793823244,11.333336603088378,12.266666793823243,11.333336603088378,12.266666793823243C10.79999660308838,11.733333793823242,9.999999603088378,11.733333793823242,9.46666660308838,12.266666793823243C8.933333603088379,12.799999793823241,8.933333603088379,13.599996793823243,9.46666660308838,14.133336793823242C9.46666660308838,14.133336793823242,15.066666603088379,19.73333679382324,15.066666603088379,19.73333679382324C15.33333660308838,19.999996793823243,15.599996603088378,20.13333679382324,15.999996603088379,20.13333679382324C16.39999660308838,20.13333679382324,16.66666660308838,19.999996793823243,16.933336603088378,19.73333679382324C16.933336603088378,19.73333679382324,22.53336660308838,14.133336793823242,22.53336660308838,14.133336793823242C23.06666660308838,13.599996793823243,23.06666660308838,12.799999793823241,22.53336660308838,12.266666793823243C22.53336660308838,12.266666793823243,22.53336660308838,12.266666793823243,22.53336660308838,12.266666793823243Z" fill="#000000" fill-opacity="1"/></g></g>
              </svg>
            </div>
            <div class="filter_select__bt37">
              {% comment %} add all chairs option {% endcomment %}
              <div class="filter_option__bt37 category-filter-option">
                <label for="all-chairs" class="filter_option_label__bt37">
                  <input
                    type="radio"
                    name="filter.p.t.category"
                    value=""
                    id="all-chairs"
                    {% if filter.active_values.first %}
                    {% else %}
                      checked
                    {% endif %}
                  >
                  All Chairs
                </label>
              </div>
              {%- for filter_value in filter.values -%}
                <div class="filter_option__bt37 category-filter-option">
                  <label for="Filter-{{ filter.param_name }}-{{ forloop.index }}" class="filter_option_label__bt37">
                    <input
                      type="radio"
                      name="{{ filter_value.param_name }}"
                      value="{{ filter_value.value }}"
                      id="Filter-{{ filter.param_name }}-{{ forloop.index }}"
                      {% if filter_value.active -%}
                        checked
                      {%- endif %}
                      {% if filter_value.count == 0 and filter_value.active == false -%}
                        disabled
                      {%- endif %}
                    >
                    {{ filter_value.label }}
                  </label>
                </div>
              {%- endfor -%}
            </div>
          </div>
          <a class="filter-open-btn">FILTER</a>
        </div>
      {%- endif -%}
    {%- endfor -%}

    <div class="filter-container-for-mobile">
      <div class="filter-close-section-for-mobile">
        <div class="filter-close-text">FILTER</div>
        <a class="filter-close-btn">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            fill="none"
            version="1.1"
            width="13.999974250793457"
            height="13.999974250793457"
            viewBox="0 0 13.999974250793457 13.999974250793457"
          >
            <g><path d="M0.292892,12.2929C-0.0976307,12.6834,-0.0976307,13.3166,0.292892,13.7071C0.683417,14.0976,1.31658,14.0976,1.70711,13.7071C1.70711,13.7071,0.292892,12.2929,0.292892,12.2929C0.292892,12.2929,0.292892,12.2929,0.292892,12.2929ZM13.7071,1.70711C14.0976,1.31658,14.0976,0.683417,13.7071,0.292893C13.3166,-0.0976307,12.6834,-0.0976307,12.2929,0.292893C12.2929,0.292893,13.7071,1.70711,13.7071,1.70711C13.7071,1.70711,13.7071,1.70711,13.7071,1.70711ZM1.70711,0.292892C1.31658,-0.0976307,0.683417,-0.0976307,0.292892,0.292892C-0.0976307,0.683416,-0.0976307,1.31658,0.292892,1.70711C0.292892,1.70711,1.70711,0.292892,1.70711,0.292892C1.70711,0.292892,1.70711,0.292892,1.70711,0.292892ZM12.2929,13.7071C12.6834,14.0976,13.3166,14.0976,13.7071,13.7071C14.0976,13.3166,14.0976,12.6834,13.7071,12.2929C13.7071,12.2929,12.2929,13.7071,12.2929,13.7071C12.2929,13.7071,12.2929,13.7071,12.2929,13.7071ZM1.70711,13.7071C1.70711,13.7071,13.7071,1.70711,13.7071,1.70711C13.7071,1.70711,12.2929,0.292893,12.2929,0.292893C12.2929,0.292893,0.292892,12.2929,0.292892,12.2929C0.292892,12.2929,1.70711,13.7071,1.70711,13.7071C1.70711,13.7071,1.70711,13.7071,1.70711,13.7071ZM0.292892,1.70711C0.292892,1.70711,12.2929,13.7071,12.2929,13.7071C12.2929,13.7071,13.7071,12.2929,13.7071,12.2929C13.7071,12.2929,1.70711,0.292892,1.70711,0.292892C1.70711,0.292892,0.292892,1.70711,0.292892,1.70711C0.292892,1.70711,0.292892,1.70711,0.292892,1.70711Z" fill="#000000" fill-opacity="1"/></g>
          </svg>
        </a>
      </div>
      <hr class="filter-close-hr">
      <div class="filter-form-container other-filter-container">
        {%- for filter in collection.filters -%}
          {%- if filter.label != 'Category' -%}
            <div class="filter_section__bt37 other-filter-section">
              <div class="filter_section_name__bt37 other-filter-section-name">
                {{ filter.label }}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  fill="none"
                  version="1.1"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <defs><clipPath id="master_svg0_47_7983"><rect x="0" y="0" width="24" height="24" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_47_7983)"><g><path d="M16.899999713897706,9.199999618530274C16.499999713897704,8.799999618530274,15.899999713897705,8.799999618530274,15.499999713897704,9.199999618530274C15.499999713897704,9.199999618530274,11.999999713897704,12.699999618530274,11.999999713897704,12.699999618530274C11.999999713897704,12.699999618530274,8.499999713897704,9.199999618530274,8.499999713897704,9.199999618530274C8.099999713897706,8.799999618530274,7.499999713897705,8.799999618530274,7.099999713897705,9.199999618530274C6.699999713897705,9.599999618530273,6.699999713897705,10.199999618530274,7.099999713897705,10.599999618530273C7.099999713897705,10.599999618530273,11.299999713897705,14.799999618530274,11.299999713897705,14.799999618530274C11.499999713897704,14.999999618530273,11.699999713897705,15.099999618530273,11.999999713897704,15.099999618530273C12.299999713897705,15.099999618530273,12.499999713897704,14.999999618530273,12.699999713897705,14.799999618530274C12.699999713897705,14.799999618530274,16.899999713897706,10.599999618530273,16.899999713897706,10.599999618530273C17.299999713897705,10.199999618530274,17.299999713897705,9.599999618530273,16.899999713897706,9.199999618530274C16.899999713897706,9.199999618530274,16.899999713897706,9.199999618530274,16.899999713897706,9.199999618530274Z" fill="#000000" fill-opacity="1"/></g></g>
                </svg>
              </div>

              {%- case filter.type -%}
                {%- when 'boolean' -%}
                  <div class="filter_select__bt37 other_filter_select__bt37">
                    <div class="filter_option__bt37">
                      <label
                        for="Filter-{{ filter.param_name }}-{{ filter.true_value.value }}"
                        class="filter_option_label__bt37"
                      >
                        <input
                          type="checkbox"
                          name="{{ filter.param_name }}"
                          value="{{ filter.true_value.value }}"
                          id="Filter-{{ filter.param_name }}"
                          {% if filter.true_value.active -%}
                            checked
                          {%- endif %}
                          {% if filter.true_value.count == 0 and filter.true_value.active == false -%}
                            disabled
                          {%- endif -%}
                        >
                        {{- filter.true_value.label -}}
                      </label>
                    </div>

                    <div class="filter_option__bt37">
                      <label
                        for="Filter-{{ filter.param_name }}-{{ filter.false_value.value }}"
                        class="filter_option_label__bt37"
                      >
                        <input
                          type="checkbox"
                          name="{{ filter.param_name }}"
                          value="{{ filter.false_value.value }}"
                          id="Filter-{{ filter.param_name }}"
                          {% if filter.false_value.active -%}
                            checked
                          {%- endif %}
                          {% if filter.false_value.count == 0 and filter.false_value.active == false -%}
                            disabled
                          {%- endif %}
                        >
                        {{- filter.false_value.label -}}
                      </label>
                    </div>
                  </div>
                {%- when 'list' -%}
                  <div class="filter_select__bt37 other_filter_select__bt37">
                    {%- for filter_value in filter.values -%}
                      <div class="filter_option__bt37">
                        <label
                          for="Filter-{{ filter.param_name }}-{{ forloop.index }}"
                          class="filter_option_label__bt37"
                        >
                          <input
                            type="checkbox"
                            name="{{ filter_value.param_name }}"
                            value="{{ filter_value.value }}"
                            id="Filter-{{ filter.param_name }}-{{ forloop.index }}"
                            {% if filter_value.active -%}
                              checked
                            {%- endif %}
                            {% if filter_value.count == 0 and filter_value.active == false -%}
                              disabled
                            {%- endif %}
                          >
                          <span>
                            {%- case filter_value.display.type -%}
                              {%- when 'colors' -%}
                                {% liquid
                                  assign size_limit = filter_value.display.value.size | at_most: 4
                                  assign rotation = '0deg'
                                  if size_limit == 2
                                    assign rotation = '45deg'
                                  endif
                                  assign angle_increment = 360 | divided_by: size_limit
                                  assign angle = 0
                                %}
                                {%- capture conic_gradient -%}
                                {%- for color in filter_value.display.value limit: size_limit -%}
                                  {{ color }} {{ angle }}deg{%- assign angle = angle | plus: angle_increment %} {{ angle }}deg{%- unless forloop.last %}, {%- endunless -%}
                                {%- endfor -%}
                              {%- endcapture -%}
                                <span
                                  style="
                                    width: 25px;
                                    height: 25px;
                                    border-radius: 50%;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    background: conic-gradient({{ conic_gradient }}); transform: rotateZ({{ rotation }});
                                  "
                                ></span>
                              {%- when 'image' -%}
                                {{
                                  filter_value.display.value
                                  | image_url: width: 25
                                  | image_tag: alt: filter_value.display.value.alt
                                }}
                              {%- else -%}
                                <span></span>
                            {%- endcase -%}
                          </span>
                          {{ filter_value.label }}
                        </label>
                      </div>
                    {%- endfor -%}
                    {% comment %} Pre order {% endcomment %}
                    {% if filter.label == 'Stock' %}
                      <div class="filter_option__bt37">
                        <label
                          for="Filter-{{ filter.param_name }}-pre-order"
                          class="filter_option_label__bt37"
                        >
                          <input
                            type="checkbox"
                            name="{{ filter.param_name }}"
                            value="2"
                            id="Filter-{{ filter.param_name }}-pre-order"
                            {% for value in availability_filters_array %}
                              {% if value == '2' %}
                                checked
                              {% endif %}
                            {% endfor %}
                          >
                          <span></span>
                          Pre order
                        </label>
                      </div>
                    {% endif %}
                  </div>
                {%- when 'price_range' -%}
              {%- endcase -%}
              <hr class="filter-section-hr">
            </div>
          {%- endif -%}
        {%- endfor -%}

        {% comment %} sort by {% endcomment %}
        <div class="filter_section__bt37 other-filter-section sort-by-filter-section">
          <div class="filter_section_name__bt37 other-filter-section-name">
            Sort by
            <span id="sort-by-label" class="sort-by-label" style="text-transform: none;">
              {% if collection.sort_by != '' %}
                :&nbsp;
                {% if collection.sort_by == 'best-selling' %}
                  Popular
                {% elsif collection.sort_by == 'price-ascending' %}
                  Price: low-high
                {% elsif collection.sort_by == 'price-descending' %}
                  Price: high-low
                {% endif %}
              {% endif %}
            </span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              fill="none"
              version="1.1"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              {% if collection.sort_by != '' %}
                class="rotated"
              {% endif %}
            >
              <defs><clipPath id="master_svg0_47_7983"><rect x="0" y="0" width="24" height="24" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_47_7983)"><g><path d="M16.899999713897706,9.199999618530274C16.499999713897704,8.799999618530274,15.899999713897705,8.799999618530274,15.499999713897704,9.199999618530274C15.499999713897704,9.199999618530274,11.999999713897704,12.699999618530274,11.999999713897704,12.699999618530274C11.999999713897704,12.699999618530274,8.499999713897704,9.199999618530274,8.499999713897704,9.199999618530274C8.099999713897706,8.799999618530274,7.499999713897705,8.799999618530274,7.099999713897705,9.199999618530274C6.699999713897705,9.599999618530273,6.699999713897705,10.199999618530274,7.099999713897705,10.599999618530273C7.099999713897705,10.599999618530273,11.299999713897705,14.799999618530274,11.299999713897705,14.799999618530274C11.499999713897704,14.999999618530273,11.699999713897705,15.099999618530273,11.999999713897704,15.099999618530273C12.299999713897705,15.099999618530273,12.499999713897704,14.999999618530273,12.699999713897705,14.799999618530274C12.699999713897705,14.799999618530274,16.899999713897706,10.599999618530273,16.899999713897706,10.599999618530273C17.299999713897705,10.199999618530274,17.299999713897705,9.599999618530273,16.899999713897706,9.199999618530274C16.899999713897706,9.199999618530274,16.899999713897706,9.199999618530274,16.899999713897706,9.199999618530274Z" fill="#000000" fill-opacity="1"/></g></g>
            </svg>
          </div>
          <div class="filter_select__bt37 other_filter_select__bt37 {% if collection.sort_by !=''%}select-active{% endif %}">
            <div class="filter_option__bt37">
              <label for="sort_by_best_selling" class="filter_option_label__bt37">
                <input
                  type="radio"
                  id="sort_by_best_selling"
                  name="sort_by"
                  value="best-selling"
                  {% if collection.sort_by == 'best-selling' %}
                    checked
                  {% endif %}
                >
                Popular
              </label>
            </div>
            <div class="filter_option__bt37">
              <label for="sort_by_price_ascending" class="filter_option_label__bt37">
                <input
                  type="radio"
                  id="sort_by_price_ascending"
                  name="sort_by"
                  value="price-ascending"
                  {% if collection.sort_by == 'price-ascending' %}
                    checked
                  {% endif %}
                >
                Price: low-high
              </label>
            </div>
            <div class="filter_option__bt37">
              <label for="sort_by_price_descending" class="filter_option_label__bt37">
                <input
                  type="radio"
                  id="sort_by_price_descending"
                  name="sort_by"
                  value="price-descending"
                  {% if collection.sort_by == 'price-descending' %}
                    checked
                  {% endif %}
                >
                Price: high-low
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="selected-filters">
        {%- for filter in collection.filters -%}
          {%- if filter.label != 'Category' -%}
            {%- for filter_value in filter.active_values -%}
              <a class="selected-filter" href="{{ filter_value.url_to_remove }}">
                {{ filter_value.label }}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  fill="none"
                  version="1.1"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                >
                  <defs><clipPath id="master_svg0_47_7954"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_47_7954)"><g><path d="M3.4343140476837157,3.4343140476837157C3.7467340476837157,3.1218952476837156,4.253260047683716,3.1218952476837156,4.565690047683716,3.4343140476837157C4.565690047683716,3.4343140476837157,8.000000047683717,6.868630047683716,8.000000047683717,6.868630047683716C8.000000047683717,6.868630047683716,11.434320047683716,3.4343140476837157,11.434320047683716,3.4343140476837157C11.746720047683716,3.1218952476837156,12.253280047683717,3.1218952476837156,12.565680047683715,3.4343140476837157C12.878080047683715,3.7467340476837157,12.878080047683715,4.253260047683716,12.565680047683715,4.565690047683716C12.565680047683715,4.565690047683716,9.131370047683717,8.000000047683717,9.131370047683717,8.000000047683717C9.131370047683717,8.000000047683717,12.565680047683715,11.434320047683716,12.565680047683715,11.434320047683716C12.878080047683715,11.746720047683716,12.878080047683715,12.253280047683717,12.565680047683715,12.565680047683715C12.253280047683717,12.878080047683715,11.746720047683716,12.878080047683715,11.434320047683716,12.565680047683715C11.434320047683716,12.565680047683715,8.000000047683717,9.131370047683717,8.000000047683717,9.131370047683717C8.000000047683717,9.131370047683717,4.565690047683716,12.565680047683715,4.565690047683716,12.565680047683715C4.253260047683716,12.878080047683715,3.7467340476837157,12.878080047683715,3.4343140476837157,12.565680047683715C3.1218952476837156,12.253280047683717,3.1218952476837156,11.746720047683716,3.4343140476837157,11.434320047683716C3.4343140476837157,11.434320047683716,6.868630047683716,8.000000047683717,6.868630047683716,8.000000047683717C6.868630047683716,8.000000047683717,3.4343140476837157,4.565690047683716,3.4343140476837157,4.565690047683716C3.1218952476837156,4.253260047683716,3.1218952476837156,3.7467340476837157,3.4343140476837157,3.4343140476837157C3.4343140476837157,3.4343140476837157,3.4343140476837157,3.4343140476837157,3.4343140476837157,3.4343140476837157Z" fill-rule="evenodd" fill="#000000" fill-opacity="1"/></g></g>
                </svg>
              </a>
            {%- endfor -%}
          {%- endif -%}
        {%- endfor -%}
        {% comment %}
          add pre order remove tag
        {% endcomment %}
        {% if availability_filters_array.size > 0 %}
          {% for value in availability_filters_array %}
            {% if value == '2' %}
              {% comment %}
                remove pre order condition
              {% endcomment %}
              {% assign param_to_remove = 'filter.v.availability=2' %}
              {%- assign modified_url = pageUrl | replace: param_to_remove, '' -%}
              {%- comment %}
                if start with &, remove first char
              {%- endcomment %}
              {% assign first_char = modified_url | slice: 0, 1 %}
              {% if first_char == '&' %}
                {% assign modified_url = modified_url | slice: 1 %}
              {% endif %}
              {%- comment %}
                if end with &, remove last char
              {%- endcomment %}
              {% assign last_char = modified_url | slice: modified_url | size | minus: 1, 1 %}
              {% if last_char == '&' %}
                {% assign modified_url = modified_url | slice: 0, modified_url | size | minus: 1 %}
              {% endif %}

              <a class="selected-filter" href="{{ 'all?' | append: modified_url }}">
                Pre order
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  fill="none"
                  version="1.1"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                >
                  <defs><clipPath id="master_svg0_47_7954"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_47_7954)"><g><path d="M3.4343140476837157,3.4343140476837157C3.7467340476837157,3.1218952476837156,4.253260047683716,3.1218952476837156,4.565690047683716,3.4343140476837157C4.565690047683716,3.4343140476837157,8.000000047683717,6.868630047683716,8.000000047683717,6.868630047683716C8.000000047683717,6.868630047683716,11.434320047683716,3.4343140476837157,11.434320047683716,3.4343140476837157C11.746720047683716,3.1218952476837156,12.253280047683717,3.1218952476837156,12.565680047683715,3.4343140476837157C12.878080047683715,3.7467340476837157,12.878080047683715,4.253260047683716,12.565680047683715,4.565690047683716C12.565680047683715,4.565690047683716,9.131370047683717,8.000000047683717,9.131370047683717,8.000000047683717C9.131370047683717,8.000000047683717,12.565680047683715,11.434320047683716,12.565680047683715,11.434320047683716C12.878080047683715,11.746720047683716,12.878080047683715,12.253280047683717,12.565680047683715,12.565680047683715C12.253280047683717,12.878080047683715,11.746720047683716,12.878080047683715,11.434320047683716,12.565680047683715C11.434320047683716,12.565680047683715,8.000000047683717,9.131370047683717,8.000000047683717,9.131370047683717C8.000000047683717,9.131370047683717,4.565690047683716,12.565680047683715,4.565690047683716,12.565680047683715C4.253260047683716,12.878080047683715,3.7467340476837157,12.878080047683715,3.4343140476837157,12.565680047683715C3.1218952476837156,12.253280047683717,3.1218952476837156,11.746720047683716,3.4343140476837157,11.434320047683716C3.4343140476837157,11.434320047683716,6.868630047683716,8.000000047683717,6.868630047683716,8.000000047683717C6.868630047683716,8.000000047683717,3.4343140476837157,4.565690047683716,3.4343140476837157,4.565690047683716C3.1218952476837156,4.253260047683716,3.1218952476837156,3.7467340476837157,3.4343140476837157,3.4343140476837157C3.4343140476837157,3.4343140476837157,3.4343140476837157,3.4343140476837157,3.4343140476837157,3.4343140476837157Z" fill-rule="evenodd" fill="#000000" fill-opacity="1"/></g></g>
                </svg>
              </a>
            {% endif %}
          {% endfor %}
        {% endif %}
        <a class="selected-filters-clear" href="{{ collection.url }}?sort_by=best-selling">Clear</a>
      </div>
    </div>
  </form>
</div>

{% comment %} get filter values from collection.filters {% endcomment %}
{%- assign color_filters = '' -%}
{%- assign upholstery_filters = '' -%}
{%- assign seat_type_filters = '' -%}

{%- for filter in collection.filters -%}
  {%- for filter_value in filter.active_values -%}
    {% assign key = filter_value.param_name %}
    {% assign parts = key | split: '.' %}
    {% assign param_name = parts | last %}
    {% assign value = filter_value.value %}

    {%- if param_name == 'color' -%}
      {%- assign color_filters = color_filters | append: value | append: ',' -%}
    {%- elsif param_name == 'upholstery' -%}
      {%- assign upholstery_filters = upholstery_filters | append: value | append: ',' -%}
    {%- elsif param_name == 'seat_type' -%}
      {%- assign seat_type_filters = seat_type_filters | append: value | append: ',' -%}
    {%- endif -%}
  {%- endfor -%}
{%- endfor -%}

{%- assign color_filters = color_filters | strip | rstrip: ',' -%}
{% assign selected_colors_array = color_filters | split: ',' %}

{%- assign upholstery_filters = upholstery_filters | strip | rstrip: ',' -%}
{% assign selected_upholstery_array = upholstery_filters | split: ',' %}

{%- assign seat_type_filters = seat_type_filters | strip | rstrip: ',' -%}
{% assign selected_seat_type_array = seat_type_filters | split: ',' %}

{% assign sort_by = collection.sort_by %}

{% comment %} product list {% endcomment %}
<div class="products_container">
  {% for product in collection.products %}
    {% if product.type == 'Chair' %}
      {% comment %} variant sort by price {% endcomment %}
      {% assign variant_prices = '' %}

      {% comment %} sort by promotion_price not price {% endcomment %}
      {% for variant in product.variants %}
        {% assign promotion_price = variant.price %}
        {% assign promotion_start = variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.start
          | date: '%s'
        %}
        {% assign promotion_end = variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.end
          | date: '%s'
        %}
        {% assign now = 'now' | date: '%s' %}
        {% if promotion_start and promotion_end and now > promotion_start and now < promotion_end %}
          {% assign promotion_price = variant.price
            | minus: variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.discount
          %}
        {% endif %}

        {% assign variant_prices = variant_prices
          | append: promotion_price
          | append: '__'
          | append: variant.id
          | append: ','
        %}
      {% endfor %}

      {% assign variant_prices_array = variant_prices | split: ',' %}
      {% comment %} <div>variant_prices_array {{ variant_prices_array }}</div> {% endcomment %}
      {% assign sorted_variant_prices = variant_prices_array | sort %}
      {% comment %} <div>sorted_variant_prices {{ sorted_variant_prices }}</div> {% endcomment %}

      {% if sort_by == 'price-ascending' %}
        {% assign sorted_variant_prices = sorted_variant_prices %}
      {% endif %}
      {% if sort_by == 'price-descending' %}
        {% assign sorted_variant_prices = sorted_variant_prices | reverse %}
      {% endif %}
      {% if sort_by == 'best-selling' %}
        {% assign sorted_variant_prices = product.variants %}
      {% endif %}

      {% for item in sorted_variant_prices %}
        {% if sort_by != 'best-selling' %}
          {% assign parts = item | split: '__' %}
          {% comment %} {% assign final_price = parts[0] %} {% endcomment %}
          {% assign variant_id = parts[1] | plus: 0 %}
          {% assign variant = product.variants | where: 'id', variant_id | first %}
        {% else %}
          {% comment %} best-selling no sort {% endcomment %}
          {% assign variant = item %}
        {% endif %}

        {% unless variant.metafields.extra_setting.hidden %}
          {% assign variant_upholstery = variant.options[1] %}
          {% assign variant_color = variant.options[2] %}
          {% assign pre_order_status = variant.metafields.Metadata[request.locale.endonym_name].pre_order.status %}
          {% comment %} custom filter {% endcomment %}
          {% assign variant_upholstery_for_filter = variant.metafields.custom_filter.upholstery %}
          {% assign variant_color_for_filter = variant.metafields.custom_filter.color %}
          {% assign variant_seat_type_for_filter = variant.metafields.custom_filter.seat_type %}
          {% comment %} manual filter: color and upholstery {% endcomment %}
          {%- assign show_variant = false -%}
          {%- assign color_match = false -%}
          {%- assign upholstery_match = false -%}
          {%- assign available_match = false -%}
          {%- assign seat_type_match = false -%}

          {% comment %} color or {% endcomment %}
          {% if selected_colors_array.size > 0 %}
            {% if selected_colors_array contains variant_color_for_filter %}
              {% assign color_match = true %}
            {% endif %}
          {% else %}
            {% assign color_match = true %}
          {% endif %}

          {% comment %} upholstery or {% endcomment %}
          {%- if selected_upholstery_array.size > 0 -%}
            {% if selected_upholstery_array contains variant_upholstery_for_filter %}
              {% assign upholstery_match = true %}
            {% endif %}
          {%- else -%}
            {% assign upholstery_match = true %}
          {%- endif -%}

          {% comment %} seat type or {% endcomment %}
          {%- if selected_seat_type_array.size > 0 -%}
            {% if selected_seat_type_array contains variant_seat_type_for_filter %}
              {% assign seat_type_match = true %}
            {% endif %}
          {%- else -%}
            {% assign seat_type_match = true %}
          {%- endif -%}

          {% comment %} available or{% endcomment %}
          {% if variant.available %}
            {% if pre_order_status %}
              {% assign variant_available = '2' %}
            {% else %}
              {% assign variant_available = '1' %}
            {% endif %}
          {% else %}
            {% assign variant_available = '0' %}
          {% endif %}
          {%- if availability_filters_array.size > 0 -%}
            {%- for value in availability_filters_array -%}
              {% if value == variant_available %}
                {% assign available_match = true %}
                {% break %}
              {% endif %}
            {%- endfor -%}
          {%- else -%}
            {% assign available_match = true %}
          {%- endif -%}

          {% comment %} and {% endcomment %}
          {%- if color_match and upholstery_match and available_match and seat_type_match %}
            {% assign show_variant = true %}
          {% endif %}
          {% comment %}  {% endcomment %}
          {% assign now = 'now' | date: '%s' %}
          {% assign promotion_start = variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.start
            | date: '%s'
          %}
          {% assign promotion_end = variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.end
            | date: '%s'
          %}
          {% assign on_sale_label = '' %}
          {% assign discount_note = '' %}
          {% assign promotion_price = '' %}
          {% assign product_price = variant.price | money %}

          {% if promotion_start and promotion_end and now > promotion_start and now < promotion_end %}
            {% assign promotion_price = variant.price
              | minus: variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.discount
            %}
            {% assign on_sale_label = 'On Sale' %}
            {% assign discount_note = 'Sale code applied price.' %}
          {% else %}
            {% assign discount_note = '&nbsp;' %}
          {% endif %}

          {%- if show_variant -%}
            <div class="product_container__bt37">
              <a href="{{ variant.url }}" class="">
                <div class="product_img_wrapper">
                  <img
                    src="{{ variant.image | image_url: width: 372 }}"
                    class="product_image"
                    width="200"
                    height="auto"
                  >
                  <div class="product_labels__bt37">
                    {% if on_sale_label != '' %}
                      <span class="product_on_sale_label__bt37">{{ on_sale_label }}</span>
                    {% endif %}
                    {% if variant.available %}
                      {% if pre_order_status %}
                        <span class="product_pre_order_label__bt37">Pre order</span>
                      {% else %}
                        <span class="product_in_stock_label__bt37">In stock</span>
                      {% endif %}
                    {% else %}
                      <span class="product_out_stock_label__bt37">Out of stock</span>
                    {% endif %}
                  </div>
                </div>
              </a>
              <div class="product_content__bt37">
                <div class="product_title__bt37">{{ product.title }}</div>
                <div class="product_variant__bt37">
                  {{ variant.options[0] }}
                  &nbsp;-&nbsp;{{ variant_upholstery }} &nbsp;/&nbsp;{{ variant_color }}
                </div>
                <div class="price-section__bt37">
                  <div class="price-container__bt37">
                    <div class="price-info__bt37">
                      {% if on_sale_label != '' %}
                        <span class="current-price__bt37">{{ promotion_price | money }}</span>
                        <span class="old-price__bt37">{{ product_price }}</span>
                      {% else %}
                        <span class="current-price__bt37">{{ product_price }}</span>
                      {% endif %}
                    </div>
                    {% if discount_note != '' %}
                      <div class="discount-note__bt37">{{ discount_note }}</div>
                    {% endif %}
                  </div>
                  <div class="add-to-cart-section__bt37">
                    <form action="/cart/add" method="post" enctype="multipart/form-data">
                      <input type="hidden" name="id" value="{{ variant.id }}">
                      <input type="hidden" name="quantity" value="1">
                      <input
                        type="hidden"
                        id="itemProperties_preOrderDescription"
                        name="properties[Estimated_to_ship_out]"
                        value="{{ variant.metafields.Metadata[request.locale.endonym_name].pre_order.waves.last.ets }}"
                        {% unless pre_order_status %}
                          disabled
                        {% endunless %}
                      >
                      <input
                        type="hidden"
                        id="itemProperties_warranty"
                        name="properties[warranty]"
                        value="{{ variant.metafields.Metadata[request.locale.endonym_name].warranty }}"
                      >
                      <input
                        type="hidden"
                        id="itemProperties_accessories"
                        name="properties[accessories]"
                        value="{{ variant.metafields.Metadata[request.locale.endonym_name].accessories }}"
                      >
                      <input
                        type="hidden"
                        id="cart_discount_code"
                        name="attributes[cart_discount_code]"
                        value="{{ variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.code }}"
                      >
                      <input
                        type="hidden"
                        id="cart_discount_amount"
                        name="attributes[cart_discount_amount]"
                        value="{{ variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.discount }}"
                      >
                      <button
                        class="add-to-cart-btn__bt37"
                        type="submit"
                        id="add-to-cart-btn__bt37"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          version="1.1"
                          viewBox="0 0 32 32"
                        >
                          <defs>
                            <style>
                              .st0 {
                                fill: none;
                              }

                              .st1 {
                                fill-rule: evenodd;
                              }

                              .st2 {
                                fill: #f0f0f0;
                              }

                              .st3 {
                                clip-path: url(#clippath);
                              }
                            </style>
                            <clipPath id="clippath">
                              <rect class="st0" x="4" y="4" width="24" height="24"/>
                            </clipPath>
                          </defs>
                          <circle class="st2" cx="16" cy="16" r="16"/>
                          <g class="st3">
                            <path class="st1" d="M16,10c.7,0,1.2.5,1.2,1.2v3.6h3.6c.7,0,1.2.5,1.2,1.2s-.5,1.2-1.2,1.2h-3.6v3.6c0,.7-.5,1.2-1.2,1.2s-1.2-.5-1.2-1.2v-3.6h-3.6c-.7,0-1.2-.5-1.2-1.2s.5-1.2,1.2-1.2h3.6v-3.6c0-.7.5-1.2,1.2-1.2h0Z"/>
                          </g>
                        </svg>
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          {%- endif -%}
        {% endunless %}
      {% endfor %}
    {% endif %}
  {% endfor %}
</div>

{% schema %}
{
  "name": "Collection - all prodcuts",
  "settings": [
    {
      "id": "banner_image",
      "type": "image_picker",
      "label": "Banner Image"
    }
  ],
  "presets": []
}
{% endschema %}
