var collectionGalleryList = $("#bt32-csb_pswpContainer").slick({
    slidesToShow: 3,
    slidesToScroll: 1,
    asNavFor: "#bt32-csb_mainImage",
    dots: false,
    arrows: false,
    centerMode: true,
    focusOnSelect: true,
    vertical: false,
    verticalSwiping: false,
    speed: 500,
});

var collectionGallery = $("#bt32-csb_mainImage").slick({
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    asNavFor: "#bt32-csb_pswpContainer",
    speed: 500,
});

var dynamicGallery = lightGallery(document.body, {
    licenseKey: "DA7E83CA-6E474542-97592C9F-8A8DFF9D",
    dynamic: true,
    controls: false,
    download: false,
    plugins: [lgZoom],
    dynamicEl: [],
});

// Addon Notice Modal
const noticeModal = document.getElementById("addonNoticeModal");
const closeNoticeModalBtn = document.getElementById("addonNoticeModalCloseBtn");
const addonNoticeModalCancelBtn = document.getElementById("addonNoticeModalCancelBtn");
function openNoticeModal(imageUrl, text) {
    document.getElementById("addonNoticeModalImage").src = imageUrl;
    document.getElementById("addonNoticeModalText").innerHTML = text;
    noticeModal.classList.add("show");
    document.body.style.overflow = "hidden";
}
function closeNoticeModal() {
    noticeModal.classList.remove("show");
    document.body.style.overflow = "";
    // clone confirm button, replace old one, clear all events
    let confirmBtn = document.getElementById("addonNoticeModalConfirmBtn");
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    confirmBtn = document.getElementById("addonNoticeModalConfirmBtn");
}
closeNoticeModalBtn.addEventListener("click", closeNoticeModal);
addonNoticeModalCancelBtn.addEventListener("click", closeNoticeModal);
// close modal when click outside
// noticeModal.addEventListener("click", (e) => {
//     if (e.target === noticeModal) {
//         closeNoticeModal();
//     }
// });

// Addon detail Modal
const detailModal = document.getElementById("addonDetailModal");
const closeDetailModalBtn = document.getElementById("addonDetailModalCloseBtn");
function openDetailModal(imageUrl, title, price, text, url) {
    if (document.getElementById("addonDetailModalImage")) {
        document.getElementById("addonDetailModalImage").src = imageUrl;
    }
    if (document.getElementById("addonDetailModalBodyTitle")) {
        document.getElementById("addonDetailModalBodyTitle").innerHTML = title;
    }
    if (document.getElementById("addonDetailModalBodyPrice")) {
        document.getElementById("addonDetailModalBodyPrice").innerHTML = price;
    }
    if (document.getElementById("addonDetailModalBodyDescription")) {
        document.getElementById("addonDetailModalBodyDescription").innerHTML = text;
    }
    if (document.getElementById("addonDetailModalLearnMoreBtn")) {
        document.getElementById("addonDetailModalLearnMoreBtn").href = url;
    }
    detailModal.classList.add("show");
    document.body.style.overflow = "hidden";
}
function closeDetailModal() {
    detailModal.classList.remove("show");
    document.body.style.overflow = "";
}
closeDetailModalBtn.addEventListener("click", closeDetailModal);
detailModal.addEventListener("click", (e) => {
    if (e.target === detailModal) {
        closeDetailModal();
    }
});

// addon data from liquid
const addonData = window.addonData;
// console.log("addonData:", addonData);

/* ---  product functions --- */
var productInter = {
    addToCart: function (target) {
        if (target.disabled == true) return false;
        let formData = new FormData(document.querySelector('form[action="/cart/add"]'));

        // create main item data
        let itemsData = {
            items: [
                {
                    id: formData.get("id"),
                    quantity: formData.get("quantity"),
                    properties: {
                        warranty: formData.get("properties[warranty]") || "",
                        accessories: formData.get("properties[accessories]") || "",
                    },
                },
            ],
        };

        if (formData.get("properties[Estimated_to_ship_out]")) {
            itemsData.items[0].properties["Estimated_to_ship_out"] = formData.get("properties[Estimated_to_ship_out]");
        }

        // set cart discount attributes
        let attributesData = {
            attributes: {
                cart_discount_code: "",
                cart_discount_amount: "",
            },
        };
        if (document.getElementById("bt34-mainPromotion").style["display"] == "block") {
            attributesData.attributes["cart_discount_code"] = formData.get("attributes[cart_discount_code]");
            attributesData.attributes["cart_discount_amount"] = formData.get("attributes[cart_discount_amount]");
        }

        // get addon item data
        document.querySelectorAll(".bt32-addonItem").forEach(function (element) {
            // variant id
            const variantId = element.querySelector(".bt32-colorInput:checked").value;
            // quantity
            const quantity = element.querySelector(".bt32-addonQty").innerText;
            itemsData.items.push({
                id: variantId,
                quantity: quantity,
            });
        });
        // console.log("itemsData:", itemsData);

        $.ajax({
            type: "POST",
            dataType: "JSON",
            url: "/cart/add.js",
            data: itemsData,
            timeout: 5000,
            success: function () {
                // update cart attributes
                $.post(
                    "/cart/update.js",
                    attributesData,
                    function (cartData) {
                        ajaxcart.updateCartItem(cartData);
                        target.classList.remove("bt20-csb_addingItem");
                        target.disabled == false;
                        ajaxcart.openCart();
                    },
                    "json"
                );
                // rdt('track', 'AddToCart');
                // var current_option = document.querySelector('input[value="' + formData.get("id") + '"]');
            },
            error: function (err) {
                alert(err.responseJSON.description);
                target.classList.remove("bt20-csb_addingItem");
                target.disabled == false;
            },
            beforeSend: function () {
                target.classList.add("bt20-csb_addingItem");
                target.disabled == true;
            },
        });
    },
    updatePreOrderInfo: function (data, quantity) {
        var preOrderInfoWrap = document.querySelector(".bt20-csb_preOrderWrap");
        var tempDOM = "";
        for (var i = 0; i < data.length - 1; i++) {
            tempDOM +=
                '<div class="bt20-csb_preOrderWaves"><div class="bt20-csb_preOrderInfo"><span class="bt20-csb_wave">' +
                (i + 1) +
                '</span><span class="bt20-csb_etsDate">' +
                data[i].ets +
                "</span></div></div>";
        }
        tempDOM +=
            '<div class="bt20-csb_preOrderWaves bt20-csb_currentWave"><div class="bt20-csb_preOrderInfo"><span class="bt20-csb_wave">' +
            data.length +
            '</span><span class="bt20-csb_etsDate">' +
            data[data.length - 1].ets +
            '</span></div><div class="bt20-csb_preOrderedStatus"><div class="bt20-csb_preOrderedDone" id="bt36-csb_preOrderProgress"></div></div></div>';
        preOrderInfoWrap.innerHTML = tempDOM;
        var orderStatus = Math.round(((data[data.length - 1].total_number - parseInt(quantity)) / data[data.length - 1].total_number) * 100) + "%";
        setTimeout(function () {
            document.getElementById("bt36-csb_preOrderProgress").innerHTML = orderStatus;
            document.getElementById("bt36-csb_preOrderProgress").style.width = orderStatus;
        }, 1000);
    },
    changeToThisOption: function (current_variant, stopPopstate, stopKlarnaReflash) {
        var cart = document.querySelector(".bt20-csb_cart"),
            notifymeContainer = document.querySelector(".bt32-notifymeContainer"),
            cartBtn = document.getElementById("bt20-csb_cartBtn"),
            inventory = document.querySelector(".bt20-csb_inventory");

        //update product gallery and gallery list
        var slickNumbers = collectionGallery.slick("getSlick").slideCount;
        for (var i = 0; i < slickNumbers; i++) {
            collectionGallery.slick("slickRemove", 0);
            collectionGalleryList.slick("slickRemove", 0);
        }
        var this_option_images = document.querySelectorAll(".image_storage_" + current_variant.id);
        for (var i = 0; i < this_option_images.length; i++) {
            var resizeImage = Boulies.imageResize(this_option_images[i].value, 900, "heightOnly");
            collectionGallery.slick(
                "slickAdd",
                '<a class="pswp_image" href="' +
                    this_option_images[i].value +
                    '" data-lg-size="600-900" target="_blank"><img class="bt32-collectionGalleryItem" src="' +
                    resizeImage +
                    '"></a>'
            );
            collectionGalleryList.slick("slickAdd", '<div><img src="' + resizeImage + '"></div>');
        }

        // update url history
        if (!stopPopstate) {
            var current_search = Boulies.getUrlkey() ? Boulies.getUrlkey() : {},
                _search = "?";
            current_search["variant"] = current_variant.value;
            for (var i in current_search) {
                _search += i + "=" + current_search[i] + "&";
            }
            history.pushState({}, "", _search.substr(0, _search.length - 1));
        }

        if (current_variant.dataset.variantMetadata == "null") return alert("Product Metadata Not Set!");

        var current_variant_metadata = JSON.parse(current_variant.dataset.variantMetadata);
        // update properties
        document.getElementById("itemProperties_warranty").value = current_variant_metadata?.warranty;
        document.getElementById("itemProperties_accessories").value = current_variant_metadata?.accessories;
        if (current_variant_metadata?.pre_order?.status == true) {
            document.getElementById("itemProperties_preOrderDescription").disabled = false;
            document.getElementById("itemProperties_preOrderDescription").value =
                current_variant_metadata.pre_order.waves[current_variant_metadata.pre_order.waves.length - 1].ets;
        } else {
            document.getElementById("itemProperties_preOrderDescription").disabled = true;
        }

        // Low inventory notifycation
        var _lin = document.getElementById("lowInventoryNotify");
        if (parseInt(current_variant.dataset.quantity) > parseInt(_lin.dataset.linTrigger) || _lin.dataset.lin == "disable") {
            _lin.innerHTML = "In stock";
        } else {
            if (_lin.dataset.lin == "text") {
                _lin.innerHTML = "Limited stock available";
            } else {
                _lin.innerHTML = "Only <b>" + current_variant.dataset.quantity + "</b> in stock";
            }
        }

        // update button
        if (current_variant.dataset.available == "true") {
            cart.style.display = "block";
            notifymeContainer.style.display = "none";
            if (current_variant_metadata?.pre_order?.status == true) {
                cart.classList.add("bt20-csb_proOrdering");
                if (current_variant.dataset.available == "true") {
                    cartBtn.classList.remove("bt20-csb_activeCartBtn", "bt20-csb_addingItem", "bt32-csb_preorderClosed");
                    cartBtn.classList.add("bt20-csb_preorderCartBtn");
                    cartBtn.disabled = false;
                    inventory.classList.remove("bt20-csb_inStock", "bt20-csb_outOfStock");
                } else {
                    cartBtn.classList.remove("bt20-csb_addingItem", "bt20-csb_activeCartBtn");
                    cartBtn.classList.add("bt20-csb_preorderCartBtn", "bt32-csb_preorderClosed");
                    cartBtn.disabled = true;
                    inventory.classList.remove("bt20-csb_inStock");
                    inventory.classList.add("bt20-csb_outOfStock");
                }
                productInter.updatePreOrderInfo(current_variant_metadata?.pre_order?.waves, current_variant.dataset.quantity);
            } else {
                cartBtn.classList.remove("bt20-csb_preorderCartBtn", "bt20-csb_addingItem", "bt32-csb_preorderClosed");
                cartBtn.classList.add("bt20-csb_activeCartBtn");
                cartBtn.disabled = false;
                inventory.classList.remove("bt20-csb_outOfStock");
                inventory.classList.add("bt20-csb_inStock");
                cart.classList.remove("bt20-csb_proOrdering");
            }
        } else {
            var notyfymeData = document.getElementById("bt32-notifyme_data");
            inventory.classList.remove("bt20-csb_inStock");
            inventory.classList.add("bt20-csb_outOfStock");
            cart.style.display = "none";
            notyfymeData.dataset.region = Boulies.setting.region;
            notyfymeData.dataset.itemName = current_variant.dataset.productSeries;
            notyfymeData.dataset.itemColor = current_variant.dataset.options;
            notyfymeData.dataset.itemSku = current_variant.id;
            notifymeContainer.style.display = "block";
        }

        // update addon items
        // ["handle1,handle2,handle3"]=>["handle1","handle2","handle3"]
        productInter.getAddonItems(current_variant_metadata.addon[0].split(","));

        // update promotion and set promotion counting
        var saleWrap = document.getElementById("bt34-mainPromotion");
        var _startDate_sub = new Date(current_variant_metadata?.Sub_Promotion?.start).getTime();
        var _endDate_sub = new Date(current_variant_metadata?.Sub_Promotion?.end).getTime();
        var _localDate = new Date().getTime();

        if (current_variant.dataset.saleStatus == "true") {
            document.getElementById("bt20-saleName").innerHTML = current_variant_metadata?.Main_Promotion?.name;
            document.getElementById("bt20-saleCode").innerHTML = current_variant_metadata?.Main_Promotion?.code;
            document.getElementById("bps_saleBannerBg").style["background-image"] = "url(" + current_variant_metadata?.Main_Promotion?.image + ")";
            document.getElementById("bt20-saleDiscount").innerHTML = current_variant_metadata?.Main_Promotion?.discount / 100;
            // update cart discount info
            document.getElementById("cart_discount_code").value = current_variant_metadata?.Main_Promotion?.code;
            document.getElementById("cart_discount_amount").value = current_variant_metadata?.Main_Promotion?.discount / 100;

            saleWrap.style.display = "block";

            // // update price when on sale
            // let sale_price = parseInt(current_variant.dataset.price) - current_variant_metadata?.Main_Promotion?.discount;
            // //
            // document.querySelectorAll("#b5-collection__old_price").forEach((oldPrice) => {
            //     oldPrice.innerHTML = Boulies.formatPrice(parseInt(current_variant.dataset.price));
            // });
            // document.querySelectorAll("#bt20-collection_price").forEach((price) => {
            //     price.innerHTML = Boulies.formatPrice(sale_price);
            // });
            // document.querySelectorAll("#bt20-csb_pricesWrap").forEach((priceWrap) => {
            //     priceWrap.classList.add("b5-on-sale-price");
            // });
            // document.getElementById("Klarna_notify").dataset.purchaseAmount = sale_price;
            // if (!stopKlarnaReflash) {
            //     try {
            //         window.Klarna.OnsiteMessaging.refresh();
            //     } catch (err) {
            //         console.log(err);
            //     }
            // }
        } else {
            // saleWrap.style.display = "none";
            // // update price when not on sale
            // //
            // document.querySelectorAll("#bt20-csb_pricesWrap").forEach((priceWrap) => {
            //     priceWrap.classList.remove("b5-on-sale-price");
            // });
            // document.querySelectorAll("#bt20-collection_price").forEach((price) => {
            //     price.innerHTML = Boulies.formatPrice(parseInt(current_variant.dataset.price));
            // });
            // document.getElementById("Klarna_notify").dataset.purchaseAmount = parseInt(current_variant.dataset.price);
            // if (!stopKlarnaReflash) {
            //     try {
            //         window.Klarna.OnsiteMessaging.refresh();
            //     } catch (err) {
            //         console.log(err);
            //     }
            // }
        }
        if (_endDate_sub > _localDate && _startDate_sub < _localDate) {
            document.getElementById("sub_promotion_url__bt40").href = current_variant_metadata?.Sub_Promotion?.url;
            document.getElementById("sub_promotion_image__bt40").src = current_variant_metadata?.Sub_Promotion?.image;
            document.querySelector(".product_sub_promotion_wrapper__bt40").style.display = "block";
        } else {
            document.querySelector(".product_sub_promotion_wrapper__bt40").style.display = "none";
        }

        if (!stopKlarnaReflash) {
            try {
                window.Klarna.OnsiteMessaging.refresh();
            } catch (err) {
                console.log(err);
            }
        }

        // update main product price
        let salePrice = parseInt(current_variant.dataset.price);
        let oldPrice = parseInt(current_variant.dataset.price);
        if (current_variant.dataset.saleStatus == "true") {
            salePrice = parseInt(current_variant.dataset.price) - current_variant_metadata?.Main_Promotion?.discount;
        }
        const mainProduct = document.getElementById("bt51-currentMainProduct");
        mainProduct.dataset.oldPrice = oldPrice;
        mainProduct.dataset.currentPrice = salePrice;
        mainProduct.querySelector(".bt51-totalPriceList-item-value").innerHTML = current_variant.dataset.option1 + "/" + current_variant.dataset.option2;
        mainProduct.querySelector(".bt51-totalPriceList-item-oldPrice").innerHTML = Boulies.formatPrice(oldPrice);
        mainProduct.querySelector(".bt51-totalPriceList-item-currentPrice").innerHTML = Boulies.formatPrice(salePrice);

        // update total price
        productInter.updateTotalPrice();
    },
    variantsMap: {},
    makeVariantsMap: function () {
        var model_list = JSON.parse(document.querySelector("#product-model-list").dataset.list),
            final_price;
        model_list.forEach(function (model) {
            productInter.variantsMap[model] = {};
        });
        document.querySelectorAll(".bt51-variant").forEach(function (variant) {
            if (productInter.variantsMap[variant.dataset.option1][variant.dataset.option2] === undefined) {
                productInter.variantsMap[variant.dataset.option1][variant.dataset.option2] = [];
                if (variant.dataset.saleStatus == "true") {
                    final_price = parseInt(variant.dataset.price) - parseInt(variant.dataset.saleDiscount);
                } else {
                    final_price = parseInt(variant.dataset.price);
                }
                productInter.variantsMap[variant.dataset.option1][variant.dataset.option2].push({
                    sku: variant.id,
                    price: final_price,
                    color: variant.dataset.option1,
                    size: variant.dataset.option2,
                });
            } else {
                productInter.variantsMap[variant.dataset.option1][variant.dataset.option2].push({
                    sku: variant.id,
                    price: final_price,
                    color: variant.dataset.option1,
                    size: variant.dataset.option2,
                });
            }
        });
    },
    sortOptionsOnFirstLoad: function () {
        var models = Object.keys(productInter.variantsMap),
            upholsterys = [],
            models_HTML = "",
            default_variant = document.querySelector("input.bt51-variant:checked"),
            default_model;

        if (default_variant) {
            default_model = default_variant.dataset.option1;
        } else {
            var variants = document.querySelectorAll("input.bt51-variant"),
                default_option = document.getElementById("product-model-list").dataset.defaultOption.split(" / "),
                weighted_variants = [];
            for (var i = 0; i < variants.length; i++) {
                if (variants[i].dataset.option1 == default_option[0] && variants[i].dataset.option2 == default_option[1]) {
                    weighted_variants.push({
                        node: variants[i],
                        weight: 2,
                    });
                } else if (variants[i].dataset.option1 == default_option[0]) {
                    weighted_variants.push({
                        node: variants[i],
                        weight: 1,
                    });
                } else {
                    weighted_variants.push({
                        node: variants[i],
                        weight: 0,
                    });
                }
            }
            weighted_variants.sort(function (a, b) {
                return b.weight - a.weight;
            });
            weighted_variants[0].node.checked = true;
            default_model = weighted_variants[0].node.dataset.option1;
            default_upholstery = weighted_variants[0].node.dataset.option2;
        }

        for (var i = 0; i < models.length; i++) {
            var model_upholsterys = Object.keys(productInter.variantsMap[models[i]]);
            if (model_upholsterys.length == 0) continue;
            var is_checked = "",
                first_variant_preview = document.querySelector(
                    '.color_image_storage[data-option="' + productInter.variantsMap[models[i]][model_upholsterys[0]][0].color + '"]'
                ),
                color_preview = "",
                low_price_list = [];

            if (first_variant_preview) {
                color_preview = 'style="background-image:url(' + Boulies.imageResize(first_variant_preview.value, 160, "heightOnly") + ')"';
            }
            for (var n = 0; n < model_upholsterys.length; n++) {
                low_price_list.push(
                    productInter.variantsMap[models[i]][model_upholsterys[n]].sort(function (a, b) {
                        return a.price - b.price;
                    })[0].price
                );
                if (upholsterys.indexOf(model_upholsterys[n]) !== -1) continue;
                upholsterys.push(model_upholsterys[n]);
            }
            if (models[i] == default_model) {
                is_checked = 'checked="checked"';
            }

            models_HTML +=
                '<input type="radio" ' +
                is_checked +
                ' class="bt51-model" name="product-model" id="bt51-model-' +
                i +
                '" value="' +
                models[i] +
                '"> <label for="bt51-model-' +
                i +
                '" class="bt51-model-display" data-value="' +
                models[i] +
                '" ' +
                color_preview +
                "></label>";
        }
        document.getElementById("product-model-list").innerHTML = models_HTML;
    },
    changeUpholstery: function (stop_active_first_element) {
        var model_value = document.querySelector("input.bt51-model:checked").value;
        var upholstery_input = document.querySelectorAll(".bt51-upholstery");
        document.querySelectorAll(".bt51-upholstery-display").forEach(function (upholstery, index) {
            if (productInter.variantsMap[model_value][upholstery.dataset.value] === undefined) {
                upholstery.style.display = "none";
                upholstery_input[index].disabled = true;
            } else {
                upholstery.style.display = "block";
                upholstery_input[index].disabled = false;
            }
        });
        if (!stop_active_first_element) {
            // document.querySelector("input.bt51-upholstery:enabled").checked = true;
            productInter.changeVariants(stop_active_first_element);
        } else {
            productInter.changeVariants(stop_active_first_element);
        }
    },
    changeVariants: function (stop_active_first_element) {
        var model_value = document.querySelector('input[name="product-model"]:checked').value;
        // var upholstery_value = document.querySelector('input[name="product-upholstery"]:checked').value;
        var variant_input = document.querySelectorAll(".bt51-variant");
        document.querySelectorAll(".bt51-variant-display").forEach(function (variant, index) {
            // if (variant.dataset.option1 == model_value && variant.dataset.option2 == upholstery_value) {
            if (variant.dataset.option1 == model_value) {
                variant.style.display = "block";
                variant_input[index].disabled = false;
            } else {
                variant.style.display = "none";
                variant_input[index].disabled = true;
            }
        });
        if (!stop_active_first_element) {
            document.querySelector("input.bt51-variant:enabled").checked = true;
            productInter.changeToThisOption(document.querySelector('input[name="id"]:checked'));
        } else {
            productInter.changeToThisOption(document.querySelector('input[name="id"]:checked'), false, stop_active_first_element);
        }
    },
    getAddonItems: function (addonItems) {
        if (!addonItems || addonItems.length < 1) return false;

        const itemList = document.getElementById("product-add-on-list");
        if (itemList.dataset.addonList == addonItems.join()) return;
        itemList.innerHTML = "";

        addonItems.forEach(function (v) {
            $.getJSON("/products/" + v + ".js", function (item) {
                // console.log("addonItems:", item);

                const noticeText = addonData.find((addon) => addon.handle === item.handle)?.notice || "";
                const introText = addonData.find((addon) => addon.handle === item.handle)?.intro || "";
                // default show emo logo
                // !! false is string here 
                const show_emo_logo = addonData.find((addon) => addon.handle === item.handle)?.show_emo_logo;
                const addonVariants = addonData.find((addon) => addon.handle === item.handle)?.variants || [];

                const tempDOM = document.createElement("div");
                tempDOM.setAttribute("class", "bt32-addonItem");

                // selected variant price
                const priceDom = '<span class="bt32-addonPrice-current" id="bt32-addonPrice-current">' + Boulies.formatPrice(0) + "</span>";

                let colorOptions = "";
                let imgDom = "";
                if (item?.options[0]?.name == "Color") {
                    colorOptions = "";
                    item.variants.forEach((variant, index) => {
                        if (variant.options[0]) {
                            let colorValue = variant.options[0].toLowerCase().replace(/\s/g, "");
                            let inputId = variant.id;
                            // default selected first
                            const isSelected = index === 0;

                            colorOptions += `
                                <input type="radio" id="${inputId}" name="color_${item.id}" value="${variant.id}" 
                                    class="bt32-colorInput" ${isSelected ? "checked" : ""}> 
                                <label for="${inputId}" class="bt32-colorOption">${colorValue}</label>
                            `;

                            imgDom += `<img class="bt32-addonImage-img${isSelected ? " selected" : ""}" 
                                src="${Boulies.imageResize(variant.featured_image.src, 60)}" 
                                alt="${variant.title}" 
                                id="${variant.sku}">`;
                        }
                    });
                }

                const svgDom = `<svg
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    fill="none"
                    version="1.1"
                    width="44.769229888916016"
                    height="12"
                    viewBox="0 0 44.769229888916016 12">
                    <defs>
                        <clipPath id="master_svg0_121_2940">
                            <rect
                                x="0"
                                y="0"
                                width="44.769229888916016"
                                height="12"
                                rx="0"
                            />
                        </clipPath>
                    </defs>
                    <g clip-path="url(#master_svg0_121_2940)">
                        <g>
                            <g>
                                <path
                                    d="M40.81460665625,0.427132L40.10009765625,0.427132L40.10009765625,0L42.04693765625,0L42.04693765625,0.427132L41.32439765625,0.427132L41.32439765625,2.55876L40.81460665625,2.55876L40.81460665625,0.427132Z"
                                    fill="#D0B587"
                                    fill-opacity="1"
                                    style="mix-blend-mode:passthrough"
                                />
                            </g>
                            <g>
                                <path
                                    d="M42.45654296875,0L43.02253196875,0L43.45605596875,1.19678C43.51225296875,1.35393,43.56042296875,1.52317,43.61260296875,1.68838L43.62866296875,1.68838C43.68486296875,1.5272,43.72901296875,1.35796,43.78521296875,1.19678L44.20669296875,0L44.77268296875,0L44.77268296875,2.55876L44.30704296875,2.55876L44.30704296875,1.49093C44.30704296875,1.24916,44.34317296875,0.89456,44.37127296875,0.652787L44.35521296875,0.652787L44.15049296875,1.25319L43.75711296875,2.32908L43.46408296875,2.32908L43.06668696875,1.25319L42.86196796875,0.652787L42.84992596875,0.652787C42.87400996875,0.89456,42.91415296875,1.24916,42.91415296875,1.49093L42.91415296875,2.55876L42.45654603126,2.55876L42.45654296875,0Z"
                                    fill="#D0B587"
                                    fill-opacity="1"
                                    style="mix-blend-mode:passthrough"
                                />
                            </g>
                        </g>
                        <g>
                            <path
                                d="M0,12L8.88721,12L8.88721,10.09L2.25994,10.09L2.25994,6.64876L8.15664,6.64876L8.15664,4.73875L2.25994,4.73875L2.25994,1.91001L8.88721,1.91001L8.88721,0L0,0L0,12Z"
                                fill="#CAAF7F"
                                fill-opacity="1"
                                style="mix-blend-mode:passthrough"
                            />
                        </g>
                        <g>
                            <path
                                d="M19.32357609375,0L16.32504609375,7.74077L13.33052609375,0L11.11474609375,0L11.11474609375,12L13.36263609375,12L13.36263609375,6.06447L15.305466093749999,10.7871L17.34863609375,10.7871L19.29146609375,6.06447L19.29146609375,12L21.539346093749998,12L21.539346093749998,0L19.32357609375,0Z"
                                fill="#CAAF7F"
                                fill-opacity="1"
                                style="mix-blend-mode:passthrough"
                            />
                        </g>
                        <g>
                            <g>
                                <path
                                    d="M37.20676171875,1.5997610092163086L35.59309171875,3.219641009216309C36.415981718750004,3.8643710092163084,36.94584171875,4.867721009216309,36.94584171875,6.0000310092163085C36.94584171875,7.950331009216309,35.37232171875,9.52991100921631,33.429501718750004,9.52991100921631L29.30702171875,9.52991100921631L27.31201171875,11.53260100921631C27.86997071875,11.713961009216309,28.46406171875,11.814661009216309,29.08223171875,11.814661009216309L33.429501718750004,11.814661009216309C36.624721718749996,11.814661009216309,39.221811718750004,9.207551009216308,39.221811718750004,6.0000310092163085C39.221811718750004,4.243141009216309,38.43911171875,2.6675910092163084,37.20676171875,1.5997610092163086Z"
                                    fill="#CAAF7F"
                                    fill-opacity="1"
                                    style="mix-blend-mode:passthrough"
                                />
                            </g>
                            <g>
                                <path
                                    d="M25.56555078125,5.999970618591308C25.56555078125,4.049670618591309,27.13907078125,2.4700906185913087,29.08190078125,2.4700906185913087L33.42915078125,2.4700906185913087C33.47335078125,2.4700906185913087,33.521450781249996,2.4741106185913084,33.56565078125,2.4781406185913086L35.46835078125,0.5681386185913087C34.83405078125,0.3263656185913086,34.14765078125,0.18936069064530858,33.42915078125,0.18936069064530858L29.08189078125,0.18936069064530858C25.88667078125,0.18936069064530858,23.28955078125,2.7964806185913087,23.28955078125,6.004000618591308C23.28955078125,7.857590618591309,24.16060878125,9.505680618591308,25.50533078125,10.56946061859131L27.12703078125,8.941540618591308C26.18371078125,8.30890061859131,25.56153078125,7.228980618591309,25.56153078125,6.004000618591308L25.56555078125,5.999970618591308Z"
                                    fill="#CAAF7F"
                                    fill-opacity="1"
                                    style="mix-blend-mode:passthrough"
                                />
                            </g>
                        </g>
                    </g>
                </svg>`;

                const stockSvg = `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="22" height="22" viewBox="0 0 22 22"><defs><clipPath id="master_svg0_208_1274"><rect x="0" y="0" width="22" height="22" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_208_1274)"><g><path d="M1.375,10.999986052513123C1.375,5.684756052513123,5.68477,1.3749860525131226,11,1.3749860525131226C16.3152,1.3749860525131226,20.625,5.684756052513123,20.625,10.999986052513123C20.625,16.315186052513123,16.3152,20.624986052513123,11,20.624986052513123C5.68477,20.624986052513123,1.375,16.315186052513123,1.375,10.999986052513123ZM3.00781,10.999986052513123C3.00781,15.412886052513123,6.58711,18.992186052513123,11,18.992186052513123C15.4129,18.992186052513123,18.9922,15.412886052513123,18.9922,10.999986052513123C18.9922,6.587096052513123,15.4129,3.0077960525131227,11,3.0077960525131227C6.58711,3.0077960525131227,3.00781,6.587096052513123,3.00781,10.999986052513123ZM11.6875,12.203086052513122C11.6875,12.297686052513123,11.6102,12.374986052513123,11.5156,12.374986052513123L10.48438,12.374986052513123C10.38984,12.374986052513123,10.3125,12.297686052513123,10.3125,12.203086052513122L10.3125,6.359366052513122C10.3125,6.264826052513123,10.38984,6.187486052513123,10.48438,6.187486052513123L11.5156,6.187486052513123C11.6102,6.187486052513123,11.6875,6.264826052513123,11.6875,6.359366052513122L11.6875,12.203086052513122ZM10.2708,15.510486052513123C10.0774,15.317086052513122,9.96875,15.054786052513123,9.96875,14.781186052513123C9.96875,14.507686052513122,10.0774,14.245386052513123,10.2708,14.051986052513122C10.46419,13.858686052513123,10.7265,13.749986052513123,11,13.749986052513123C11.2735,13.749986052513123,11.5358,13.858686052513123,11.7292,14.051986052513122C11.9226,14.245386052513123,12.0312,14.507686052513122,12.0312,14.781186052513123C12.0312,15.054786052513123,11.9226,15.317086052513122,11.7292,15.510486052513123C11.5358,15.703786052513122,11.2735,15.812486052513123,11,15.812486052513123C10.7265,15.812486052513123,10.46419,15.703786052513122,10.2708,15.510486052513123Z" fill-rule="evenodd" fill="#8C8C8C" fill-opacity="1"/></g></g></svg>`;

                let checkboxId = `bt51-addonItemCheckbox-${item.id}`;

                tempDOM.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="item_${item.id}" class="bt51-addonItemInput">
                    <label for="${checkboxId}" class="bt51-addonItem-wrapper">
                        <div class="bt32-addonInfo-wrapper">
                            <div class="bt32-addonInfo">
                                <div class="bt32-addonInfo-content">
                                    <h3 class="bt32-addonTitle">${item.title} ${show_emo_logo == "false" ? "" : svgDom}</h3>
                                    <div class="bt32-addonDesc">${introText} <a class="bt32-addonDetailsBtn" id="bt32-addonDetailsBtn">Details</a></div>
                                </div>
                                <div class="bt32-addonColors">${colorOptions}</div>
                            </div>
                            <div class="bt32-addonImage">${imgDom}</div>
                        </div>
                        <div class="bt32-addonDetails">
                            <div class="bt32-addonControls">
                                <div class="bt32-addonQty-wrapper">
                                    <button class="bt32-addonQtyBtn" type="button" id="bt32-addonQtyBtn-minus" disabled>-</button>
                                    <span class="bt32-addonQty" id="bt32-addonQty">0</span>
                                    <button class="bt32-addonQtyBtn" type="button" id="bt32-addonQtyBtn-plus">+</button>
                                </div>
                                <div class="bt51-stock-quantity-wrapper">
                                    ${stockSvg}
                                    <span class="bt51-stock-quantity-text">Only <span class="bt51-stock-quantity-number">999</span> left in stock</span>
                                </div>
                            </div>
                            <div class="bt32-addonPrice-wrapper">${priceDom}</div>
                        </div>
                    </label>
                `;

                // update input value to selected variant id
                const updateInputValue = () => {
                    const checkedInput = tempDOM.querySelector(".bt32-colorInput:checked");
                    const selectedVariant = item.variants.find((v) => v.id == checkedInput.value);
                    tempDOM.querySelector(".bt51-addonItemInput").value = selectedVariant.id;
                };
                updateInputValue();

                // update price
                const updatePrice = () => {
                    // let qty = parseInt(qtySpan.textContent);
                    const checkedInput = tempDOM.querySelector(".bt32-colorInput:checked");
                    const selectedVariant = item.variants.find((v) => v.id == checkedInput.value);
                    let unitPrice = parseInt(selectedVariant.price);
                    // let currentPrice = Math.round(qty * singlePrice);
                    // only show Unit Price
                    tempDOM.querySelector(".bt32-addonPrice-current").dataset.price = unitPrice;
                    tempDOM.querySelector(".bt32-addonPrice-current").innerHTML = Boulies.formatPrice(unitPrice);
                };
                updatePrice();

                // change color img
                const colorInputs = tempDOM.querySelectorAll(".bt32-colorInput");
                const colorImages = tempDOM.querySelectorAll(".bt32-addonImage-img");
                colorInputs.forEach((input) => {
                    input.addEventListener("change", () => {
                        // Remove selected class from all images
                        colorImages.forEach((img) => img.classList.remove("selected"));
                        // Find and show the image matching the selected color variant
                        const selectedVariant = item.variants.find((v) => v.id == input.value);
                        const selectedImage = tempDOM.querySelector(`#${selectedVariant.sku}`);
                        selectedImage.classList.add("selected");
                        updatePrice();
                        // update input value
                        updateInputValue();
                        // check stock
                        const selectedAddonVariant = addonVariants.find((v) => v.id == input.value);
                        const qtySpan = tempDOM.querySelector("#bt32-addonQty");
                        let qty = parseInt(qtySpan.textContent);
                        if (qty >= selectedAddonVariant.inventory_quantity) {
                            tempDOM.querySelector(".bt51-stock-quantity-wrapper").style.display = "flex";
                            tempDOM.querySelector(".bt51-stock-quantity-number").textContent = selectedAddonVariant.inventory_quantity;
                            qtySpan.textContent = selectedAddonVariant.inventory_quantity;
                            plusBtn.disabled = true;
                        } else {
                            tempDOM.querySelector(".bt51-stock-quantity-wrapper").style.display = "none";
                            plusBtn.disabled = false;
                        }
                    });
                });

                // change qty
                const qtySpan = tempDOM.querySelector("#bt32-addonQty");
                const minusBtn = tempDOM.querySelector("#bt32-addonQtyBtn-minus");
                const plusBtn = tempDOM.querySelector("#bt32-addonQtyBtn-plus");
                // minusBtn.disabled = false;
                minusBtn.addEventListener("click", (e) => {
                    e.preventDefault();
                    let qty = parseInt(qtySpan.textContent);
                    if (qty > 0) {
                        qty--;
                        qtySpan.textContent = qty;
                        if (qty === 0) {
                            minusBtn.disabled = true;
                            // uncheck the input
                            tempDOM.querySelector(".bt51-addonItemInput").checked = false;
                        }
                    }

                    // check stock
                    const checkedInput = tempDOM.querySelector(".bt32-colorInput:checked");
                    const selectedVariant = addonVariants.find((v) => v.id == checkedInput.value);
                    if (qty >= selectedVariant.inventory_quantity) {
                        tempDOM.querySelector(".bt51-stock-quantity-wrapper").style.display = "flex";
                        tempDOM.querySelector(".bt51-stock-quantity-number").textContent = selectedVariant.inventory_quantity;
                        plusBtn.disabled = true;
                    } else {
                        tempDOM.querySelector(".bt51-stock-quantity-wrapper").style.display = "none";
                        plusBtn.disabled = false;
                    }
                });
                plusBtn.addEventListener("click", (e) => {
                    e.preventDefault();
                    let qty = parseInt(qtySpan.textContent);

                    if (qty === 0) {
                        // show notice modal
                        const checkedInput = tempDOM.querySelector(".bt32-colorInput:checked");
                        const selectedVariant = item.variants.find((v) => v.id == checkedInput.value);
                        openNoticeModal(Boulies.imageResize(selectedVariant.featured_image.src, 90), noticeText);
                        const noticeModalConfirmBtn = document.getElementById("addonNoticeModalConfirmBtn");
                        // if confirm check the input
                        noticeModalConfirmBtn.addEventListener("click", function () {
                            closeNoticeModal();
                            qty++;
                            qtySpan.textContent = qty;
                            minusBtn.disabled = false;
                            // check the input
                            tempDOM.querySelector(".bt51-addonItemInput").checked = true;
                            // updatePrice();
                        });
                        // if cancel or close uncheck the input
                        // clone cancel button, replace old one, clear all events
                        let cancelBtn = document.getElementById("addonNoticeModalCancelBtn");
                        const newCancelBtn = cancelBtn.cloneNode(true);
                        cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);
                        cancelBtn = document.getElementById("addonNoticeModalCancelBtn");
                        // clone close button, replace old one, clear all events
                        let closeBtn = document.getElementById("addonNoticeModalCloseBtn");
                        const newCloseBtn = closeBtn.cloneNode(true);
                        closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);
                        closeBtn = document.getElementById("addonNoticeModalCloseBtn");

                        // uncheck the input
                        function cancel() {
                            tempDOM.querySelector(`#${checkboxId}`).checked = false;
                        }
                        cancelBtn.addEventListener("click", function () {
                            closeNoticeModal();
                            cancel();
                        });
                        closeBtn.addEventListener("click", function () {
                            closeNoticeModal();
                            cancel();
                        });
                    } else {
                        qty++;
                        qtySpan.textContent = qty;
                        minusBtn.disabled = false;
                    }

                    // check stock
                    const checkedInput = tempDOM.querySelector(".bt32-colorInput:checked");
                    const selectedVariant = addonVariants.find((v) => v.id == checkedInput.value);
                    if (qty >= selectedVariant.inventory_quantity) {
                        tempDOM.querySelector(".bt51-stock-quantity-wrapper").style.display = "flex";
                        tempDOM.querySelector(".bt51-stock-quantity-number").textContent = selectedVariant.inventory_quantity;
                        plusBtn.disabled = true;
                    } else {
                        tempDOM.querySelector(".bt51-stock-quantity-wrapper").style.display = "none";
                        plusBtn.disabled = false;
                    }
                });

                // plus 1 when checked
                tempDOM.querySelector(".bt51-addonItemInput").addEventListener("change", function () {
                    let qty = parseInt(qtySpan.textContent);
                    if (this.checked && qty === 0) {
                        plusBtn.click();
                    }
                    if (!this.checked) {
                        qtySpan.textContent = 0;
                        minusBtn.disabled = true;
                        // hide stock quantity
                        tempDOM.querySelector(".bt51-stock-quantity-wrapper").style.display = "none";
                        plusBtn.disabled = false;
                    }
                });

                // show modal when click details
                const detailBtn = tempDOM.querySelector("#bt32-addonDetailsBtn");
                detailBtn.addEventListener("click", (e) => {
                    e.preventDefault();
                    const checkedInput = tempDOM.querySelector(".bt32-colorInput:checked");
                    const selectedVariant = item.variants.find((v) => v.id == checkedInput.value);
                    openDetailModal(
                        // image
                        Boulies.imageResize(selectedVariant.featured_image.src, 500),
                        // title
                        item?.title || "",
                        // price
                        "+" + Boulies.formatPrice(selectedVariant.price),
                        // description
                        item?.description || "",
                        // url
                        item?.url || ""
                    );
                });

                itemList.appendChild(tempDOM);
            });
        });

        itemList.dataset.addonList = addonItems.join();
    },
    notifyMe: function (target) {
        if (target.classList.contains("disabled")) return;
        var notifymeData = document.getElementById("bt32-notifyme_data");
        if (!/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/.test(notifymeData.value)) {
            alert("Please enter an valid email address!");
            return;
        }
        var requestData = JSON.parse(JSON.stringify(notifymeData.dataset));
        requestData["email"] = notifymeData.value;
        $.ajax({
            type: "POST",
            url: "https://app.boulies.com/notifyorders/notifyme",
            data: requestData,
            beforeSend: function () {
                target.classList.add("disabled");
                target.innerHTML = "Sending request...";
            },
            error: function (xhr, status, error) {
                alert(status);
                target.classList.remove("disabled");
                target.innerHTML = "Notify me";
            },
            success: function () {
                target.innerHTML = "We've received your request!";
            },
        });
    },
    updateTotalPrice: function () {
        // update total price
        const totalPriceListHtml = document.getElementById("bt51-totalPriceList");
        let totalOldPrice = 0;
        let totalCurrentPrice = 0;
        totalPriceListHtml.querySelectorAll(".bt51-totalPriceList-item").forEach((item) => {
            let oldPrice = parseInt(item.dataset.oldPrice);
            let currentPrice = parseInt(item.dataset.currentPrice);
            let quantity = parseInt(item.dataset.quantity);
            totalOldPrice += oldPrice * quantity;
            totalCurrentPrice += currentPrice * quantity;
        });

        // have sale
        if (totalOldPrice > totalCurrentPrice) {
            document.querySelectorAll("#b5-collection__old_price").forEach((oldPrice) => {
                oldPrice.innerHTML = Boulies.formatPrice(totalOldPrice);
            });
            document.querySelectorAll("#bt20-collection_price").forEach((price) => {
                price.innerHTML = Boulies.formatPrice(totalCurrentPrice);
            });
            document.querySelectorAll("#bt20-csb_pricesWrap").forEach((priceWrap) => {
                priceWrap.classList.add("b5-on-sale-price");
            });
            document.getElementById("Klarna_notify").dataset.purchaseAmount = totalCurrentPrice;
            // if (!stopKlarnaReflash) {
            //     try {
            //         window.Klarna.OnsiteMessaging.refresh();
            //     } catch (err) {
            //         console.log(err);
            //     }
            // }
        } else {
            saleWrap.style.display = "none";
            // update price when not on sale
            document.querySelectorAll("#bt20-csb_pricesWrap").forEach((priceWrap) => {
                priceWrap.classList.remove("b5-on-sale-price");
            });
            document.querySelectorAll("#bt20-collection_price").forEach((price) => {
                price.innerHTML = Boulies.formatPrice(totalCurrentPrice);
            });
            document.getElementById("Klarna_notify").dataset.purchaseAmount = totalCurrentPrice;
            // if (!stopKlarnaReflash) {
            //     try {
            //         window.Klarna.OnsiteMessaging.refresh();
            //     } catch (err) {
            //         console.log(err);
            //     }
            // }
        }
    },
    init: function () {
        productInter.makeVariantsMap();

        productInter.sortOptionsOnFirstLoad();

        productInter.changeUpholstery(true);

        document.getElementById("bt20-csb_cartBtn").addEventListener("click", function (e) {
            e.preventDefault();
            productInter.addToCart(this);
        });

        document.getElementById("bt32-notifymeBtn").addEventListener("click", function (e) {
            e.preventDefault();
            productInter.notifyMe(this);
        });

        document.querySelectorAll(".bt51-model").forEach(function (model) {
            model.addEventListener("change", function () {
                productInter.changeUpholstery();
            });
        });

        // document.querySelectorAll(".bt51-upholstery").forEach(function (upholstery) {
        //     upholstery.addEventListener("change", function () {
        //         productInter.changeVariants();
        //     });
        // });

        document.querySelectorAll(".bt51-variant").forEach(function (option) {
            option.addEventListener("change", function () {
                productInter.changeToThisOption(this);
            });
        });

        document.querySelectorAll(".bt51-variant-display").forEach(function (option) {
            // var storage_selector = '.color_image_storage[data-option="' + option.dataset.option2 + " / " + option.dataset.option3 + '"]';
            var storage_selector = '.color_image_storage[data-option="' + option.dataset.option2 + '"]';
            if (document.querySelector(storage_selector)) {
                option.style.backgroundImage = "url(" + document.querySelector(storage_selector).value + ")";
            }
        });

        window.addEventListener("popstate", (event) => {
            var url_param = Boulies.getUrlkey();
            if (!url_param || !url_param.variant) return false;
            var selected_variant = document.querySelector('input[value="' + url_param.variant + '"]');
            selected_variant.checked = true;
            productInter.changeToThisOption(selected_variant, true);
        });
    },
};

// ==============================================================
// Run Code After Document Ready
// ==============================================================
$(document).ready(function () {
    productInter.init();

    // set add-on wrapper open btn
    function showAddon() {
        const addonBuyBoxWrapper = document.querySelector(".bt51-addonBuyBox-wrapper");
        if (addonBuyBoxWrapper) {
            addonBuyBoxWrapper.style.right = "0";
        }
    }
    const addonOpenDiv = document.getElementById("bt51-addonSection");
    if (addonOpenDiv) {
        addonOpenDiv.addEventListener("click", showAddon);
    }

    function closeAddon() {
        const addonBuyBoxWrapper = document.querySelector(".bt51-addonBuyBox-wrapper");
        addonBuyBoxWrapper.style.right = "-100vw";
        //
        const body = document.body;
        body.style.overflow = "auto";
        // scroll to package container
        document.querySelector(".bt32-packageContainer").scrollIntoView({
            behavior: "smooth",
        });
    }

    // set add-on wrapper close btn
    const addonCloseBtn = document.getElementById("bt51-addonCloseBtn");
    if (addonCloseBtn) {
        addonCloseBtn.addEventListener("click", closeAddon);
    }

    // set add-on wrapper confirm btn
    const addonConfirmBtn = document.getElementById("bt51-addonConfirmBtn");
    if (addonConfirmBtn) {
        addonConfirmBtn.addEventListener("click", function () {
            const addonItems = document.querySelectorAll(".bt32-addonItem");
            // selected add-on list
            const selectedAddonList = document.getElementById("bt51-selectedAddonList");
            let selectedAddonhtml = "";
            // total price list
            const totalPriceList = document.getElementById("bt51-addonTotalPriceList");
            let totalPricehtml = "";

            if (addonItems.length > 0) {
                let indexCount = 0;
                addonItems.forEach((item, index) => {
                    const addonItemCheckbox = item.querySelector(".bt51-addonItemInput");
                    const addonItemQty = Number(item.querySelector("#bt32-addonQty").textContent);
                    if (addonItemCheckbox?.checked && addonItemQty > 0) {
                        // add add-on list
                        let img = item.querySelector(".bt32-addonImage-img.selected");
                        let imgSrc = img.src;
                        let imgAlt = img.alt;
                        let addonItemPrice = item.querySelector(".bt32-addonPrice-current").dataset.price;
                        let addonItemId = addonItemCheckbox.value;
                        let addonItemTitle = item.querySelector(".bt32-addonTitle").textContent;
                        selectedAddonhtml += `
                        <div class="bt51-selectedAddonItem" data-id="${addonItemId}" data-quantity="${addonItemQty}" data-price="${addonItemPrice}">
                            <div class="bt51-selectedAddonItem-wrapper">
                                <div class="bt51-selectedAddonItem-img"><img src="${imgSrc}" alt="${imgAlt}"></div>
                                <div class="bt51-selectedAddonItem-info">
                                    <div class="bt51-selectedAddonItem-title">${addonItemTitle}</div>
                                    <div class="bt51-selectedAddonItem-color">${imgAlt}</div>
                                </div>
                            </div>
                            <div class="bt51-selectedAddonItem-qty">x${addonItemQty}</div>
                        </div>`;

                        // add addon item
                        // maybe no old price, both use current price
                        indexCount++;
                        // the first selected item title is "Add-ons", others are empty
                        let itemTitle = indexCount === 1 ? "Add-ons" : "";
                        totalPricehtml += `
                        <div class="bt51-totalPriceList-item" data-current-price="${addonItemPrice}" data-quantity="${addonItemQty}" data-old-price="${addonItemPrice}">
                            <div class="bt51-totalPriceList-item-title">${itemTitle}</div>
                            <div class="bt51-totalPriceList-item-wrapper">
                                <div class="bt51-totalPriceList-item-value"> ${addonItemTitle}</div>
                                <div class="bt51-totalPriceList-item-quantity">x${addonItemQty}</div>
                                <div class="bt51-totalPriceList-item-price">
                                    <span class="bt51-totalPriceList-item-oldPrice"></span>
                                    <span class="bt51-totalPriceList-item-currentPrice">${Boulies.formatPrice(addonItemPrice)}</span>
                                </div>
                            </div>
                        </div>`;
                    }
                });
                // add html
                selectedAddonList.innerHTML = selectedAddonhtml;
                totalPriceList.innerHTML = totalPricehtml;
                const addonOpenBtn = document.getElementById("bt51-addonOpenBtn");

                if (selectedAddonhtml == "") {
                    // show addon open btn
                    if (addonOpenBtn) {
                        addonOpenBtn.style.display = "block";
                    }
                } else {
                    // hide addon open btn
                    if (addonOpenBtn) {
                        addonOpenBtn.style.display = "none";
                    }
                }
            }
            productInter.updateTotalPrice();

            // hide addon buy box
            closeAddon();
        });
    }

    $(".bt20-csb_mainImage").on("click", ".pswp_image", function (e) {
        e.preventDefault();
        dynamicGallery.refresh([{ src: this.href }]);
        dynamicGallery.openGallery();
    });

    $(".dynamic-gallery-item").on("click", function (e) {
        e.preventDefault();
        dynamicGallery.refresh([{ src: this.href }]);
        dynamicGallery.openGallery();
    });

    var choose_guide = document.querySelector(".choose-guide-wrap__bt51");
    if (choose_guide) {
        choose_guide.addEventListener("click", function (e) {
            var trigger = e.target.closest(".choose-guide-header__bt51");
            if (!trigger) return;
            if (this.dataset.status == "is_actived") {
                this.dataset.status = "";
            } else {
                this.dataset.status = "is_actived";
            }
        });
    }

    if (Boulies.getUrlkey().judgeme_dynamic_form == "1") {
        var injectDOM = document.createElement("div");
        injectDOM.style.display = "inline-block";
        injectDOM.innerHTML =
            '<p style=" background-color: #fcfff5; color: #2c662d; margin-top: 10px; padding: 10px 20px; border: 1px solid #a3c293; border-radius: 4px; text-align: left; font-size: 14px; "> <b style=" display: block; margin-bottom: 2px; font-size: 16px; ">Add picture/video for a reward.</b> By submitting with picture/video, boulies will offer 6 months extended warranty as a bonus.</p> <p style=" text-align: left; padding: 4px 20px 0; font-size: 14px; color: #6e6e6e; ">* A notification email will send to you when successfully submit a picture/video review.</p>';
        var insert_notify = setInterval(function () {
            if (document.querySelector(".jdgm-form-dynamic__picture-upload-field")) {
                document.querySelector(".jdgm-form-dynamic__picture-upload-field").appendChild(injectDOM);
                clearInterval(insert_notify);
            }
        }, 1000);
    }

    if (!!document.querySelector(".bt51-bestof-wrap")) {
        var bestOfList = $(".bt51-bestofs").slick({
            slidesToShow: 3,
            dots: false,
            arrows: false,
            speed: 500,
            responsive: [
                {
                    breakpoint: 800,
                    settings: {
                        slidesToShow: 1,
                        dots: true,
                    },
                },
            ],
        });
    }
});
