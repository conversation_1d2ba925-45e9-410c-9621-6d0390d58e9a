// ==============================================================
// Boulies Ajax Cart Function
// ==============================================================

var ajaxcart = {
    cartWrap: document.querySelector(".bouliesCartWrap"),
    cartItemNum: document.querySelector(".bt5-cart-item-num"),
    isUpdatingQuantity: false,
    init: function () {
        ajaxcart.getCartItem();

        document.querySelector(".js-cart-trigger").addEventListener("click", function () {
            ajaxcart.openCart();
        });

        document.querySelector(".bouliesCartWrap").addEventListener("click", function (e) {
            if (!e.target) {
                alert("This browser is too old, some features may not work properly ");
                return false;
            }
            var target = e.target;
            if (target.classList.contains("bouliesCartWrap")) {
                ajaxcart.closeCart();
                return;
            }
            for (var i = 0; i < 4; i++) {
                if (target.classList.contains("bouliesCartCloseTrigger")) {
                    ajaxcart.closeCart();
                    return;
                }
                if (target.nodeName == "LI") {
                    target.classList.toggle("bc_faqQuestionClosed");
                    return;
                }
                target = target.parentNode;
            }
        });
    },
    openCart: function () {
        if (ajaxcart.cartWrap.classList.contains("bouliesCartLoaded")) return false;
        ajaxcart.cartWrap.style["display"] = "block";
        document.documentElement.classList.add("bouliesNoscroll");
        setTimeout(function () {
            ajaxcart.cartWrap.classList.add("bouliesCartLoaded");
        }, 20);
    },
    closeCart: function () {
        if (!ajaxcart.cartWrap.classList.contains("bouliesCartLoaded")) return false;
        ajaxcart.cartWrap.classList.remove("bouliesCartLoaded");
        setTimeout(function () {
            ajaxcart.cartWrap.style["display"] = "none";
            document.documentElement.classList.remove("bouliesNoscroll");
        }, 500);
    },
    getCartItem: function () {
        $.ajax({
            type: "GET",
            url: "/cart.js",
            dataType: "JSON",
            timeout: 5000,
            success: function (cartData) {
                ajaxcart.updateCartItem(cartData);
            },
        });
    },
    updateCartItem: function (cartData) {
        console.log("cartData:", cartData);
        if (cartData.item_count < 1) {
            ajaxcart.cartWrap.classList.add("cartIsEmpty");
            ajaxcart.cartItemNum.dataset.cartNum = "";
            return false;
        }
        ajaxcart.cartWrap.classList.remove("cartIsEmpty");
        ajaxcart.cartItemNum.dataset.cartNum = "is_actived";
        ajaxcart.cartItemNum.dataset.num = cartData.item_count;
        var lineItems = "",
            cart_discount = "";

        // group cart items by timestamp
        function groupCartItems(cartData) {
            let mainProducts = [];
            let addons = [];
            cartData.items.forEach((item) => {
                if (!item.properties || !item.properties.timestamp) {
                    mainProducts.push(item);
                    return;
                }
                if (item.properties.isAddon) {
                    addons.push(item);
                } else {
                    mainProducts.push({
                        ...item,
                        addons: [],
                    });
                }
            });
            addons.forEach((addon) => {
                let timestamp = addon.properties.timestamp;
                let existingMainProduct = mainProducts.find((product) => product.properties.timestamp === timestamp);
                if (existingMainProduct) {
                    existingMainProduct.addons.push(addon);
                }
            });
            return mainProducts;
        }
        const groupedCartItems = groupCartItems(cartData);
        console.log("groupedCartItems:", groupedCartItems);

        //
        groupedCartItems.forEach(function (v) {
            if (v.properties == null) {
                v.properties = {};
            }
            var _preorderInfo = v.properties.Estimated_to_ship_out
                ? '<tr class="bc_preorder"><td><span class="bt51-cart-warning-label">Pre-order Info</span></td><td>Estimated to ship out by: ' +
                  v.properties.Estimated_to_ship_out +
                  "</td></tr>"
                : "";
            var _preorderLabel = v.properties.Estimated_to_ship_out ? '<div class="bc_preorderedLabel">PRE-ORDERED!</div>' : "";
            var _variantTitle = v.variant_title ? '<span class="bc_itemVariants">' + v.variant_title + "</span>" : "";
            var _accessories = v.properties.accessories
                ? '<tr class="bc_accessories"><td><span class="bt51-cart-label">Package Includes</span></td><td>' + v.properties.accessories + "</td></tr>"
                : "";
            var _description = v.requires_shipping ? "" : '<tr><td colspan="2" class="bc_lineItemDescription">' + v.product_description + "</td></tr>";
            var _url = v.product_type == "Addon" ? "javascript:void(0);" : v.url;
            var _discounts = "";
            if (v.line_level_discount_allocations.length > 0) {
                _discounts = '<span class="bc_itemOriginalPrice">' + Boulies.formatPrice(v.original_line_price) + "</span>";
                v.line_level_discount_allocations.forEach(function (discount) {
                    _discounts +=
                        '<span class="bc_itemDiscount"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18"> <path d="M17.78 3.09C17.45 2.443 16.778 2 16 2h-5.165c-.535 0-1.046.214-1.422.593l-6.82 6.89c0 .002 0 .003-.002.003-.245.253-.413.554-.5.874L.738 8.055c-.56-.953-.24-2.178.712-2.737L9.823.425C10.284.155 10.834.08 11.35.22l4.99 1.337c.755.203 1.293.814 1.44 1.533z" fill="#a5a5a5"></path> <path d="M10.835 2H16c1.105 0 2 .895 2 2v5.172c0 .53-.21 1.04-.586 1.414l-6.818 6.818c-.777.778-2.036.782-2.82.01l-5.166-5.1c-.786-.775-.794-2.04-.02-2.828.002 0 .003 0 .003-.002l6.82-6.89C9.79 2.214 10.3 2 10.835 2zM13.5 8c.828 0 1.5-.672 1.5-1.5S14.328 5 13.5 5 12 5.672 12 6.5 12.672 8 13.5 8z" fill="#656565"></path> </svg>' +
                        discount.discount_application.title +
                        " (-" +
                        Boulies.formatPrice(discount.amount) +
                        ")</span>";
                });
            }
            // add package info for desk
            let _packageInfo = "";
            if (v?.addons?.length > 0) {
                _packageInfo = `<tr><td colspan="2" class="bc_lineItemDescription">* Package: Divided into 2 boxes</td></tr>`;
                _accessories = "";
            }
            // render addon html
            let addonHtml = "";
            if (v?.addons && v?.addons?.length > 0) {
                v.addons.forEach((addon) => {
                    let originalPrice = '<span class="bc_itemOriginalPrice">' + Boulies.formatPrice(addon.original_line_price) + "</span>";
                    // first addon is free
                    let currentPrice = addon.price * (addon.quantity - 1);
                    let linePrice = '<span class="bc_itemLinePrice">' + Boulies.formatPrice(currentPrice) + "</span>";
                    addonHtml += `
                    <div class="bc_listedItem addonItem" id="lineItem_${addon.id}">
                        <div class="bc_itemImageWrap addonItem">
                            <img src="${Boulies.imageResize(addon.image, 300)}">
                        </div>
                        <div class="bc_addonItemInfoWrap">
                            <div class="bc_itemInfo">
                                <h2 class="bc_itemTitle">
                                    <a href="javascript:void(0);">${addon.product_title}</a>
                                </h2>
                                ${addon.variant_title ? `<span class="bc_itemVariants">${addon.variant_title}</span>` : ""}
                                <table class="bc_itemSpecification">
                                    ${
                                        addon.requires_shipping
                                            ? ""
                                            : `<tr><td colspan="2" class="bc_lineItemDescription">${addon.product_description}</td></tr>`
                                    }
                                </table>
                            </div>
                            <div class="bc_itemPriceInfo">
                                <div class="bc_itemQuantity">
                                    <div class="bc_quantityWrap">
                                        <span class="bc_quantityAdjust" data-quantityAdjust="minus" 
                                        style="opacity: ${addon.quantity > 1 ? "1" : "0.5"};"
                                        onclick="ajaxcart.updateItemQuantity(${addon.id}, ${addon.quantity > 1 ? addon.quantity - 1 : addon.quantity})">
                                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="14" height="14" viewBox="0 0 24 24">
                                                <g><path fill="#000" d="M4,12C4,11.447715,4.447715,11,5,11C5,11,19,11,19,11C19.552300000000002,11,20,11.447715,20,12C20,12.55228,19.552300000000002,13,19,13C19,13,5,13,5,13C4.447715,13,4,12.55228,4,12C4,12,4,12,4,12Z"/></g>
                                            </svg>
                                        </span>
                                        <input 
                                            onkeydown="if(event.keyCode==13){event.preventDefault();if(this.value >= 1){ajaxcart.updateItemQuantity(${
                                                addon.id
                                            },this.value)}}"
                                            value="${addon.quantity}"
                                            min="1"
                                            autocomplete="off"
                                            name="updateQuantity"
                                            type="number"
                                            class="bc_quantityDirectAdjust"
                                            data-id="${addon.id}"
                                            data-idClass="lineItem_${addon.id}"
                                        >
                                        <span class="bc_quantityAdjust" data-quantityAdjust="plus" 
                                        onclick="ajaxcart.updateItemQuantity(${addon.id}, ${addon.quantity + 1})">
                                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="14" height="14" viewBox="0 0 24 24">
                                                <g><path fill="#000" d="M12,4C12.55228,4,13,4.447715,13,5C13,5,13,11,13,11C13,11,19,11,19,11C19.552300000000002,11,20,11.44772,20,12C20,12.55228,19.552300000000002,13,19,13C19,13,13,13,13,13C13,13,13,19,13,19C13,19.552300000000002,12.55228,20,12,20C11.44772,20,11,19.552300000000002,11,19C11,19,11,13,11,13C11,13,5,13,5,13C4.447715,13,4,12.55228,4,12C4,11.44772,4.447715,11,5,11C5,11,11,11,11,11C11,11,11,5,11,5C11,4.447715,11.44772,4,12,4C12,4,12,4,12,4Z"/></g>
                                            </svg>
                                        </span>
                                    </div>
                                </div>
                                <div class="bc_itemPrice">
                                    ${originalPrice}
                                    ${linePrice}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                });
            }

            // ****** main product minus button onclick event
            let minusBtnOnclick = "";
            // if main product quantity is 1 and has addons, the minus button should update all addon quantity to 0
            if (v?.addons?.length > 0 && v.quantity === 1) {
                let updates = {};
                minusBtnOnclick = `onclick="`;
                // all addon quantity to 0
                v.addons.forEach((addon) => {
                    updates[addon.id] = 0;
                });
                // console.log("updates:", updates);
                // main product quantity to 0
                updates[v.id] = 0;
                minusBtnOnclick += `ajaxcart.bulkUpdateCartItems(${JSON.stringify(updates).replace(/"/g, "&quot;")});"`;
            } else {
                // For normal products or products with addons but quantity > 1
                minusBtnOnclick = `onclick="ajaxcart.updateItemQuantity(${v.id}, ${v.quantity - 1})"`;
            }

            // ******
            let itemHtml = `
            <div class="bc_listedItem" id="lineItem_${v.id}">
                ${_preorderLabel}
                <div class="bc_itemImageWrap" ${v?.addons?.length > 0 ? "style='align-self:baseline;'" : ""}>
                    <img src="${Boulies.imageResize(v.image, 300)}">
                </div>
                <div class="bc_itemInfoWrap">
                    <div class="bc_mainItemInfo">
                        <div class="bc_itemInfo">
                            <h2 class="bc_itemTitle">
                                <a href="${_url}">${v.product_title}</a>
                            </h2>
                            ${_variantTitle}
                            <table class="bc_itemSpecification">
                                ${_description}
                                ${_accessories}
                                ${_preorderInfo}
                                ${_packageInfo}
                            </table>
                        </div>
                        <div class="bc_itemPriceInfo">
                            <div class="bc_itemQuantity">
                                <div class="bc_quantityWrap">
                                    <span class="bc_quantityAdjust" data-quantityAdjust="minus" ${minusBtnOnclick}>
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="14" height="14" viewBox="0 0 24 24">
                                            <g><path fill="#000" d="M4,12C4,11.447715,4.447715,11,5,11C5,11,19,11,19,11C19.552300000000002,11,20,11.447715,20,12C20,12.55228,19.552300000000002,13,19,13C19,13,5,13,5,13C4.447715,13,4,12.55228,4,12C4,12,4,12,4,12Z"/></g>
                                        </svg>
                                    </span>
                                    <input 
                                        onkeydown="if(event.keyCode==13){event.preventDefault();ajaxcart.updateItemQuantity(${v.id},this.value)}"
                                        value="${v.quantity}"
                                        min="1"
                                        autocomplete="off"
                                        name="updateQuantity"
                                        type="number"
                                        class="bc_quantityDirectAdjust"
                                        data-id="${v.id}"
                                        data-idClass="lineItem_${v.id}"
                                    >
                                    <span class="bc_quantityAdjust" data-quantityAdjust="plus" 
                                    onclick="ajaxcart.updateItemQuantity(${v.id}, ${v.quantity + 1})">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="14" height="14" viewBox="0 0 24 24">
                                            <g><path fill="#000" d="M12,4C12.55228,4,13,4.447715,13,5C13,5,13,11,13,11C13,11,19,11,19,11C19.552300000000002,11,20,11.44772,20,12C20,12.55228,19.552300000000002,13,19,13C19,13,13,13,13,13C13,13,13,19,13,19C13,19.552300000000002,12.55228,20,12,20C11.44772,20,11,19.552300000000002,11,19C11,19,11,13,11,13C11,13,5,13,5,13C4.447715,13,4,12.55228,4,12C4,11.44772,4.447715,11,5,11C5,11,11,11,11,11C11,11,11,5,11,5C11,4.447715,11.44772,4,12,4C12,4,12,4,12,4Z"/></g>
                                        </svg>
                                    </span>
                                </div>
                            </div>
                            <div class="bc_itemPrice">
                                ${_discounts}
                                <span style="margin-top:0.2em;display:block;">${Boulies.formatPrice(v.line_price)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="bc_itemAddons">
                        ${addonHtml}
                    </div>
                </div>
            </div>
        `;
            lineItems += itemHtml;
            //
        });
        document.querySelector("#bouliesAjaxCart_DataWrap").innerHTML = lineItems;
        if (cartData.total_discount > 0) {
            cart_discount +=
                '<p><span>Subtotal</span><span class="bc_calcPrice bc_oriSubTotal" >' + Boulies.formatPrice(cartData.original_total_price) + "</span></p>";
            if (cartData.cart_level_discount_applications.length > 0) {
                cartData.cart_level_discount_applications.forEach(function (discount) {
                    if (discount.discount_application) {
                        cart_discount +=
                            '<p><span>Extra discount (<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" style="width: 13px;position: relative;top: 2px;"> <path d="M17.78 3.09C17.45 2.443 16.778 2 16 2h-5.165c-.535 0-1.046.214-1.422.593l-6.82 6.89c0 .002 0 .003-.002.003-.245.253-.413.554-.5.874L.738 8.055c-.56-.953-.24-2.178.712-2.737L9.823.425C10.284.155 10.834.08 11.35.22l4.99 1.337c.755.203 1.293.814 1.44 1.533z" fill="#a5a5a5"></path> <path d="M10.835 2H16c1.105 0 2 .895 2 2v5.172c0 .53-.21 1.04-.586 1.414l-6.818 6.818c-.777.778-2.036.782-2.82.01l-5.166-5.1c-.786-.775-.794-2.04-.02-2.828.002 0 .003 0 .003-.002l6.82-6.89C9.79 2.214 10.3 2 10.835 2zM13.5 8c.828 0 1.5-.672 1.5-1.5S14.328 5 13.5 5 12 5.672 12 6.5 12.672 8 13.5 8z" fill="#656565"></path> </svg> ' +
                            discount.discount_application.title +
                            ')</span><span class="bc_calcPrice bc_discountAmount">-' +
                            Boulies.formatPrice(discount.discount_application.total_allocated_amount) +
                            "</span></p>";
                    } else {
                        cart_discount +=
                            '<p><span>Extra discount (<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" style="width: 13px;position: relative;top: 2px;"> <path d="M17.78 3.09C17.45 2.443 16.778 2 16 2h-5.165c-.535 0-1.046.214-1.422.593l-6.82 6.89c0 .002 0 .003-.002.003-.245.253-.413.554-.5.874L.738 8.055c-.56-.953-.24-2.178.712-2.737L9.823.425C10.284.155 10.834.08 11.35.22l4.99 1.337c.755.203 1.293.814 1.44 1.533z" fill="#a5a5a5"></path> <path d="M10.835 2H16c1.105 0 2 .895 2 2v5.172c0 .53-.21 1.04-.586 1.414l-6.818 6.818c-.777.778-2.036.782-2.82.01l-5.166-5.1c-.786-.775-.794-2.04-.02-2.828.002 0 .003 0 .003-.002l6.82-6.89C9.79 2.214 10.3 2 10.835 2zM13.5 8c.828 0 1.5-.672 1.5-1.5S14.328 5 13.5 5 12 5.672 12 6.5 12.672 8 13.5 8z" fill="#656565"></path> </svg> ' +
                            discount.title +
                            ')</span><span class="bc_calcPrice bc_discountAmount">-' +
                            Boulies.formatPrice(discount.total_allocated_amount) +
                            "</span></p>";
                    }
                });
            }
            cart_discount +=
                '<p><span>Total discount</span><span class="bc_calcPrice bc_discountAmount">-' + Boulies.formatPrice(cartData.total_discount) + "</span></p> ";
            document.querySelector("#bc_calcWrap").innerHTML = cart_discount;
        }
        document.querySelector("#bouliesAjaxCart_totalPrice").innerHTML = Boulies.formatPrice(cartData.total_price);
    },
    updateItemQuantity: function (id, num) {
        console.log("updateItemQuantity:", id, num);
        $.ajax({
            type: "POST",
            dataType: "JSON",
            url: "/cart/change.js",
            data: { quantity: num, id: id },
            timeout: 5000,
            success: function (cartData) {
                if (cartData.item_count < 1) {
                    ajaxcart.cartWrap.classList.add("cartIsEmpty");
                    ajaxcart.cartItemNum.dataset.cartNum = "";
                    return false;
                }

                ajaxcart.updateCartItem(cartData);
                ajaxcart.cartWrap.classList.remove("cartIsEmpty");
                ajaxcart.cartItemNum.dataset.cartNum = "is_actived";
                ajaxcart.cartItemNum.dataset.num = cartData.item_count;
                $("#lineItem_" + id).removeClass("bc_updatingQuantity");
                ajaxcart.isUpdatingQuantity = false;
            },
            error: function (e) {
                ajaxcart.isUpdatingQuantity = false;
                alert(e.responseJSON.message);
                ajaxcart.getCartItem();
            },
            beforeSend: function () {
                document.querySelector("#lineItem_" + id).classList.add("bc_updatingQuantity");
                ajaxcart.isUpdatingQuantity = true;
            },
        });
    },
    // add new func
    bulkUpdateCartItems: function (updates) {
        console.log("bulkUpdateCartItems:", updates);
        $.ajax({
            type: "POST",
            dataType: "JSON",
            url: "/cart/update.js",
            data: { updates: updates },
            timeout: 5000,
            success: function (cartData) {
                if (cartData.item_count < 1) {
                    ajaxcart.cartWrap.classList.add("cartIsEmpty");
                    ajaxcart.cartItemNum.dataset.cartNum = "";
                    return false;
                }

                ajaxcart.updateCartItem(cartData);
                ajaxcart.cartWrap.classList.remove("cartIsEmpty");
                ajaxcart.cartItemNum.dataset.cartNum = "is_actived";
                ajaxcart.cartItemNum.dataset.num = cartData.item_count;
                Object.keys(updates).forEach((id) => {
                    let itemElement = document.querySelector("#lineItem_" + id);
                    if (itemElement) {
                        itemElement.classList.remove("bc_updatingQuantity");
                    }
                });
                ajaxcart.isUpdatingQuantity = false;
            },
            error: function (e) {
                ajaxcart.isUpdatingQuantity = false;
                alert(e.responseJSON.message);
                ajaxcart.getCartItem();
            },
            beforeSend: function () {
                Object.keys(updates).forEach((id) => {
                    let itemElement = document.querySelector("#lineItem_" + id);
                    if (itemElement) {
                        itemElement.classList.add("bc_updatingQuantity");
                    }
                });
                ajaxcart.isUpdatingQuantity = true;
            },
        });
    },
};
