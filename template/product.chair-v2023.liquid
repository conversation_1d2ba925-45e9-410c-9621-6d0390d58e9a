{% if product.metafields.theme.impression_banner %}
  <div
    class="bt5-impression-head-banner"
    style="background-image: url({{ product.metafields.theme.impression_banner | img_url: 'master' }});"
  >
    <div class="bt5-impression-head-banner__content">
      <img
        src="{{ product.metafields.theme.product_logo_url }}"
        alt="{{ product.name }} logo"
        class="bt5-impression-head-banner__logo"
      >
    </div>
  </div>
{% endif %}
{% assign current_variant = product.selected_or_first_available_variant %}
<div class="product-notify-bar-wrap"></div>
<div data-section-id="{{ section.id }}" data-section-type="product-template">
  <meta itemprop="name" content="{{ product.title | downcase }} - {{ current_variant.title }}">
  <link itemprop="image" href="{{ current_variant.featured_image | img_url: 'grande' }}">
  <meta itemprop="description" content="{{ product.metafields.global.description_tag }}">
  <meta itemprop="sku" content="{{ current_variant.sku }}">
  <div itemprop="brand" itemtype="https://schema.org/Brand" itemscope>
    <meta itemprop="name" content="boulies">
  </div>
  <div itemprop="offers" itemtype="https://schema.org/Offer" itemscope>
    <link itemprop="url" href="{{ shop.secure_url }}{{ current_variant.url }}">
    <meta itemprop="itemCondition" content="https://schema.org/NewCondition">
    {% if current_variant.available
      and current_variant.metafields.Metadata[request.locale.endonym_name].pre_order.status
    %}
      <meta itemprop="availability" content="https://schema.org/PreOrder">
      <meta
        itemprop="availabilityStarts"
        content="{{ current_variant.metafields.Metadata[request.locale.endonym_name].pre_order.waves.last.ets }}"
      >
    {% elsif current_variant.available %}
      <meta itemprop="availability" content="https://schema.org/InStock">
    {% else %}
      <meta itemprop="availability" content="https://schema.org/OutOfStock">
    {% endif %}
    {% assign start = current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.start
      | date: '%s'
    %}
    {% assign end = current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.end | date: '%s' %}
    {% assign now = 'now' | date: '%s' %}
    {% if now > start and now < end %}
      <meta
        itemprop="price"
        content="{{ current_variant.price | minus: current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.discount | money_without_currency }}"
      >
    {% else %}
      <meta itemprop="price" content="{{ current_variant.price | money_without_currency }}">
    {% endif %}
    <meta itemprop="priceCurrency" content="{{ shop.currency }}">
    <div itemprop="shippingDetails" itemtype="https://schema.org/OfferShippingDetails" itemscope>
      <div itemprop="shippingRate" itemtype="https://schema.org/MonetaryAmount" itemscope>
        <meta itemprop="value" content="0">
        <meta itemprop="currency" content="{{ shop.currency }}">
      </div>
      <div itemprop="shippingDestination" itemtype="https://schema.org/DefinedRegion" itemscope>
        <meta itemprop="addressCountry" content="UK">
      </div>
      <div itemprop="deliveryTime" itemtype="https://schema.org/ShippingDeliveryTime" itemscope>
        <div itemprop="handlingTime" itemtype="https://schema.org/QuantitativeValue" itemscope>
          <meta itemprop="minValue" content="0">
          <meta itemprop="maxValue" content="1">
        </div>
        <div itemprop="transitTime" itemtype="https://schema.org/QuantitativeValue" itemscope>
          <meta itemprop="minValue" content="1">
          <meta itemprop="maxValue" content="5">
        </div>
        <div itemprop="businessDays" itemtype="https://schema.org/OpeningHoursSpecification" itemscope>
          <meta itemprop="dayOfWeek" content="https://schema.org/Monday">
          <meta itemprop="dayOfWeek" content="https://schema.org/Tuesday">
          <meta itemprop="dayOfWeek" content="https://schema.org/Wednesday">
          <meta itemprop="dayOfWeek" content="https://schema.org/Thursday">
        </div>
      </div>
    </div>
  </div>
  {% assign product_image_inventory = '' %}
  {% assign color_image_inventory = '' %}
  {% assign upholstery_image_inventory = '' %}
  {% for image in product.images %}
    {% if image.alt contains '[Color]' %}
      {% capture this_image %}<input type="hidden" class="color_image_storage" data-option="{{ image.alt | split: ']' | last }}" value="{{ image.src | img_url: 'master' }}" />
                {% endcapture %}
      {% capture color_image_inventory %}{{ color_image_inventory | append: this_image }}{% endcapture %}
    {% elsif image.alt contains '[Upholstery]' %}
      {% capture this_image %}<input type="hidden" class="upholstery_image_storage" data-option="{{ image.alt | split: ']' | last }}" value="{{ image.src | img_url: 'master' }}" />
                {% endcapture %}
      {% capture upholstery_image_inventory %}{{ upholstery_image_inventory | append: this_image }}{% endcapture %}
    {% else %}
      {% capture this_image %}<input type="hidden" class="image_storage_{{ image.alt }}" value="{{ image.src | img_url: 'master' }}" />
                {% endcapture %}
      {% capture product_image_inventory %}{{ product_image_inventory | append: this_image }}{% endcapture %}
    {% endif %}
  {% endfor %}
  <div class="bt20-collectionsShowBox">
    <div class="bt20-responseWrapper clearfix">
      <div class="bt20-csb_imageBox bt20-csb_loadingImage_p imageDragBanned">
        {% if product.metafields.custom.awards %}
          <div class="bt51-awards-list">
            {% for award in product.metafields.custom.awards.value %}
              <a
                {% if award.award_url %}
                  href="{{ award.award_url }}" target="_blank"
                {% endif %}
                class="bt51-award"
              >
                <img
                  src="{{ award.award_logo | img_url: 'x200' }}"
                  alt="{{ award.product_award }}"
                  class="bt51-award-image"
                >
              </a>
            {% endfor %}
          </div>
        {% endif %}
        <div class="bt20-csb_mainImage" id="bt32-csb_mainImage"></div>
        <div class="bt20-csb_sideList">
          <div class="bt20-csb_pswpContainer bt32-csb_gallerySider" id="bt32-csb_pswpContainer"></div>
        </div>
        <div class="image_storage_wrapper__bt40" style="display: none; visibility: hidden;">
          {{ product_image_inventory }}
          {{ upholstery_image_inventory }}
          {{ color_image_inventory }}
        </div>
      </div>
      <div class="bt20-csb_collectionsBox" id="bt20-collectionBuyBox">
        {% if product.metafields.theme.impression_banner %}
          <div
            class="bt5-impression-head-banner--sp"
            style="background-image: url({{ product.metafields.theme.impression_banner | img_url: 'master' }});"
          >
            <div class="bt5-impression-head-banner__content--sp">
              <img
                style=" transform: scale(0.7); "
                src="{{ product.metafields.theme.product_logo_url }}"
                alt="{{ product.name }} logo"
                class="bt5-impression-head-banner__logo--sp"
              >
            </div>
          </div>
        {% endif %}

        {% if product.metafields.theme.product_detail_page_url %}
          <a class="product_impression" href="{{ product.metafields.theme.product_detail_page_url }}">
            <div class="impression_video_background">
              <video
                poster=""
                autoplay="autoplay"
                muted=""
                loop=""
                x5-playsinline=""
                playsinline=""
                webkit-playsinline=""
                x-webkit-airplay=""
                x5-video-player-type="h5"
                x5-video-player-fullscreen=""
                x5-video-orientation="portraint"
              >
                <source src="{{ product.metafields.theme.impression_video_webm }}" type="video/webm">
                <source src="{{ product.metafields.theme.impression_video_h265 }}" type="video/mp4">
              </video>
            </div>
            <div class="impression_content">
              <img src="{{ product.metafields.theme.product_logo_url }}" alt="">
              <p>View detail about this chair.</p>
            </div>
          </a>
        {% endif %}

        <h1 class="product_title">
          {{ product.title }}
        </h1>
        <div class="bt20-csb_reviewStars">
          <div class="jdgm-widget jdgm-preview-badge" data-id="{{ product.id }}">
            {{ product.metafields.judgeme.badge }}
          </div>
        </div>
        <form action="/cart/add" method="post" enctype="multipart/form-data" id="bt20-addToCartForm">
          <input name="quantity" type="hidden" value="1">
          <input
            type="hidden"
            id="itemProperties_preOrderDescription"
            name="properties[Estimated_to_ship_out]"
            value="{{ current_variant.metafields.Metadata[request.locale.endonym_name].pre_order.waves.last.ets }}"
            {% unless current_variant.metafields.Metadata[request.locale.endonym_name].pre_order.status %}
              disabled
            {% endunless %}
          >
          <input
            type="hidden"
            id="itemProperties_warranty"
            name="properties[warranty]"
            value="{{ current_variant.metafields.Metadata[request.locale.endonym_name].warranty }}"
          >
          <input
            type="hidden"
            id="itemProperties_accessories"
            name="properties[accessories]"
            value="{{ current_variant.metafields.Metadata[request.locale.endonym_name].accessories }}"
          >
          <input
            type="hidden"
            id="cart_discount_code"
            name="attributes[cart_discount_code]"
            value="{{ current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.code }}"
          >
          <input
            type="hidden"
            id="cart_discount_amount"
            name="attributes[cart_discount_amount]"
            value="{{ current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.discount }}"
          >

          {% assign current_options = current_variant.title | split: ' / ' %}

          <div class="bt51-product-section--model">
            <h3 class="bt51-product-section-title">{{ product.options[0] }}</h3>
            <fieldset
              class="bt51-product-model-list"
              id="product-model-list"
              data-list="{{ product.options_by_name[product.options[0]].values | json }}"
              data-default-option="{{ current_variant.title }}"
              data-default-sku="{{ current_variant.sku }}"
            ></fieldset>
          </div>

          {% if product.metafields.custom.choose_guide %}
            {% assign metaobject = product.metafields.custom.choose_guide.value %}
            <div class="choose-guide-wrap__bt51" data-status="">
              <div class="choose-guide-header__bt51">
                {{ metaobject.display_name }}
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" width="15" class="cg-triangle__bt51">
                  <path d="M196.5-248 480-664l283.5 416h-567Z"></path>
                </svg>
              </div>
              <div class="choose-guide-body__bt51">
                {% if metaobject.tips %}
                  <div class="choose-guide-section__bt51">
                    <h4 class="choose-guide-section-title__bt51">Tips</h4>
                    <p class="choose-guide-content__bt51">{{ metaobject.tips | metafield_tag }}</p>
                  </div>
                {% endif %}
                <div class="choose-guide-section__bt51">
                  <h4 class="choose-guide-section-title__bt51">Size Recommendation</h4>
                  <div class="size-recommend-wrap__bt51">
                    {% for size in metaobject.guide_list.value %}
                      <div class="size-recommend__bt51">
                        <div class="mode-name__bt51">{{ size.model_name }}</div>
                        <div class="size-detail__bt51">
                          <div class="height-guide__bt51">
                            <div class="recommend-type__bt51">
                              <svg xmlns="http://www.w3.org/2000/svg" height="15" viewBox="0 -960 960 960" width="15">
                                <path fill="#576983" d="M480-134.782 310.107-304.674l65.588-66.654 57.804 58.305v-334.519l-57.804 59.435-66.153-66.653L480-825.218 650.458-654.76l-66.153 65.022-57.804-57.239v333.954l58.369-58.305 65.588 66.088L480-134.782Z"></path>
                              </svg>
                              HEIGHT
                            </div>
                            <div class="recommend-range__bt51">{{ size.height }}</div>
                          </div>
                          <div class="weight-guide__bt51">
                            <div class="recommend-type__bt51">
                              <svg xmlns="http://www.w3.org/2000/svg" height="15" viewBox="0 -960 960 960" width="15">
                                <path fill="#576983" d="M277.957-228.196h404.086L636.08-563.804H324.001l-46.044 335.608Zm202.282-428.609q18.9 0 31.754-13.093 12.855-13.093 12.855-31.993 0-18.901-12.882-31.755-12.881-12.854-31.922-12.854-18.618 0-31.755 12.881t-13.137 31.923q0 18.617 13.093 31.754 13.094 13.137 31.994 13.137Zm123.218 0h33.108q35.512 0 61.159 21.935 25.646 21.935 30.277 57.61l46.043 335.607q6.696 42.134-21.275 74.296-27.97 32.162-70.773 32.162H278.233q-42.804 0-70.888-32.162-28.085-32.162-21.389-74.296l46.043-335.607q4.631-35.675 30.277-57.61 25.647-21.935 61.159-21.935h33.108q-5.435-10.369-7.087-21.571-1.652-11.201-1.652-23.276 0-54.611 38.801-93.404t93.424-38.793q54.623 0 93.395 38.793t38.772 93.404q0 12.075-1.652 23.276-1.652 11.202-7.087 21.571Zm-325.5 428.609h404.086-404.086Z"></path>
                              </svg>
                              WEIGHT
                            </div>
                            <div class="recommend-range__bt51">{{ size.weight }}</div>
                          </div>
                        </div>
                      </div>
                    {% endfor %}
                  </div>
                  <a href="#product-dimensions" class="bt5-catalog__link--highlight">
                    Click to view detail specifications
                    <svg
                      class="bt5-highlight-link__arrow"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      version="1.1"
                      width="18"
                      height="18"
                      viewBox="0 0 19.5 16"
                    >
                      <g><path fill="#336CA8" transform="matrix(0,1,-1,0,19.5,-19.5)" d="M26.79289,0.292893C27.183419999999998,-0.0976311,27.816580000000002,-0.0976311,28.20711,0.292893C28.20711,0.292893,35.2071,7.29289,35.2071,7.29289C35.5976,7.68342,35.5976,8.31658,35.2071,8.70711C34.8166,9.09763,34.1834,9.09763,33.7929,8.70711C33.7929,8.70711,28.5,3.41421,28.5,3.41421C28.5,3.41421,28.5,18.5,28.5,18.5C28.5,19.0523,28.05228,19.5,27.5,19.5C26.94772,19.5,26.5,19.0523,26.5,18.5C26.5,18.5,26.5,3.41421,26.5,3.41421C26.5,3.41421,21.20711,8.70711,21.20711,8.70711C20.816580000000002,9.09763,20.183417,9.09763,19.792893,8.70711C19.4023689,8.31658,19.4023689,7.68342,19.792893,7.29289C19.792893,7.29289,26.79289,0.292893,26.79289,0.292893C26.79289,0.292893,26.79289,0.292893,26.79289,0.292893Z"></path></g>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          {% endif %}

          <div class="bt51-product-section--upholstery">
            <h3 class="bt51-product-section-title">{{ product.options[1] }}</h3>
            <fieldset class="bt51-product-upholstery-list" id="product-upholstery-list"></fieldset>
          </div>

          <div class="bt51-product-section--variant">
            <h3 class="bt51-product-section-title">{{ product.options[2] }}</h3>
            <fieldset class="bt51-product-variant-list">
              {% for variant in product.variants %}
                {% unless variant.metafields.extra_setting.hidden %}
                  {% assign options = variant.title | split: ' / ' %}
                  {% assign start = variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.start
                    | date: '%s'
                  %}
                  {% assign end = variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.end
                    | date: '%s'
                  %}
                  {% assign now = 'now' | date: '%s' %}
                  <input
                    {% if variant.id == current_variant.id %}
                      checked="checked"
                    {% endif %}
                    type="radio"
                    name="id"
                    class="bt51-variant"
                    data-variant-metadata="{{ variant.metafields.Metadata[request.locale.endonym_name] | json | escape }}"
                    data-available="{{ variant.available }}"
                    data-quantity="{{ variant.inventory_quantity }}"
                    data-price="{{ variant.price }}"
                    data-sale-status="{% if now > start and now < end %}true{% else %}false{% endif %}"
                    data-sale-discount="{{ variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.discount }}"
                    data-product-series="{{ product.title }}"
                    data-option1="{{ options[0] }}"
                    data-option2="{{ options[1] }}"
                    data-option3="{{ options[2] }}"
                    value="{{ variant.id }}"
                    id="{{ variant.sku }}"
                  >
                  <label
                    for="{{ variant.sku }}"
                    id="display_{{ variant.sku }}"
                    class="bt51-variant-display"
                    {% unless options[0] == current_options[0] and options[1] == current_options[1] %}
                      style="display:none;"
                    {% endunless %}
                    data-option1="{{ options[0] }}"
                    data-option2="{{ options[1] }}"
                    data-option3="{{ options[2] }}"
                    ><span class="bt51-color-name">{{ options[2] }}</span></label
                  >
                {% endunless %}
              {% endfor %}
            </fieldset>
          </div>

          <div class="bt20-csb_inventory bt20-csb_inStock" style="margin-bottom: 0;">
            <span class="bt5-icon-text bt20-csb_instock">
              <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                <path fill="#2a8c00" d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292c-12.7 17.7-39 17.7-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"></path>
              </svg>
              <span
                id="lowInventoryNotify"
                data-lin="{{ section.settings.low_inventory_notify }}"
                data-lin-trigger="{{ section.settings.low_inventory_notify_trigger }}"
                >In stock</span
              >. {{ section.settings.custom_notify -}}
            </span>
            <span class="bt5-icon-text bt20-csb_outstock">
              <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                <path fill="#777b74" d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z"></path>
              </svg>
              Sold Out</span
            >
            <span class="bt5-icon-text bt20-csb_checkstock">
              <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                <path fill="#777b74" d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zM694.5 432.7L481.9 725.4c-6.4 8.8-19.6 8.8-26 0l-126.4-174c-3.8-5.3 0-12.7 6.5-12.7h55.2c5.1 0 10 2.5 13 6.6l64.7 89 150.9-207.8c3-4.1 7.8-6.6 13-6.6H688c6.5 0.1 10.3 7.5 6.5 12.8z"></path>
              </svg>
              More on the way, pre-order now.</span
            >
          </div>
          <div class="bt20-csb_shippingInfo" style="font-size:12px;color:#2a8c00">
            <span id="bt40-csb_stockLocation" class="bt5-icon-text" style="margin:4px 0;">
              <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="14" height="14">
                <path fill="#2a8c00" d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m193.5 301.7l-210.6 292c-12.7 17.7-39 17.7-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"></path>
              </svg>
              Ships from Essex, United Kingdom.</span
            >
          </div>

          <div class="bt20-csb_promotions">
            <h3 class="bt51-product-section-title">Promotion</h3>
            <div
              class="bt32-csb_saleWraper"
              id="bt34-mainPromotion"
              data-startdate="{{ current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.start }}"
              data-enddate="{{ current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.end }}"
            >
              <div class="bps_saleBannerWrapper">
                <div
                  class="bps_saleBannerBg"
                  id="bps_saleBannerBg"
                  style="background-image: url({{ current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.image }})"
                ></div>
                <div class="bps_saleBannerTextWrapper">
                  <span class="bps_saleBannerHead" id="bt20-saleName">
                    {{- current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.name -}}
                  </span>
                  <span class="bps_saleBannerInfo" style="display:none;">
                    <span class="home-saleEndNotif" id="saleEndDate"> ENDS IN <span id="saleCounting"></span> </span>
                  </span>
                </div>
                <div class="bps_saleBannerCodeInfo">
                  <div class="bps_saleCodeInfo">
                    Use code "<span class="bt20-saleCode" id="bt20-saleCode">
                      {{- current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.code -}}</span
                    >" to get {{ cart.currency.symbol -}}
                    <span id="bt20-saleDiscount">
                      {{-
                        current_variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.discount
                        | divided_by: 100
                      -}}
                    </span>
                    off!
                  </div>
                </div>
              </div>
            </div>
            <div
              class="product_sub_promotion_wrapper__bt40"
              data-startdate="{{ current_variant.metafields.Metadata[request.locale.endonym_name].Sub_Promotion.start }}"
              data-enddate="{{ current_variant.metafields.Metadata[request.locale.endonym_name].Sub_Promotion.end }}"
            >
              <a
                id="sub_promotion_url__bt40"
                href="{{ current_variant.metafields.Metadata[request.locale.endonym_name].Sub_Promotion.url }}"
              >
                <img
                  id="sub_promotion_image__bt40"
                  src="{{ current_variant.metafields.Metadata[request.locale.endonym_name].Sub_Promotion.image }}"
                  alt="Sub promotion image"
                >
              </a>
            </div>

            <div class="bt20-prom_freeshipping clearfix">
              <div class="bt20-prom_fsIcon">
                <svg
                  style=" width: 60px; "
                  viewBox="0 0 47.7 28.97"
                  width="69px"
                  height="40px"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g> <path fill="#48545e" d="m14.11,0l-2.18,2.55V0H2.64v25.55h5.84v-.38c0-1.68,1.37-3.05,3.05-3.05s3.05,1.37,3.05,3.05v.38h21.1V0H14.11Zm20.79,24.78H15.32c-.19-1.92-1.82-3.43-3.79-3.43s-3.6,1.51-3.79,3.43H3.4V.76h7.77v3.85l3.29-3.85h20.44v24.02h0Z"></path> <path fill="#f6cd55" d="m13.28,1.56l-1.73,2.02V1.56h-7.36v22.43h2.85l.12-.34c.65-1.85,2.4-3.09,4.35-3.09s3.7,1.24,4.35,3.09l.12.34h18.12V1.56H13.28Z"></path> <path fill="#48545e" d="m44.33,8.26h-9.42v17.28h3.3v-.38c0-1.68,1.37-3.05,3.05-3.05s3.05,1.37,3.05,3.05v.38h3.4v-7.71s-3.37-9.58-3.37-9.58Zm2.6,16.52h-1.89c-.19-1.92-1.82-3.43-3.79-3.43s-3.6,1.51-3.79,3.43h-1.79v-15.75h8.12l3.14,8.94v6.81h0Zm-35.41-3.43c-2.1,0-3.81,1.71-3.81,3.81s1.71,3.81,3.81,3.81,3.81-1.71,3.81-3.81c0-2.1-1.71-3.81-3.81-3.81Zm0,6.86c-1.68,0-3.05-1.37-3.05-3.05s1.37-3.05,3.05-3.05,3.05,1.37,3.05,3.05-1.37,3.05-3.05,3.05Z"></path> <path fill="#48545e" d="m41.25,21.35c-2.1,0-3.81,1.71-3.81,3.81s1.71,3.81,3.81,3.81,3.81-1.71,3.81-3.81-1.71-3.81-3.81-3.81Zm0,6.86c-1.68,0-3.05-1.37-3.05-3.05s1.37-3.05,3.05-3.05,3.05,1.37,3.05,3.05-1.37,3.05-3.05,3.05Z"></path> <path fill="#eb8a6d" d="m41.25,22.91c-1.24,0-2.25,1.01-2.25,2.25s1.01,2.25,2.25,2.25,2.25-1.01,2.25-2.25-1.01-2.25-2.25-2.25Zm-29.73,0c-1.24,0-2.25,1.01-2.25,2.25s1.01,2.25,2.25,2.25,2.25-1.01,2.25-2.25-1.01-2.25-2.25-2.25Zm32.7-7.17l-1.42-4.13c-.08-.22-.28-.37-.52-.37h-4.34c-.3,0-.55.24-.55.55v4.13c0,.**********.55h5.76c.37,0,.64-.37.52-.72Z"></path> <path fill="#48545e" d="m0,10.86h22.16v.76H0v-.76Zm14.95,2.61h10.3v.76h-10.3v-.76Zm-7.81,3.05h16.54v.76H7.15v-.76Zm3.98-8.64h8.96v.76h-8.96v-.76Z"></path> </g>
                </svg>
              </div>
              <div class="bt20-prom_info">
                <!--
                  <span>Free Shipping</span><br/>
                  Basic shipping service is free when purchasing from boulies
                -->

                <!-- custom shipping text -->
                {% assign is_custom_product = false %}

                {% if section.settings.selected_products %}
                  {% for selected_product in section.settings.selected_products %}
                    {% if selected_product.id == product.id %}
                      {% assign is_custom_product = true %}
                      <span>{{ section.settings.custom_shipping_title }}</span><br>
                      {{ section.settings.custom_shipping_content }}
                    {% endif %}
                  {% endfor %}
                {% endif %}

                <!-- Default shipping title and content if no specific product is selected -->
                {% unless is_custom_product %}
                  <span>{{ section.settings.shipping_title }}</span><br>
                  {{ section.settings.shipping_content }}
                {% endunless %}
              </div>
            </div>
          </div>

          <div class="bt20-csb_pricesWrap" id="bt20-csb_pricesWrap">
            <div class="b5-csb_oldPrice" id="b5-collection__old_price"></div>
            <div class="bt20-csb_currentPrice" id="bt20-collection_price">{{ current_variant.price | money }}</div>
            <div class="bt20-csb_priceNote b5-regular-price-notify">Excluding Coupon Discounts</div>
            <div class="bt20-csb_priceNote b5-sale-price-notify">Price After Sale Code Applied</div>
            <div style="margin-top: 10px;">
              <klarna-placement
                id="Klarna_notify"
                data-key="credit-promotion-auto-size"
                data-locale="en-GB"
                data-purchase-amount="{{ current_variant.price }}"
              ></klarna-placement>
            </div>
          </div>

          <div class="bt20-csb_cart">
            <div class="bt20-csb_preOrderWindow">
              <div class="bt20-csb_sectionTitle" style="color: #b19c62;margin-left: 8px;">Preorder Status</div>
              <div class="bt20-csb_preOrderWrap"></div>
            </div>
            <button type="submit" id="bt20-csb_cartBtn" class="bt20-csb_activeCartBtn"></button>
          </div>
        </form>

        <!-- ================  notifyme section  ================ -->
        <div class="bt32-notifymeContainer" style="display: none;">
          <div class="bt32-notifymeBody">
            <div class="bt32-notifymeTitle">Be notified when restock</div>
            <input type="text" placeholder="Enter your email" id="bt32-notifyme_data">
          </div>
          <button id="bt32-notifymeBtn" class="bt32-notifymeBtn">Notify me</button>
        </div>

        <!-- ================  addon section  ================ -->
        <div class="bt32-addonContainer" id="bt32-addonSection">
          <h3 class="bt51-product-section-title">Add extra items or service</h3>
          <div class="bt32-addonLists"></div>
        </div>
      </div>
    </div>
  </div>
</div>

{% if product.metafields.custom.bestofs %}
  <div class="bt51-bestof-wrap">
    <div class="bt51-bestofs">
      {% for bestof in product.metafields.custom.bestofs.value %}
        <a class="bt51-bestof">
          <div class="bt51-bestof-name">
            Selected as
            <span class="bt51-bestof-title">{{ bestof.bestof_name }}</span>
          </div>
          <div class="bt51-media-review-quote">
            {{ bestof.review_quote }}
          </div>
          <div class="bt51-media-logo">
            <img src="{{ bestof.media_logo | img_url: 'master' }}" alt="media logo">
          </div>
        </a>
      {% endfor %}
    </div>
  </div>
{% endif %}

{{ product.description }}

<div style="background-color:#f6f6f6;overflow: hidden;">
  <div class="wrapper">
    <div class="productReviewWrap" id="bt20-collectionReviews">
      <ul class="bt20-reviewSwitcher">
        <li class="bt20-reviewTitle bt20-currentReviews">Customer Reviews</li>
      </ul>
      <div
        style="max-width: 1100px;margin: 0 auto;"
        id="judgeme_product_reviews"
        class="jdgm-widget jdgm-review-widget bt20-reviews"
        data-product-title="{{ product.title | escape }}"
        data-id="{{ product.id }}"
      >
        {{ product.metafields.judgeme.widget }}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Product settings",
  "class": "Product--gaming-chairs",
  "settings": [
    {
      "id": "low_inventory_notify",
      "type": "select",
      "label": "Low inventory notify type",
      "info": "是否在低库存时显示低库存警示？[Disable]:不启用低库存警示；[Text Warning]:仅文字告知低库存（Limited stock available.）。[Stock Number Warning]: 直接告知库存数量（X stock left, order soon.）。",
      "options": [
        {
          "value": "disable",
          "label": "Disable"
        },
        {
          "value": "text",
          "label": "Text Warning"
        },
        {
          "value": "number",
          "label": "Stock Number Warning"
        }
      ],
      "default": "disable"
    },
    {
      "id": "low_inventory_notify_trigger",
      "type": "number",
      "label": "Low inventory notify trigger number",
      "info": "低库存告示触发数量。",
      "default": 10
    },
    {
      "id": "custom_notify",
      "type": "text",
      "label": "Inventory customize notify for stock available items",
      "info": "自定义库存状态后的通知，默认无，仅显示在有库存的产品下，预售和缺货的产品下不显示。例如“Order now, arrive before Christmas”。"
    },

    {
      "id": "shipping_title",
      "type": "text",
      "label": "Shipping title",
      "info": "Apply to all products, e.g., 'Free Shipping'",
      "default": "Free Shipping"
    },
    {
      "id": "shipping_content",
      "type": "text",
      "label": "Shipping content",
      "info": "Apply to all products, e.g., 'Basic shipping service is free when purchasing from boulies'",
      "default": "Basic shipping service is free when purchasing from boulies"
    },
    {
      "id": "selected_products",
      "type": "product_list",
      "label": "Select Products",
      "info": "Choose the products that require custom shipping content."
    },
    {
      "id": "custom_shipping_title",
      "type": "text",
      "label": "Custom Shipping Title",
      "info": "Apply to selected products",
      "default": "Free Shipping"
    },
    {
      "id": "custom_shipping_content",
      "type": "text",
      "label": "Custom Shipping Content",
      "info": "Apply to selected products",
      "default": "Basic shipping service is free when purchasing from boulies"
    }
  ],
  "presets": [
    {
      "name": "Product[Gaming Chair]",
      "category": "product settings"
    }
  ]
}
{% endschema %}

{% javascript %}
{% endjavascript %}
