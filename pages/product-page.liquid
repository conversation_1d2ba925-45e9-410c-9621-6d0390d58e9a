{% assign products_with_upholstery_info = section.settings.products_with_upholstery_info %}
{% unless products_with_upholstery_info contains product.id %}
  <div>
    <p>This section is visible for this product.</p>
  </div>
{% endunless %}

{% assign products_with_seat_type_info = section.settings.products_with_seat_type_info %}
{% unless products_with_seat_type_info contains product.id %}
  <div>
    <p>This section is visible for this product.</p>
  </div>
{% endunless %}

{% schema %}
{
  "name": "Custom Section",
  "settings": [
    {
      "id": "products_with_upholstery_info",
      "type": "product_list",
      "label": "Products with Upholstery Info",
      "info": "Select products that need to show the upholstery information section"
    },
    {
      "id": "products_with_seat_type_info",
      "type": "product_list",
      "label": "Products with Seat Type Info",
      "info": "Select products that need to show the seat type information section"
    }
  ]
}
{% endschema %}
