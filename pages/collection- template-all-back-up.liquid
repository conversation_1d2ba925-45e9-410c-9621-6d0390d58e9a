{% assign filter_series = '' %}
{% assign filter_upholstery = '' %}
{% assign filter_colour = '' %}
{% for block in section.blocks %}
  {% if block.type == 'filter_option_series' %}
    {% capture filter_series %}{{ filter_series }}<div class="filter_option__bt37" data-filter-type="series" data-filter-value="{{ block.settings.value }}" >{{ block.settings.present }}</div>{% endcapture %}
  {% endif %}
  {% if block.type == 'filter_option_material' %}
    {% capture filter_upholstery %}{{ filter_upholstery }}<div class="filter_option__bt37" data-filter-type="upholstery" data-filter-value="{{ block.settings.value }}" >{{ block.settings.present }}</div>{% endcapture %}
  {% endif %}
  {% if block.type == 'filter_option_colour' %}
    {% capture filter_colour %}{{ filter_colour }}<div class="filter_option__bt37" data-filter-type="colour" data-filter-value="{{ block.settings.value }}" >{{ block.settings.present }}</div>{% endcapture %}
  {% endif %}
{% endfor %}
<div class="collection_header__bt37">
  <div class="collection_header-title__bt37">
    <p class="collection_header-text__bt37"><span>Boulies</span> Collections</p>
  </div>
  <div
    class="collection_header-banner__bt37"
    data-spbanner="{{ settings.promotion_image_sp }}"
    data-pcbanner="{{ settings.promotion_image_pc  | replace: '_x260', '_x350' }}"
    data-start="{{ settings.promotion_start_date }}"
    data-end="{{ settings.promotion_end_date }}"
  >
    <img
      id="collection_banner_image"
      style="display:none;"
      data-default-banner="{{ section.settings.banner_image.src | img_url: 'master' }}"
      src=""
      alt="Boulies Collections"
      crossOrigin="Anonymous"
    >
    <div class="home-saleBannerContent">
      <p class="home-saleBannerInfo" style="color:white;margin: 0;">
        <span class="bt5-sale-note">{{ settings.promotion_content }}</span>
        <span class="home-saleEndNotif" id="saleEndDate">ENDS IN <span id="saleCounting"></span></span>
      </p>
    </div>
  </div>
</div>
<div class="filter_wrap__bt37">
  <div class="filter_list__bt37">
    <div class="filter_section__bt37">
      <div class="filter_present__bt37">
        <div class="filter_section_name__bt37">Series</div>
        <div class="filter_actived__bt37" data-filter-type="series" data-actived="all">All</div>
      </div>
      <div class="filter_select__bt37">
        {{ filter_series }}
        <div class="filter_option__bt37" data-filter-type="series" data-filter-value="all">All</div>
      </div>
    </div>
    <div class="filter_section__bt37">
      <div class="filter_present__bt37">
        <div class="filter_section_name__bt37">Upholstery</div>
        <div class="filter_actived__bt37" data-filter-type="upholstery" data-actived="all">All</div>
      </div>
      <div class="filter_select__bt37">
        {{ filter_upholstery }}
        <div class="filter_option__bt37" data-filter-type="upholstery" data-filter-value="all">All</div>
      </div>
    </div>
    <div class="filter_section__bt37">
      <div class="filter_present__bt37">
        <div class="filter_section_name__bt37">Color</div>
        <div class="filter_actived__bt37" data-filter-type="colour" data-actived="all">All</div>
      </div>
      <div class="filter_select__bt37">
        {{ filter_colour }}
        <div class="filter_option__bt37" data-filter-type="colour" data-filter-value="all">All</div>
      </div>
    </div>
  </div>
  <div class="filter_list__bt37">
    <div class="filter_section__bt37">
      <div class="filter_present__bt37">
        <div class="filter_section_name__bt37">Availability</div>
        <div class="filter_actived__bt37" data-filter-type="availability" data-actived="all">All</div>
      </div>
      <div class="filter_select__bt37">
        <div class="filter_option__bt37" data-filter-type="availability" data-filter-value="true">In Stock</div>
        <div class="filter_option__bt37" data-filter-type="availability" data-filter-value="pre_order">Pre Order</div>
        <div class="filter_option__bt37" data-filter-type="availability" data-filter-value="false">Out of Stock</div>
        <div class="filter_option__bt37" data-filter-type="availability" data-filter-value="all">All</div>
      </div>
    </div>
    <div class="filter_section__bt37">
      <div class="filter_present__bt37">
        <div class="filter_section_name__bt37">Promotion</div>
        <div class="filter_actived__bt37" data-filter-type="promotion_status" data-actived="all">All</div>
      </div>
      <div class="filter_select__bt37">
        <div class="filter_option__bt37" data-filter-type="promotion_status" data-filter-value="true">On Sale</div>
        <div class="filter_option__bt37" data-filter-type="promotion_status" data-filter-value="false">Not on Sale</div>
        <div class="filter_option__bt37" data-filter-type="promotion_status" data-filter-value="all">All</div>
      </div>
    </div>
    <div class="filter_section__bt37">
      <div class="filter_present__bt37">
        <div class="filter_section_name__bt37">Sort by</div>
        <div class="filter_actived__bt37" data-filter-type="sort" data-actived="popular">Popular</div>
      </div>
      <div class="filter_select__bt37">
        <div class="filter_option__bt37" data-filter-type="sort" data-filter-value="price_ascending">
          Price: low-high
        </div>
        <div class="filter_option__bt37" data-filter-type="sort" data-filter-value="price_descending">
          Price: high-low
        </div>
        <div class="filter_option__bt37" data-filter-type="sort" data-filter-value="popular">Popular</div>
      </div>
    </div>
  </div>
</div>
<span class="filter_switcher__bt40">
  <i class="iconFont icon-search" style="font-size: 0.9em;margin-right: 0.1em;font-weight:700;"></i>
  <span class="filter_notify__bt40">Open filter</span>
</span>
<div class="products_container__bt37" id="products_container__bt37"></div>
<script>
  var products_list = [];
  {% paginate collection.products by 1000 %}
      {% for product in collection.products %}
          {% if product.type == 'Chair' %}
              {% for variant in product.variants %}
  		{% unless variant.metafields.extra_setting.hidden %}
              {% assign start = variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.start | date: '%s' %}
              {% assign end = variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.end | date: '%s' %}
              {% assign now = 'now' | date: '%s' %}
              products_list.push({ title: '{{ product.title }}', url: '{{ variant.url }}', image: '{{ variant.image | img_url: '200x' }}', series: '{{ product.handle }}', model:'{{ variant.option1 }}', upholstery: '{{ variant.option2 }}', colour: '{{ variant.option3 }}', availability: {{ variant.available }}, price: {{ variant.price }}, promotion_start: '{{ variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.start }}', promotion_end: '{{ variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.end }}', promotion_price: {{ variant.price | minus: variant.metafields.Metadata[request.locale.endonym_name].Main_Promotion.discount }},promotion_status:{% if now > start and now < end %}true{% else %}false{% endif %}, pre_order_status: {% if variant.metafields.Metadata[request.locale.endonym_name].pre_order.status %}{{ variant.metafields.Metadata[request.locale.endonym_name].pre_order.status }}{% else %}false{% endif %} });
              {% endunless %}
  		{% endfor %}
          {% endif %}
      {% endfor %}
  {% endpaginate %}
</script>

{% schema %}
{
  "name": "Collection - all prodcuts",
  "settings": [
    {
      "id": "banner_image",
      "type": "image_picker",
      "label": "Banner Image"
    }
  ],
  "blocks": [
    {
      "type": "filter_option_series",
      "name": "Filter option - Series",
      "settings": [
        {
          "id": "present",
          "type": "text",
          "label": "Present Text",
          "placeholder": "Master Series",
          "info": "此处填写'Series'下拉菜单内的选项，客人会看到，此处无填写要求，建议所有选项都统一，例如要添加Master系列选项，此处可填：Master Series。"
        },
        {
          "id": "value",
          "type": "text",
          "label": "Filter Value",
          "placeholder": "master-series",
          "info": "此处填写该'Series'选项对应的产品的URL handle，例如Master系列产品链接为：boulies.co.uk/products/master-series。则此处应该填写：master-series。"
        }
      ]
    },
    {
      "type": "filter_option_material",
      "name": "Filter option - Material",
      "settings": [
        {
          "id": "present",
          "type": "text",
          "label": "Present Text",
          "placeholder": "Ultraflex PU",
          "info": "此处填写'Material'下拉菜单内的选项，客人会看到，此处无填写要求。"
        },
        {
          "id": "value",
          "type": "text",
          "label": "Filter Value",
          "placeholder": "Ultraflex PU",
          "info": "此处填写该'Material'选项对应的材质名称。注意，一定要和现有产品的材质名称完全一致，如产品Variant选项内的Material填的'Ultraflex PU'，则此处也得填这个，大小写，空格都不能更改。"
        }
      ]
    },
    {
      "type": "filter_option_colour",
      "name": "Filter option - Colour",
      "settings": [
        {
          "id": "present",
          "type": "text",
          "label": "Present Text",
          "placeholder": "Black",
          "info": "此处填写'colour'下拉菜单内的选项，客人会看到，此处无填写要求。"
        },
        {
          "id": "value",
          "type": "text",
          "label": "Filter Value",
          "placeholder": "Black,Charcoal",
          "info": "此处填写该'colour'选项对应的所有颜色，用半角逗号','分开，不要加空格。注意，一定要和现有产品的颜色名称完全一致，如产品Variant选项内的Color填的'Black'，则此处也得填这个，大小写不能改。\n!!!注意!!!\n 示例如果此处填写'Black,Charcoal'，则在客人选择'Black'颜色选项时，Variant的颜色为'Black'和'Charcoal'的产品都会出现。如果想要一款产品在多个颜色选项下出现，可在多个选项内添加。"
        }
      ]
    }
  ],
  "presets": []
}
{% endschema %}
