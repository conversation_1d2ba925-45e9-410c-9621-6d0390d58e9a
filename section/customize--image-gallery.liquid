<!-- Slick.js CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.css">

<style>
  html {
    scroll-behavior: smooth;
  }

  .customize-image-gallery {
    width: 100%;
    max-width: 1920px;
    position: relative;
    margin: 0 auto;
  }

  .cig-content {
    margin-left: auto;
    max-width: 1640px;
  }

  .cig-item-1 {
    position: relative;
    margin-right: 48px;
  }

  .cig-item-2 {
    position: relative;
    margin-right: 48px;
  }

  .cig-item-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    opacity: 1;
    transition: all 0.3s ease-in-out;
  }

  .cig-item-arrow.hide {
    opacity: 0;
    pointer-events: none;
  }

  .cig-item-arrow:hover {
    opacity: 0.7;
  }

  .cig-item-arrow.right {
    right: 7%;
  }

  .cig-item-arrow.left {
    left: 7%;
  }

  .cig-item-1-container {
    position: absolute;
    top: 0;
    left: 0;
  }

  .cig-item-1-left-btn {
    border: 0;
    background: none;
    position: absolute;
    top: 48.13%;
    left: 25.59%;
    padding: 0;
    cursor: pointer;
    opacity: 1;
    transition: all 0.3s ease-in-out;
  }

  .cig-item-1-right-btn {
    border: 0;
    background: none;
    position: absolute;
    top: 66.83%;
    left: 65.15%;
    padding: 0;
    cursor: pointer;
    opacity: 1;
    transition: all 0.3s ease-in-out;
  }

  .cig-item-1-left-btn:hover,
  .cig-item-1-right-btn:hover {
    opacity: 0.7;
  }

  .cig-item-1-left-popover {
    background-color: #fff;
    position: absolute;
    top: 32.8%;
    left: 10.59%;
    border-radius: 10px;
    transition: all 0.3s ease-in-out;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }

  .cig-item-1-left-popover.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }

  .cig-item-1-right-popover {
    background-color: #fff;
    position: absolute;
    top: 51.47%;
    left: 65.15%;
    border-radius: 10px;
    transition: all 0.3s ease-in-out;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }

  .cig-item-1-right-popover.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }

  .cig-item-1-left-popover-content,
  .cig-item-1-right-popover-content,
  .cig-item-2-left-popover-content,
  .cig-item-2-right-popover-content {
    padding: 16px 53px 24px 24px;
  }

  .cig-item-1-left-popover-content-title,
  .cig-item-1-right-popover-content-title,
  .cig-item-2-left-popover-content-title,
  .cig-item-2-right-popover-content-title {
    font-size: 24px;
    font-weight: 700;
    color: #141414;
  }

  .cig-item-1-left-popover-content-link,
  .cig-item-1-right-popover-content-link,
  .cig-item-2-left-popover-content-link,
  .cig-item-2-right-popover-content-link {
    font-size: 18px;
    font-weight: 500;
    margin-top: 8px;
    display: block;
  }

  .cig-item-2-left-btn {
    border: 0;
    background: none;
    position: absolute;
    top: 54.27%;
    left: 36.25%;
    padding: 0;
    cursor: pointer;
    opacity: 1;
    transition: all 0.3s ease-in-out;
  }

  .cig-item-2-right-btn {
    border: 0;
    background: none;
    position: absolute;
    top: 44.67%;
    left: 67.5%;
    padding: 0;
    cursor: pointer;
    opacity: 1;
    transition: all 0.3s ease-in-out;
  }

  .cig-item-2-left-btn:hover,
  .cig-item-2-right-btn:hover {
    opacity: 0.7;
  }

  .cig-item-2-left-popover {
    background-color: #fff;
    position: absolute;
    top: 38.93%;
    left: 21.25%;
    border-radius: 10px;
    transition: all 0.3s ease-in-out;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }

  .cig-item-2-left-popover.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }

  .cig-item-2-right-popover {
    background-color: #fff;
    position: absolute;
    top: 29.33%;
    left: 67.5%;
    border-radius: 10px;
    opacity: 0;
    transition: all 0.3s ease-in-out;
    visibility: hidden;
    pointer-events: none;
  }

  .cig-item-2-right-popover.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }

  @media (max-width: 1440px) {
    .cig-item-1 img,
    .cig-item-2 img {
      max-width: 1200px;
      height: auto;
    }

    .cig-item-1 svg,
    .cig-item-2 svg {
      width: 48px;
      height: 48px;
    }
  }

  @media (max-width: 1200px) {
    .cig-item-1 img,
    .cig-item-2 img {
      max-width: 900px;
    }

    .cig-item-1 svg,
    .cig-item-2 svg {
      width: 36px;
      height: 36px;
    }

    .cig-item-1-left-popover-content,
    .cig-item-1-right-popover-content,
    .cig-item-2-left-popover-content,
    .cig-item-2-right-popover-content {
      padding: 8px 26px 12px 12px;
    }

    .cig-item-1-left-popover {
      top: 29.8%;
    }

    .cig-item-1-right-popover {
      top: 48.47%;
    }

    .cig-item-2-left-popover {
      top: 34.93%;
    }

    .cig-item-2-right-popover {
      top: 26.33%;
    }
  }

  @media (max-width: 900px) {
    .cig-item-1 img,
    .cig-item-2 img {
      max-width: 650px;
    }

    .cig-item-1 svg,
    .cig-item-2 svg {
      width: 24px;
      height: 24px;
    }

    .cig-item-1-left-popover {
      top: 27.8%;
    }

    .cig-item-1-right-popover {
      top: 46.47%;
    }

    .cig-item-2-left-popover {
      top: 32.93%;
    }

    .cig-item-2-right-popover {
      top: 24.33%;
    }

    .cig-item-1-left-popover-content,
    .cig-item-1-right-popover-content,
    .cig-item-2-left-popover-content,
    .cig-item-2-right-popover-content {
      padding: 8px 20px 12px 12px;
    }

    .cig-item-1-left-popover-content-title,
    .cig-item-1-right-popover-content-title,
    .cig-item-2-left-popover-content-title,
    .cig-item-2-right-popover-content-title {
      font-size: 20px;
    }

    .cig-item-1-left-popover-content-link,
    .cig-item-1-right-popover-content-link,
    .cig-item-2-left-popover-content-link,
    .cig-item-2-right-popover-content-link {
      font-size: 16px;
      margin-top: 4px;
    }
  }

  @media (max-width: 650px) {
    .cig-content {
      margin-left: 4.17vw;
    }

    .cig-item-1,
    .cig-item-2 {
      margin-right: 4vw;
    }

    .cig-item-1 img,
    .cig-item-2 img {
      width: 80vw;
      height: auto;
    }

    .cig-item-1 svg,
    .cig-item-2 svg {
      width: 6.67vw;
      height: 6.67vw;
    }

    .cig-item-1-left-popover-content,
    .cig-item-1-right-popover-content,
    .cig-item-2-left-popover-content,
    .cig-item-2-right-popover-content {
      padding: 2.67vw 13.33vw 4vw 4vw;
    }

    .cig-item-1-left-popover-content-title,
    .cig-item-1-right-popover-content-title,
    .cig-item-2-left-popover-content-title,
    .cig-item-2-right-popover-content-title {
      font-size: 4vw;
    }

    .cig-item-1-left-popover-content-link,
    .cig-item-1-right-popover-content-link,
    .cig-item-2-left-popover-content-link,
    .cig-item-2-right-popover-content-link {
      font-size: 3vw;
      margin-top: 1.33vw;
    }

    .cig-item-arrow {
      display: none;
    }

    .cig-item-1-left-btn {
      top: 39.33%;
      left: 24.05%;
    }

    .cig-item-1-left-popover {
      top: 19.5%;
      left: 16.43%;
    }

    .cig-item-1-right-btn {
      top: 53.83%;
      left: 69.76%;
    }

    .cig-item-1-right-popover {
      top: 64.83%;
      left: 22.76%;
    }

    .cig-item-2-left-btn {
      top: 52.5%;
      left: 25.48%;
    }

    .cig-item-2-left-popover {
      top: 33%;
      left: 25.48%;
    }

    .cig-item-2-right-btn {
      top: 60.5%;
      left: 76.43%;
    }

    .cig-item-2-right-popover {
      top: 71.5%;
      left: 27.43%;
    }

    .cig-item-1-left-btn:hover,
    .cig-item-1-right-btn:hover {
      opacity: 1;
    }

    .cig-item-1-left-btn:active,
    .cig-item-1-right-btn:active {
      opacity: 0.7;
    }

    .cig-item-2-left-btn:hover,
    .cig-item-2-right-btn:hover {
      opacity: 1;
    }

    .cig-item-2-left-btn:active,
    .cig-item-2-right-btn:active {
      opacity: 0.7;
    }
  }
</style>

<div class="customize-image-gallery">
  <div class="cig-content" id="cig-content">
    <div class="cig-item-1">
      <picture>
        <source
          srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/home_office-sp_600x.jpg?v=1742523449"
          media="(max-width: 650px)"
        >
        <img
          src="https://cdn.shopify.com/s/files/1/2127/6275/files/home_office_1360x.jpg?v=1742523437"
          alt="home office"
          width="auto"
          height="auto"
        >
      </picture>
      <button class="cig-item-1-left-btn">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          fill="none"
          version="1.1"
          width="56"
          height="56"
          viewBox="0 0 56 56"
        >
          <g>
            <g>
              <ellipse cx="28" cy="28" rx="28" ry="28" fill="#FFFFFF" fill-opacity="0.6000000238418579" />
            </g>
            <g>
              <ellipse cx="28" cy="28" rx="20" ry="20" fill="#FFFFFF" fill-opacity="1" />
            </g>
            <g>
              <path
                d="M35.603,26.51117Q36,26.51117,36,26.90819L36,29.21092Q36,29.60794,35.603,29.60794L29.72705,29.60794Q29.56824,29.60794,29.56824,29.766750000000002L29.56824,35.603Q29.56824,36,29.171219999999998,36L26.86849,36Q26.47146,36,26.47146,35.603L26.47146,29.766750000000002Q26.47146,29.60794,26.31266,29.60794L20.397022,29.60794Q20,29.60794,20,29.21092L20,26.90819Q20,26.51117,20.397022,26.51117L26.31266,26.51117Q26.47146,26.51117,26.47146,26.35236L26.47146,20.397022Q26.47146,20,26.86849,20L29.171219999999998,20Q29.56824,20,29.56824,20.397022L29.56824,26.35236Q29.56824,26.51117,29.72705,26.51117L35.603,26.51117Z"
                fill="#000000" fill-opacity="1" />
            </g>
          </g>
        </svg>
      </button>
      <div class="cig-item-1-left-popover">
        <div class="cig-item-1-left-popover-content">
          <div class="cig-item-1-left-popover-content-title">Boulies Master Rex</div>
          <a
            href="/products/master-rex?variant=46515031933171"
            target="_blank"
            class="cig-item-1-left-popover-content-link"
            >Learn More</a
          >
        </div>
      </div>
      <button class="cig-item-1-right-btn">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          fill="none"
          version="1.1"
          width="56"
          height="56"
          viewBox="0 0 56 56"
        >
          <g>
            <g>
              <ellipse cx="28" cy="28" rx="28" ry="28" fill="#FFFFFF" fill-opacity="0.6000000238418579" />
            </g>
            <g>
              <ellipse cx="28" cy="28" rx="20" ry="20" fill="#FFFFFF" fill-opacity="1" />
            </g>
            <g>
              <path
                d="M35.603,26.51117Q36,26.51117,36,26.90819L36,29.21092Q36,29.60794,35.603,29.60794L29.72705,29.60794Q29.56824,29.60794,29.56824,29.766750000000002L29.56824,35.603Q29.56824,36,29.171219999999998,36L26.86849,36Q26.47146,36,26.47146,35.603L26.47146,29.766750000000002Q26.47146,29.60794,26.31266,29.60794L20.397022,29.60794Q20,29.60794,20,29.21092L20,26.90819Q20,26.51117,20.397022,26.51117L26.31266,26.51117Q26.47146,26.51117,26.47146,26.35236L26.47146,20.397022Q26.47146,20,26.86849,20L29.171219999999998,20Q29.56824,20,29.56824,20.397022L29.56824,26.35236Q29.56824,26.51117,29.72705,26.51117L35.603,26.51117Z"
                fill="#000000" fill-opacity="1" />
            </g>
          </g>
        </svg>
      </button>
      <div class="cig-item-1-right-popover">
        <div class="cig-item-1-right-popover-content">
          <div class="cig-item-1-right-popover-content-title">Boulies Magvida</div>
          <a
            href="/products/magvida?variant=46750762860787"
            target="_blank"
            class="cig-item-1-right-popover-content-link"
            >Learn More</a
          >
        </div>
      </div>
    </div>

    <div class="cig-item-2">
      <picture>
        <source
          srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/gaming_room-sp_600x.jpg?v=1742523445"
          media="(max-width: 650px)"
        >
        <img
          src="https://cdn.shopify.com/s/files/1/2127/6275/files/gaming_room_1360x.jpg?v=1742523440"
          alt="gaming room"
          width="auto"
          height="auto"
        >
      </picture>

      <button class="cig-item-2-left-btn">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          fill="none"
          version="1.1"
          width="56"
          height="56"
          viewBox="0 0 56 56"
        >
          <g>
            <g>
              <ellipse cx="28" cy="28" rx="28" ry="28" fill="#FFFFFF" fill-opacity="0.6000000238418579" />
            </g>
            <g>
              <ellipse cx="28" cy="28" rx="20" ry="20" fill="#FFFFFF" fill-opacity="1" />
            </g>
            <g>
              <path
                d="M35.603,26.51117Q36,26.51117,36,26.90819L36,29.21092Q36,29.60794,35.603,29.60794L29.72705,29.60794Q29.56824,29.60794,29.56824,29.766750000000002L29.56824,35.603Q29.56824,36,29.171219999999998,36L26.86849,36Q26.47146,36,26.47146,35.603L26.47146,29.766750000000002Q26.47146,29.60794,26.31266,29.60794L20.397022,29.60794Q20,29.60794,20,29.21092L20,26.90819Q20,26.51117,20.397022,26.51117L26.31266,26.51117Q26.47146,26.51117,26.47146,26.35236L26.47146,20.397022Q26.47146,20,26.86849,20L29.171219999999998,20Q29.56824,20,29.56824,20.397022L29.56824,26.35236Q29.56824,26.51117,29.72705,26.51117L35.603,26.51117Z"
                fill="#000000" fill-opacity="1" />
            </g>
          </g>
        </svg>
      </button>
      <div class="cig-item-2-left-popover">
        <div class="cig-item-2-left-popover-content">
          <div class="cig-item-2-left-popover-content-title">Boulies Master Rex</div>
          <a
            href="/products/master-rex?variant=46515029410035"
            target="_blank"
            class="cig-item-2-left-popover-content-link"
            >Learn More</a
          >
        </div>
      </div>

      <button class="cig-item-2-right-btn">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          fill="none"
          version="1.1"
          width="56"
          height="56"
          viewBox="0 0 56 56"
        >
          <g>
            <g>
              <ellipse cx="28" cy="28" rx="28" ry="28" fill="#FFFFFF" fill-opacity="0.6000000238418579" />
            </g>
            <g>
              <ellipse cx="28" cy="28" rx="20" ry="20" fill="#FFFFFF" fill-opacity="1" />
            </g>
            <g>
              <path
                d="M35.603,26.51117Q36,26.51117,36,26.90819L36,29.21092Q36,29.60794,35.603,29.60794L29.72705,29.60794Q29.56824,29.60794,29.56824,29.766750000000002L29.56824,35.603Q29.56824,36,29.171219999999998,36L26.86849,36Q26.47146,36,26.47146,35.603L26.47146,29.766750000000002Q26.47146,29.60794,26.31266,29.60794L20.397022,29.60794Q20,29.60794,20,29.21092L20,26.90819Q20,26.51117,20.397022,26.51117L26.31266,26.51117Q26.47146,26.51117,26.47146,26.35236L26.47146,20.397022Q26.47146,20,26.86849,20L29.171219999999998,20Q29.56824,20,29.56824,20.397022L29.56824,26.35236Q29.56824,26.51117,29.72705,26.51117L35.603,26.51117Z"
                fill="#000000" fill-opacity="1" />
            </g>
          </g>
        </svg>
      </button>
      <div class="cig-item-2-right-popover">
        <div class="cig-item-2-right-popover-content">
          <div class="cig-item-2-right-popover-content-title">Boulies Magvida</div>
          <a
            href="/products/magvida?variant=46750762828019"
            target="_blank"
            class="cig-item-2-right-popover-content-link"
            >Learn More</a
          >
        </div>
      </div>
    </div>
  </div>

  <!-- arrow btn -->
  <div class="cig-item-arrow right" id="cig-item-arrow-right">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      fill="none"
      version="1.1"
      width="64"
      height="64"
      viewBox="0 0 64 64"
    >
      <g>
        <g>
          <g>
            <g>
              <path
                d="M32.000043505859374,5.33349609375C17.306643505859377,5.33349609375,5.333343505859375,17.306796093750002,5.333343505859375,32.00019609375C5.333343505859375,46.69349609375,17.306643505859377,58.66679609375,32.000043505859374,58.66679609375C46.693343505859374,58.66679609375,58.666643505859376,46.69349609375,58.666643505859376,32.00019609375C58.666643505859376,17.306796093750002,46.693343505859374,5.33349609375,32.000043505859374,5.33349609375C32.000043505859374,5.33349609375,32.000043505859374,5.33349609375,32.000043505859374,5.33349609375ZM39.44004350585937,33.41349609375C39.44004350585937,33.41349609375,30.026643505859376,42.82679609375,30.026643505859376,42.82679609375C29.626643505859374,43.22679609375,29.120043505859375,43.41349609375,28.613343505859376,43.41349609375C28.106643505859374,43.41349609375,27.600043505859375,43.22679609375,27.200043505859377,42.82679609375C26.426643505859374,42.05349609375,26.426643505859374,40.77349609375,27.200043505859377,40.00019609375C27.200043505859377,40.00019609375,35.20004350585938,32.00019609375,35.20004350585938,32.00019609375C35.20004350585938,32.00019609375,27.200043505859377,24.00019609375,27.200043505859377,24.00019609375C26.426643505859374,23.22679609375,26.426643505859374,21.94679609375,27.200043505859377,21.17349609375C27.973343505859376,20.40019609375,29.253343505859377,20.40019609375,30.026643505859376,21.17349609375C30.026643505859376,21.17349609375,39.44004350585937,30.58679609375,39.44004350585937,30.58679609375C40.240043505859376,31.36019609375,40.240043505859376,32.64019609375,39.44004350585937,33.41349609375C39.44004350585937,33.41349609375,39.44004350585937,33.41349609375,39.44004350585937,33.41349609375Z"
                fill="#000000" fill-opacity="1" />
            </g>
            <g transform="matrix(-1,0,0,-1,128,128)" style="opacity:0;"></g>
          </g>
        </g>
      </g>
    </svg>
  </div>
  <div class="cig-item-arrow left hide" id="cig-item-arrow-left">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      fill="none"
      version="1.1"
      width="64.0009765625"
      height="64.000244140625"
      viewBox="0 0 64.0009765625 64.000244140625"
    >
      <g transform="matrix(-1,0,0,-1,128.001953125,128)">
        <g>
          <g>
            <g>
              <path
                d="M96.00117265624999,69.33332061767578C81.30777265625,69.33332061767578,69.33447265625,81.30662061767578,69.33447265625,96.00002061767577C69.33447265625,110.69332061767578,81.30777265625,122.66662061767579,96.00117265624999,122.66662061767579C110.69447265625,122.66662061767579,122.66777265625001,110.69332061767578,122.66777265625001,96.00002061767577C122.66777265625001,81.30662061767578,110.69447265625,69.33332061767578,96.00117265624999,69.33332061767578C96.00117265624999,69.33332061767578,96.00117265624999,69.33332061767578,96.00117265624999,69.33332061767578ZM103.44117265624999,97.41332061767578C103.44117265624999,97.41332061767578,94.02777265625,106.82662061767579,94.02777265625,106.82662061767579C93.62777265625,107.22662061767579,93.12117265625,107.41332061767578,92.61447265625,107.41332061767578C92.10777265625,107.41332061767578,91.60117265625,107.22662061767579,91.20117265625001,106.82662061767579C90.42777265625,106.05332061767578,90.42777265625,104.77332061767578,91.20117265625001,104.00002061767577C91.20117265625001,104.00002061767577,99.20117265625001,96.00002061767577,99.20117265625001,96.00002061767577C99.20117265625001,96.00002061767577,91.20117265625001,88.00002061767577,91.20117265625001,88.00002061767577C90.42777265625,87.22662061767578,90.42777265625,85.94662061767578,91.20117265625001,85.17332061767578C91.97447265625,84.40002061767578,93.25447265625,84.40002061767578,94.02777265625,85.17332061767578C94.02777265625,85.17332061767578,103.44117265624999,94.58662061767578,103.44117265624999,94.58662061767578C104.24117265625,95.36002061767579,104.24117265625,96.64002061767579,103.44117265624999,97.41332061767578C103.44117265624999,97.41332061767578,103.44117265624999,97.41332061767578,103.44117265624999,97.41332061767578Z"
                fill="#000000" fill-opacity="0.6000000238418579" />
            </g>
            <g transform="matrix(-1,0,0,-1,256.001953125,256.00013732910156)" style="opacity:0;"></g>
          </g>
        </g>
      </g>
    </svg>
  </div>
</div>

{% schema %}
{
  "name": "Customize Image Gallery",
  "settings": [],
  "presets": [
    {
      "name": "Customize Image Gallery",
      "category": "Boulies Custom"
    }
  ]
}
{% endschema %}

<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

<script>
  $('#cig-content').slick({
    arrows: true,
    prevArrow: $('#cig-item-arrow-left'),
    nextArrow: $('#cig-item-arrow-right'),
    swipeToSlide: true,
    slidesToScroll: 1,
    infinite: false,
    variableWidth: true,
    responsive: [
      {
        breakpoint: 651,
        settings: {
          arrows: false,
          infinite: true,
        },
      },
    ],
  });

  // auto hide arrow when no image
  $('#cig-content').on('afterChange', function (event, slick, currentSlide) {
    let totalSlides = slick.slideCount;
    let slidesToShow = slick.options.slidesToShow;

    if (currentSlide === 0) {
      $('#cig-item-arrow-left').addClass('hide');
    } else {
      $('#cig-item-arrow-left').removeClass('hide');
    }

    if (currentSlide >= totalSlides - slidesToShow) {
      $('#cig-item-arrow-right').addClass('hide');
    } else {
      $('#cig-item-arrow-right').removeClass('hide');
    }
  });

  // popover toggle
  $('.cig-item-1-left-btn').on('click', function () {
    $('.cig-item-1-left-popover').toggleClass('show');
  });

  $('.cig-item-1-right-btn').on('click', function () {
    $('.cig-item-1-right-popover').toggleClass('show');
  });

  $('.cig-item-2-left-btn').on('click', function () {
    $('.cig-item-2-left-popover').toggleClass('show');
  });

  $('.cig-item-2-right-btn').on('click', function () {
    $('.cig-item-2-right-popover').toggleClass('show');
  });

  document.addEventListener('DOMContentLoaded', function () {
    const targetSection = document.querySelector('.cig-item-1');
    const trigger1LeftBtn = document.querySelector('.cig-item-1-left-btn');
    const trigger1RightBtn = document.querySelector('.cig-item-1-right-btn');
    let hasTriggered = false;

    if (!targetSection || !trigger1LeftBtn || !trigger1RightBtn) return;

    // auto click button when section visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasTriggered) {
            hasTriggered = true;
            setTimeout(() => {
              trigger1LeftBtn.click();
              trigger1RightBtn.click();
            }, 1500);
          }
        });
      },
      {
        threshold: 0.3, // visible percentage
      }
    );
    observer.observe(targetSection);

    // auto click arrow when item 2 is visible
    let hasArrowTriggered = false;
    $('#cig-content').on('afterChange', function (event, slick, currentSlide) {
      if (currentSlide === 1 && !hasArrowTriggered) {
        hasArrowTriggered = true;
        setTimeout(() => {
          document.querySelector('.cig-item-2-left-btn')?.click();
          document.querySelector('.cig-item-2-right-btn')?.click();
        }, 1500);
      }
    });
  });
</script>
