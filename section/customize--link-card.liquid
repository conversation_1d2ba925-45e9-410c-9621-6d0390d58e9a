<a
  href="{{ section.settings.url }}"
  class="b5-showcase{% if section.settings.is_inverted %} b5-showcase--inverted{% endif %} b5-showcase--{{ section.settings.alignment }}{% if section.settings.is_wide %} b5-showcase--wide{% endif %} "
>
  <picture>
    <source media="(max-width: 650px)" srcset="{{ section.settings.background__sp.src | img_url: '650x'  }}">
    <img
      class="b5-showcase-bg"
      src="{{ section.settings.background__pc.src | img_url: '1360x' }}"
      alt="{{ section.settings.title }} | {{ section.settings.subtitle }}"
    >
  </picture>
  <div class="b5-showcase-introduce">
    <h2 class="b5-showcase-title">{{ section.settings.title }}</h2>
    <h3 class="b5-showcase-subtitle">{{ section.settings.subtitle }}</h3>
    <div class="b5-showcase-link-like">
      {{ section.settings.link_text }}
      <svg
        class="b5-showcase-link-like__arrow"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        version="1.1"
        width="16"
        height="16"
        viewBox="0 0 19.5 16"
      >
        <g><path fill="" transform="matrix(0,1,-1,0,19.5,-19.5)" d="M26.79289,0.292893C27.183419999999998,-0.0976311,27.816580000000002,-0.0976311,28.20711,0.292893C28.20711,0.292893,35.2071,7.29289,35.2071,7.29289C35.5976,7.68342,35.5976,8.31658,35.2071,8.70711C34.8166,9.09763,34.1834,9.09763,33.7929,8.70711C33.7929,8.70711,28.5,3.41421,28.5,3.41421C28.5,3.41421,28.5,18.5,28.5,18.5C28.5,19.0523,28.05228,19.5,27.5,19.5C26.94772,19.5,26.5,19.0523,26.5,18.5C26.5,18.5,26.5,3.41421,26.5,3.41421C26.5,3.41421,21.20711,8.70711,21.20711,8.70711C20.816580000000002,9.09763,20.183417,9.09763,19.792893,8.70711C19.4023689,8.31658,19.4023689,7.68342,19.792893,7.29289C19.792893,7.29289,26.79289,0.292893,26.79289,0.292893C26.79289,0.292893,26.79289,0.292893,26.79289,0.292893Z"></path></g>
      </svg>
    </div>
  </div>
</a>
{% schema %}
{
  "name": "Link Card",
  "class": "customize--link-card",
  "settings": [
    {
      "type": "url",
      "id": "url",
      "label": "Link url"
    },
    {
      "type": "image_picker",
      "id": "background__pc",
      "label": "Background Image -- PC",
      "info": "图片分辨率最小为1360x560，或者，为等比例放大的图片。"
    },
    {
      "type": "image_picker",
      "id": "background__sp",
      "label": "Background Image -- SP",
      "info": "图片分辨率为388x527，或者，为等比例放大的图片。"
    },
    {
      "type": "select",
      "id": "alignment",
      "label": "Alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "right"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Boulies Chair"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Sub Title",
      "default": "Award-winning chair"
    },
    {
      "type": "text",
      "id": "link_text",
      "label": "Link Text",
      "default": "Learn More"
    },
    {
      "type": "checkbox",
      "id": "is_inverted",
      "label": "Inverted Text Color",
      "info": "背景图片较浅文字看不清时勾选。",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "is_wide",
      "label": "Wider Text Aera",
      "info": "文字显示区域加长",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "Link Card",
      "category": "Boulies Custom"
    }
  ]
}
{% endschema %}

{% stylesheet %}
  a.b5-showcase {
    position: relative;
    display: block;
    margin: 96px auto;
    width: 91.67%;
    max-width: 1360px;
    overflow: hidden;
    text-decoration: none;
    color: #fff;
  }
  img.b5-showcase-bg {
    width: 100%;
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
  }
  .b5-showcase-introduce {
    position: absolute;
    left: 60%;
    top: 50%;
    transform: translateY(-50%);
    width: 30%;
  }
  h2.b5-showcase-title {
    font-size: 36px;
    margin: 0;
    font-weight: bold;
  }
  h3.b5-showcase-subtitle {
    font-size: 24px;
    font-weight: 500;
    margin: 4px 0 16px 0;
  }
  .b5-showcase-link-like {
    font-weight: 500;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  svg.b5-showcase-link-like__arrow {
    position: relative;
    left: 10px;
    transition: left 300ms ease-in-out;
  }
  svg.b5-showcase-link-like__arrow path {
    fill: #fff;
  }
  a.b5-showcase:hover svg.b5-showcase-link-like__arrow {
    left: 15px;
  }

  a.b5-showcase.b5-showcase--inverted {
    color: #141414;
  }
  a.b5-showcase.b5-showcase--inverted svg.b5-showcase-link-like__arrow path {
    fill: #443a3a;
  }

  a.b5-showcase.b5-showcase--center .b5-showcase-introduce {
    left: 50%;
    top: 60px;
    transform: translateX(-50%);
    text-align: center;
    width: 50%;
  }
  a.b5-showcase.b5-showcase--center .b5-showcase-link-like {
    justify-content: center;
  }
  a.b5-showcase.b5-showcase--left .b5-showcase-introduce {
    left: 18%;
  }

  a.b5-showcase.b5-showcase--wide .b5-showcase-introduce {
    left: 52.5%;
    width: 36.3%;
  }
  a.b5-showcase.b5-showcase--wide.b5-showcase--left .b5-showcase-introduce {
    left: 10%;
    width: 40%;
  }

  @media screen and (max-width: 1024px) {
    h2.b5-showcase-title {
      font-size: 24px;
    }
    h3.b5-showcase-subtitle {
      font-size: 20px;
    }
  }

  @media screen and (max-width: 650px) {
    a.b5-showcase {
      transition: transform 300ms ease-in-out;
      margin: 40px auto;
    }
    a.b5-showcase:hover,
    a.b5-showcase:focus,
    a.b5-showcase:active {
      transform: scale(0.96);
    }
    .b5-showcase-introduce,
    a.b5-showcase.b5-showcase--center .b5-showcase-introduce,
    a.b5-showcase.b5-showcase--left .b5-showcase-introduce {
      top: 8vw !important;
      left: 50% !important;
      transform: translateX(-50%);
      text-align: center;
      width: 77.83vw !important;
    }
    h2.b5-showcase-title {
      font-size: 6.67vw;
    }
    h3.b5-showcase-subtitle {
      font-size: 4.67vw;
      margin: 0.67vw 0 2.67vw 0;
    }
    .b5-showcase-link-like {
      justify-content: center;
      font-size: 4vw;
    }
    img.b5-showcase-bg {
      border-radius: 3.2vw;
    }
    svg.b5-showcase-link-like__arrow {
      width: 4vw;
      height: 4vw;
    }
    a.b5-showcase:hover svg.b5-showcase-link-like__arrow {
      left: 10px;
    }
  }
{% endstylesheet %}

{% javascript %}
{% endjavascript %}
