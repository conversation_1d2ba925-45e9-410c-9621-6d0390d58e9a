<style>
  .home-buyNow-Bg {
    height: 400px;
    background-position: center;
    background-size: auto 100%;
  }

  .home-buyNow-content {
    width: 100%;
    text-align: center;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
  }

  .home-buyNow-contentHead {
    font-size: 40px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .home-buyNow-contentBody {
    font-size: 24px;
    margin-bottom: 32px;
  }

  .home-buyNow-link-btn {
    font-size: 18px;
  }

  @media screen and (max-width: 650px) {
    .home-buyNow-contentHead {
      font-size: 6vw;
      margin-bottom: 1.33vw;
    }

    .home-buyNow-contentBody {
      font-size: 4vw;
    }

    .home-buyNow-link-btn {
      font-size: 4vw;
      width: 46vw;
      padding: 2.25vw 0;
      height: unset;
      line-height: 1;
    }
  }
</style>

<div class="home-buyNow-wrap">
  <div
    class="home-buyNow-Bg lazy"
    data-bg="https://cdn.shopify.com/s/files/1/0055/5515/9126/files/index-chooseUs.jpg?1405"
  >
    <div class="home-buyNow-content">
      <div class="home-buyNow-contentHead">Upgrade To Boulies Chair Now</div>
      <div class="home-buyNow-contentBody">Experience the next level of comfort</div>
      <div class="home-buyNow-link">
        <a href="/collections/all" class="bouliesBTN bouliesBTN_borderInverted home-buyNow-link-btn"
          >Choose a boulies chair</a
        >
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Home Buy Now",
  "class": "customize--home-buy-now",
  "presets": [
    {
      "name": "Home Buy Now",
      "category": "Boulies Custom"
    }
  ]
}
{% endschema %}
