<div class="b5-caption-small">
  <div class="b5-caption-small__main">{{ section.settings.main_title }}</div>
  <h3 class="b5-caption-small__sub">{{ section.settings.sub_title }}</h3>
</div>

{% schema %}
{
  "name": "Index Caption Small",
  "class": "customize--caption-small",
  "settings": [
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "Talk about something"
    },
    {
      "type": "text",
      "id": "sub_title",
      "label": "Sub Title",
      "default": "Describe something"
    },
    {
      "type": "range",
      "id": "max_width",
      "label": "Max Width",
      "default": 1000,
      "min": 100,
      "max": 2000,
      "step": 100,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "sp_width",
      "label": "Sp Width",
      "default": 84,
      "min": 1,
      "max": 100,
      "step": 1,
      "unit": "%"
    }
  ],
  "presets": [
    {
      "name": "Index Caption Small",
      "category": "Boulies Custom"
    }
  ]
}
{% endschema %}

<style>
  .b5-caption-small {
    margin: 128px auto 72px auto;
    width: 100%;
    text-align: center;
    max-width: {{section.settings.max_width}}px;
  }
  .b5-caption-small__main {
    font-size: 36px;
    color: #141414;
    font-weight: 700;
  }
  .b5-caption-small__sub {
    font-size: 24px;
    color: #8C8C8C;
    font-weight: 500;
    margin-top: 16px;
  }

  @media screen and (max-width: 650px) {
    .b5-caption-small {
      width: {{section.settings.sp_width}}%;
      text-align: center;
      max-width: unset;
      margin: 56px auto;
    }
    .b5-caption-small__main {
      font-size: 6vw;
    }
    .b5-caption-small__sub {
      font-size: 4vw;
    }
  }
</style>
