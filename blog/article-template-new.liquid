<div class="article-container">
  <div id="progress-container">
    <div id="progress-bar"></div>
  </div>

  <div class="article-header">
    <div class="article-header-content-wrapper">
      <div class="article-header-content">
        <nav class="article-header-breadcrumb">
          <a class="breadcrumb__link" href="/pages/blog">Home</a>
          <span class="breadcrumb__span">/</span>
          <a class="breadcrumb__link" href="{{ blog.url }}">{{ blog.title }}</a>
          <span class="breadcrumb__span">/</span>
          <span class="breadcrumb__span">{{ article.metafields.custom.breadcrumb | default: article.title }}</span>
        </nav>

        <h1 class="article-header-title">{{ article.title }}</h1>
        <div class="article-header-author">
          <span class="article-header-author-name">By {{ article.author }}</span>
          <span class="article-header-author-date">{{ article.published_at | date: '%d %B %Y' }}</span>
        </div>
      </div>
    </div>

    <div class="article-header-background">
      <img
        src="{{ article.image.src | image_url}}"
        alt="{{ article.image.alt }}"
        width="{{ article.image.width }}"
        height="{{ article.image.height }}"
      >
    </div>
  </div>

  {% assign cleaned_content = article.content | replace: ' data-mce-fragment="1"', '' %}
  {% assign updated_content = cleaned_content %}
  {% assign headings = cleaned_content | split: '<h2>' %}

  <div class="article-content-wrapper">
    {% if headings.size > 1 %}
      {% for heading in headings offset: 1 %}
        {% assign title = heading | split: '</h2>' | first %}
        {% assign anchor = title | handleize %}
        {% assign new_heading = '<h2 id="' | append: anchor | append: '">' | append: title | append: '</h2>' %}
        {% assign original_heading = '<h2>' | append: title | append: '</h2>' %}
        {% assign updated_content = updated_content | replace: original_heading, new_heading %}
      {% endfor %}
    {% endif %}

    <div class="article-content-container">
      <div class="article-toc-container">
        <div class="article-toc">
          <p class="article-toc-title" id="article-toc-switch">
            Table of Contents
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              fill="none"
              version="1.1"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              class="article-toc-switch-icon svg-rotate-180"
              id="article-toc-switch-icon"
            >
              <defs><clipPath id="master_svg0_47_7983"><rect x="0" y="0" width="24" height="24" rx="0"></rect></clipPath></defs><g clip-path="url(#master_svg0_47_7983)"><g><path d="M16.899999713897706,9.199999618530274C16.499999713897704,8.799999618530274,15.899999713897705,8.799999618530274,15.499999713897704,9.199999618530274C15.499999713897704,9.199999618530274,11.999999713897704,12.699999618530274,11.999999713897704,12.699999618530274C11.999999713897704,12.699999618530274,8.499999713897704,9.199999618530274,8.499999713897704,9.199999618530274C8.099999713897706,8.799999618530274,7.499999713897705,8.799999618530274,7.099999713897705,9.199999618530274C6.699999713897705,9.599999618530273,6.699999713897705,10.199999618530274,7.099999713897705,10.599999618530273C7.099999713897705,10.599999618530273,11.299999713897705,14.799999618530274,11.299999713897705,14.799999618530274C11.499999713897704,14.999999618530273,11.699999713897705,15.099999618530273,11.999999713897704,15.099999618530273C12.299999713897705,15.099999618530273,12.499999713897704,14.999999618530273,12.699999713897705,14.799999618530274C12.699999713897705,14.799999618530274,16.899999713897706,10.599999618530273,16.899999713897706,10.599999618530273C17.299999713897705,10.199999618530274,17.299999713897705,9.599999618530273,16.899999713897706,9.199999618530274C16.899999713897706,9.199999618530274,16.899999713897706,9.199999618530274,16.899999713897706,9.199999618530274Z" fill="#000000" fill-opacity="1"></path></g></g>
            </svg>
          </p>
          <ul class="article-toc-list hidden">
            {% for heading in headings offset: 1 %}
              {% assign title = heading | split: '</h2>' | first %}
              {% assign anchor = title | handleize %}
              <li class="article-toc-item">
                <a class="article-toc-link link-underline" data-anchor="{{ anchor }}">{{ title }}</a>
              </li>
            {% endfor %}
          </ul>
        </div>
      </div>

      <div class="article-content-container-content">
        {{ updated_content }}
      </div>

      <div class="article-content-share-box">
        <div class="article-content-share-box-title">Please share this article if you like it!</div>
        <button class="article-content-share-box-btn" id="share-it-btn">Share it!</button>
      </div>

      <div class="article-content-other-articles">
        <div class="article-content-other-articles-title">Other Articles</div>

        <div class="article-content-other-articles-list">
          {% if blog.previous_article %}
            <div class="article-content-other-articles-item">
              <div class="other-articles-item-image">
                <img
                  src="{{ blog.previous_article.image | image_url: width: 150, height: 150 }}"
                  alt="{{ blog.previous_article.image.alt }}"
                  width="100px"
                  height="100px"
                  loading="lazy"
                >
              </div>
              <div class="other-articles-item-content">
                <div class="other-articles-item-content-title">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    fill="none"
                    version="1.1"
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                  >
                    <defs><clipPath id="master_svg0_163_8041"><rect x="16" y="16" width="16" height="16" rx="0"/></clipPath></defs><g transform="matrix(-1,0,0,-1,32,32)" clip-path="url(#master_svg0_163_8041)"><g><g transform="matrix(0,1,-1,0,49.435546875,-14.564453125)"><path d="M37.98391,17.675869875C38.304339999999996,17.355439375,38.823859999999996,17.355439375,39.14429,17.675869875L44.8879,23.419456875C45.2083,23.739886875,45.2083,24.259406875,44.8879,24.579846875C44.5675,24.900266875,44.0479,24.900266875,43.7275,24.579846875L39.38461,20.236946875L39.38461,32.615046875000004C39.38461,33.068246875,39.01726,33.435546875,38.564099999999996,33.435546875C38.11095,33.435546875,37.74359,33.068246875,37.74359,32.615046875000004L37.74359,20.236946875L33.40071,24.579846875C33.08027,24.900266875,32.560753,24.900266875,32.240322,24.579846875C31.9198923,24.259406875,31.9198923,23.739886875,32.240322,23.419456875L37.98391,17.675869875Z" fill="#336CA8" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></g>
                  </svg>
                  Previous
                </div>
                <a class="other-articles-item-content-link link-underline" href="{{ blog.previous_article.url }}">
                  {{ blog.previous_article.title }}
                </a>
              </div>
            </div>
          {% endif %}
          {% if blog.next_article %}
            <div class="article-content-other-articles-item">
              <div class="other-articles-item-image">
                <img
                  src="{{ blog.next_article.image | image_url: width: 150, height: 150 }}"
                  alt="{{ blog.next_article.image.alt }}"
                  width="100px"
                  height="100px"
                  loading="lazy"
                >
              </div>
              <div class="other-articles-item-content">
                <div class="other-articles-item-content-title">
                  Next
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    fill="none"
                    version="1.1"
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                  >
                    <defs><clipPath id="master_svg0_163_7983"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_163_7983)"><g><g transform="matrix(0,1,-1,0,17.435546875,-14.564453125)"><path d="M21.98391,1.675869875C22.30434,1.355439375,22.82386,1.355439375,23.144289999999998,1.675869875L28.887900000000002,7.419456875C29.2083,7.739886875,29.2083,8.259406875,28.887900000000002,8.579846875000001C28.567500000000003,8.900266875,28.0479,8.900266875,27.7275,8.579846875000001L23.384610000000002,4.236946875L23.384610000000002,16.615046875C23.384610000000002,17.068246875,23.01726,17.435546875,22.5641,17.435546875C22.11095,17.435546875,21.74359,17.068246875,21.74359,16.615046875L21.74359,4.236946875L17.40071,8.579846875000001C17.08027,8.900266875,16.560753,8.900266875,16.240322,8.579846875000001C15.9198923,8.259406875,15.9198923,7.739886875,16.240322,7.419456875L21.98391,1.675869875Z" fill="#336CA8" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></g>
                  </svg>
                </div>
                <a class="other-articles-item-content-link link-underline" href="{{ blog.next_article.url }}">
                  {{ blog.next_article.title }}
                </a>
              </div>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <div class="article-sidebar-container">
      <div class="article-sidebar">
        <div class="elementor-divider">
          <span class="elementor-divider-separator"></span>
        </div>
        <div class="article-sidebar-related-posts">
          <div class="article-sidebar-related-posts-title">-------- Related Posts</div>
          <div class="article-sidebar-related-posts-list">
            {% assign related_count = 0 %}
            {% for related_article in blog.articles %}
              {% if related_article.id != article.id and related_count < 3 %}
                <div class="article-sidebar-related-posts-item">
                  <div class="article-sidebar-related-posts-item-image-wrapper">
                    <a class="article-sidebar-related-posts-item-image" href="{{ related_article.url }}">
                      <img
                        src="{{ related_article.image | image_url: width: 150, height: 150 }}"
                        alt="{{ related_article.image.alt }}"
                        width="100px"
                        height="100px"
                        loading="lazy"
                      >
                    </a>
                  </div>
                  <div class="article-sidebar-related-posts-item-content">
                    <a class="article-sidebar-related-posts-item-link link-underline" href="{{ related_article.url }}">
                      {{ related_article.title }}
                    </a>
                    <div class="article-sidebar-related-posts-item-date">
                      {{ related_article.published_at | date: '%d %B %Y' }}
                    </div>
                  </div>
                </div>
                {% assign related_count = related_count | plus: 1 %}
              {% endif %}
            {% endfor %}
          </div>
        </div>

        <div class="elementor-divider">
          <span class="elementor-divider-separator"></span>
        </div>
        <div class="article-sidebar-newsletter">
          <div class="article-sidebar-newsletter-title">NEWSLETTER</div>
          <div class="article-sidebar-newsletter-content">Fill your email below to subscribe to my newsletter</div>
          {% form 'customer' %}
            <!-- Hidden fields to track the subscription source -->
            <input type="hidden" name="contact[tags]" value="newsletter">
            <input type="hidden" name="customer[accepts_marketing]" value="true">
            <div class="article-sidebar-newsletter-input-wrapper">
              <input
                class="article-sidebar-newsletter-input"
                type="email"
                placeholder="Email"
                name="contact[email]"
                required
              >
              <div class="article-sidebar-newsletter-submit-wrapper">
                <button class="article-sidebar-newsletter-submit" type="submit">Subscribe</button>
              </div>
            </div>
            {% if form.errors %}
              <div class="ui error mini message">
                <div class="header">Error occurred!</div>
                {{ form.errors | default_errors }}
              </div>
            {% endif %}
          {% endform %}
        </div>
      </div>
    </div>
  </div>

  <div class="article-footer"></div>
</div>

<style>
  html {
    scroll-behavior: smooth;
  }

  .main-content {
    overflow: visible !important;
  }

  #progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    z-index: 100;
  }

  #progress-bar {
    width: 0;
    height: 100%;
    background-color: #336CA8;
  }

  /* article */
  .article-container {
    width: 100%;
    max-width: 1920px;
    margin: 0 auto;
    padding-bottom: 72px;
  }

  /* article header */
  .article-header {
    position: relative;
    margin-bottom: 72px;
  }

  .article-header-background {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #000;
    overflow: hidden;
  }

  .article-header-background img {
    max-width: 1920px;
    max-height: 780px;
    object-fit: cover;
  }

  .article-header-content-wrapper {
    position: absolute;
    left: 12.5%;
    bottom: 9.23%;
    width: 75%;
  }

  .article-header-content {
    background: #ffffff;
    color:#141414;
    border-radius: 8px;
    padding: 24px 24px 40px 24px;
  }

  .article-header-breadcrumb {
    font-size: 18px;
    font-weight: 500;
  }

  .article-header-breadcrumb .breadcrumb__link {
    color: #141414;
    transition: 0.6s ease-in-out;
  }

  .article-header-breadcrumb .breadcrumb__link:hover {
    color: #336CA8;
  }

  .article-header-breadcrumb .breadcrumb__span {
    color: #141414;
  }

  .article-header-title {
    margin: 24px 0 16px 0;
    font-size: 36px;
    font-weight: 700;
  }

  .article-header-author {
    display: flex;
    align-items: center;
    gap: 24px;
    font-size: 18px;
    font-weight: 500;
    font-style: italic;
    color: #8C8C8C;
  }

  /* article toc */
  .article-toc-container {
    padding-bottom: 32px;
  }

  .article-toc {
    border-radius: 10px;
    border: 1px solid #F0F0F0;
    padding: 16px;
    font-size: 20px;
    font-weight: 500;
  }

  .article-toc-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color:#141414;
    margin: 0;
  }

  .article-toc-switch-icon {
    transition: 0.6s;
    cursor: pointer;
  }

  .svg-rotate-180 {
    transform: rotateX(180deg);
  }

  .article-toc-list {
    line-height: 1.5;
    transition: all 0.6s;
    max-height: 800px;
    overflow-y: auto;
  }

  .article-toc-list.hidden {
    overflow: hidden;
    margin: 0;
    max-height: 0;
  }

  .article-toc-item {
    margin: 5px 0;
    color: #141414;
  }

  .article-toc-link {
    text-decoration: none;
    font-size: 18px;
    color: #141414;
    cursor: pointer;
  }

  /* article content */
  .article-content-wrapper {
    max-width: 1280px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
  }

  .article-content-container {
    width: calc(100% - 458px);
    padding:0 30px;
  }

  {% comment %} article content style{% endcomment %}
  .article-content-container-content {
    padding-bottom: 40px;
    color: #141414;
    font-size: 18px;
    font-weight: 500;
    line-height: 1.5;
  }

  .article-content-container-content img {
    max-width: 100%;
    height: auto;
  }

  .article-content-container-content iframe {
    max-width: 100%;
  }

  .article-content-container-content table, th, td {
    border: 1px solid black;
    border-collapse: collapse;
    padding:0 8px !important;
  }

  /* article share box */
  .article-content-share-box {
    padding-bottom: 120px;
    text-align: center;
  }

  .article-content-share-box-title {
    font-size: 24px;
    font-weight: 700;
    color: #141414;
    font-style: italic;
    margin-bottom: 16px;
  }

  .article-content-share-box-btn {
    background: #141414;
    color: #ffffff;
    border-radius: 4px;
    cursor: pointer;
    padding: 9px 110px;
    transition: 0.6s ease-in-out;
    font-size: 18px;
    font-weight: 500;
    border: 0;
  }

  .article-content-share-box-btn:hover {
    opacity: 0.7;
  }

  .copy-success {
  }

  .copy-error {
    background: #fff;
    color: rgb(212, 21, 21);
  }

  /* other articles */
  .article-content-other-articles {
    border-top: 1px solid #F0F0F0;
  }

  .article-content-other-articles-title {
    font-size: 24px;
    font-weight: 700;
    margin: 24px 0 16px 0;
  }

  .article-content-other-articles-list {
    display: flex;
    flex-direction: row;
    gap: 24px;
  }

  .article-content-other-articles-item {
    display: flex;
    align-items: center;
    border: 1px solid #F0F0F0;
    border-radius: 16px;
    padding: 16px;
    transition: 0.3s ease-in-out;
    gap: 24px;
    width: 50%;
  }

  .article-content-other-articles-item:hover {
    box-shadow: 0 20px 35px #70798b38;
  }

  .other-articles-item-image{
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .other-articles-item-image img {
    border-radius: 8px;
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
  }

  .other-articles-item-content-title {
    color: #336CA8;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 13px;
    font-size: 18px;
    font-weight: 500;
  }

  .other-articles-item-content-link {
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    color: #141414;
  }

  /* sidebar */
  .article-sidebar-container {
    width: 382px;
    padding: 0 8px;
  }

  .article-sidebar {
    position: sticky;
    top: 20px;
    border-radius: 8px;
    border: 1px solid #F0F0F0;
    padding: 32px 24px 24px 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .elementor-divider {
  }

  .elementor-divider-separator {
    border-block-start: 1px solid #F0F0F0;
    width: 100%;
    display: block;
  }

  /* related posts */
  .article-sidebar-related-posts {
  }

  .article-sidebar-related-posts-title {
    color: #141414;
    font-size: 18px;
    font-weight: 700;
    padding-bottom: 24px;
  }

  .article-sidebar-related-posts-list {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .article-sidebar-related-posts-item {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .article-sidebar-related-posts-item-link {
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    color: #141414;
  }

  .article-sidebar-related-posts-item-date {
    font-size: 16px;
    font-weight: 500;
    color: #8C8C8C;
    margin-top: 4px;
  }

  .article-sidebar-related-posts-item-image-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .article-sidebar-related-posts-item-image {
    overflow: hidden;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .article-sidebar-related-posts-item-image img {
    max-width: 80px;
    max-height: 80px;
    object-fit: cover;
    transition: 0.6s ease-in-out;
  }

  .article-sidebar-related-posts-item-image img:hover {
    transform: scale(1.1);
  }

  /* newsletter */
  .article-sidebar-newsletter {
    background-color: #F0F0F0;
    border-radius: 8px;
    padding: 48px 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: border 0.3s, background 0.3s;
    text-align: center;
  }

  .article-sidebar-newsletter-title {
    color: #141414;
    font-size: 18px;
    font-weight: 700;
  }

  .article-sidebar-newsletter-content {
    color: #141414;
    font-size: 16px;
    font-weight: 500;
    line-height: 1.5;
    margin: 0 40px;
  }

  .article-sidebar-newsletter-input-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  .article-sidebar-newsletter-input {
    background-color: #ffffff;
    padding: 11px 16px;
    border: 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    width: 303px;
  }

  .article-sidebar-newsletter-submit-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 6px 8px;
    background-color: #ffffff;
  }

  .article-sidebar-newsletter-submit {
    border: 0;
    padding: 5px 8px;
    border-radius: 4px;
    background-color: #141414;
    color: #ffffff;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: 0.6s ease-in-out;
    opacity: 1;
  }

  .article-sidebar-newsletter-submit:hover {
    opacity: 0.7;
  }

  /* underline hover */
  .link-underline {
    background-image: linear-gradient(90deg, currentColor, currentColor);
    background-repeat: no-repeat;
    background-size: 0% 1px;
    background-position: 0% 100%;
    transition: 1s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: inline;
  }

  .link-underline:hover {
    background-size: 100% 1px;
    color: #336CA8;
  }

  @media (max-width: 1200px) {
    .article-header-background img {
      max-width: 1200px;
      max-height: 600px;
    }

    .article-content-wrapper {
      flex-direction: column;
    }

    .article-content-container {
      width: unset;
    }

    .article-sidebar-container {
      width: unset;
      padding: 30px;
    }
  }

  @media (max-width: 900px) {
    .article-header-background img {
      max-width: 900px;
      max-height: 450px;
    }

    .article-content-other-articles-list {
      flex-direction: column;
    }

    .article-content-other-articles-item {
      width: unset;
    }
  }

  @media (max-width: 600px) {
    .article-container {
      padding-bottom: 8vw;
    }

    .article-header {
      margin-bottom: 6.67vw;
    }

    .article-header-content-wrapper {
      width: 92%;
      left: 4%;
      bottom: 5.33%;
    }

    .article-header-content {
      padding: 4vw;
      border-radius: 1.33vw;
    }

    .article-header-breadcrumb {
      font-size: 4vw;
      line-height: 1.5;
    }

    .article-header-title {
      font-size: 6vw;
      margin: 4vw 0 2.67vw 0;
    }

    .article-header-author {
      gap: 2.67vw;
      font-size: 3vw;
    }

    .article-header-background img {
      max-width: 600px;
      max-height: 450px;
    }

    .article-toc-container {
      padding-bottom: 2.33vw;
    }

    .article-toc {
      padding: 2.67vw;
      font-size: 4vw;
      border-radius: 0.67vw;
    }

    .article-toc-item a {
      font-size: 3.33vw;
    }

    .article-toc-link {
      font-size: 3.33vw;
    }

    .article-content-container {
      padding: 0;
      width: 92%;
      margin: 0 auto;
    }

    .article-content-container-content {
      padding-bottom: 5.33vw;
      word-wrap: break-word;
    }

    .article-content-container-content iframe {
      height: 20vh;
    }

    .article-content-share-box {
      padding-bottom: 12vw;
    }

    .article-content-share-box-title {
      margin-bottom: 2.67vw;
      font-size: 4vw;
    }

    .article-content-share-box-btn {
      padding: 2.27vw 20vw;
      font-size: 4vw;
      border-radius: 0.67vw;
    }

    .article-content-other-articles-title {
      font-size: 6vw;
      margin: 5.33vw 0 4vw 0;
    }

    .article-content-other-articles-list {
      gap: 4vw;
    }

    .article-content-other-articles-item {
      padding: 2.5vw;
      gap: 4vw;
      border-radius: 2.67vw;
    }

    .other-articles-item-image img {
      border-radius: 1.33vw;
    }

    .other-articles-item-content-title {
      margin-bottom: 1.33vw;
      font-size: 4vw;
      gap: 1.33vw;
    }

    .other-articles-item-content-link {
      font-size: 3.33vw;
    }

    .article-sidebar-container {
      padding: 4vw;
      width: 92%;
      margin: 0 auto;
    }

    .article-sidebar {
      padding: 5.33vw 4vw 4vw 4vw;
      gap: 4vw;
      border-radius: 1.33vw;
    }

    .article-sidebar-related-posts-title {
      font-size: 6vw;
      padding-bottom: 4vw;
    }

    .article-sidebar-related-posts-list {
      gap: 4vw;
    }

    .article-sidebar-related-posts-item {
      gap: 4vw;
    }

    .article-sidebar-related-posts-item-link {
      font-size: 4vw;
    }

    .article-sidebar-related-posts-item-date {
      font-size: 3.33vw;
    }

    .article-sidebar-newsletter {
      border-radius: 1.33vw;
      padding: 8vw 4vw;
      gap: 2.67vw;
    }

    .article-sidebar-newsletter-title {
      font-size: 6vw;
    }

    .article-sidebar-newsletter-content {
      font-size: 4vw;
      margin: 0 6.33vw;
    }

    .article-sidebar-newsletter-input-wrapper {
      margin-top: 2.67vw;
    }

    .article-sidebar-newsletter-input {
      padding: 2.17vw 2.67vw;
      border-radius: 0.67vw;
      font-size: 3vw;
      width: 76vw;
    }

    .article-sidebar-newsletter-submit-wrapper {
      padding: 1.33vw;
    }

    .article-sidebar-newsletter-submit {
      padding: 1.08vw 2.67vw;
      border-radius: 0.67vw;
      font-size: 2.67vw;
    }
  }
</style>

<script>
  function updateProgress() {
    var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    var documentHeight = document.documentElement.scrollHeight;
    var windowHeight = window.innerHeight;
    var scrollPercent = (scrollTop / (documentHeight - windowHeight)) * 100;
    document.getElementById('progress-bar').style.width = scrollPercent + '%';
  }

  window.addEventListener('scroll', updateProgress);
  updateProgress();

  // article toc switch
  if (document.getElementById('article-toc-switch')) {
    document.getElementById('article-toc-switch').addEventListener('click', function () {
      document.querySelector('.article-toc-list').classList.toggle('hidden');
      document.getElementById('article-toc-switch-icon').classList.toggle('svg-rotate-180');
    });
  }

  // share url
  document.getElementById('share-it-btn').addEventListener('click', function () {
    const btn = document.getElementById('share-it-btn');
    const url = window.location.href;
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(url)
        .then(function () {
          btn.textContent = 'Copied!';
          btn.classList.add('copy-success');
          setTimeout(() => {
            btn.textContent = 'Share it!';
            btn.classList.remove('copy-success');
          }, 5000);
        })
        .catch(function (err) {
          btn.textContent = 'Failed to copy';
          btn.classList.add('copy-error');
          setTimeout(() => {
            btn.textContent = 'Share it!';
            btn.classList.remove('copy-error');
          }, 5000);
          console.error('Failed to copy link:', err);
        });
    } else {
      const textArea = document.createElement('textarea');
      textArea.value = url;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        const successful = document.execCommand('copy');
        if (successful) {
          btn.textContent = 'Copied!';
          btn.classList.add('copy-success');
        } else {
          btn.textContent = 'Failed to copy';
          btn.classList.add('copy-error');
        }
        setTimeout(() => {
          btn.textContent = 'Share it!';
          btn.classList.remove('copy-success', 'copy-error');
        }, 5000);
      } catch (err) {
        btn.textContent = 'Failed to copy';
        btn.classList.add('copy-error');
        setTimeout(() => {
          btn.textContent = 'Share it!';
          btn.classList.remove('copy-error');
        }, 5000);
        console.error('Failed to copy link:', err);
      }
      document.body.removeChild(textArea);
    }
  });

  {% comment %} allow table scroll {% endcomment %}
  document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".article-content-container-content table").forEach(function (table) {
      let parent = table.parentElement;
      while (parent && getComputedStyle(parent).display === "inline") {
        parent = parent.parentElement;
      }
      if (parent) {
        parent.style.overflowX = "auto";
        parent.style.maxWidth = "100%";
        parent.style.display = "block";
      }
    });
  });

  {% comment %} article toc link click {% endcomment %}
  document.querySelectorAll('.article-toc-link').forEach(link => {
    link.addEventListener('click', function (e) {
      e.preventDefault();
      const targetId = this.getAttribute('data-anchor');
      const targetEl = document.getElementById(targetId);
      if (targetEl) {
        targetEl.scrollIntoView({ behavior: 'smooth' });
      }
    });
  });
</script>
