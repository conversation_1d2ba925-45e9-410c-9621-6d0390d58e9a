{% assign number_of_comments = article.comments_count %}

{% if comment and comment.created_at %}
  {% assign number_of_comments = article.comments_count | plus: 1 %}
{% endif %}

{% paginate article.comments by 5 %}
  {% comment %}{% include 'breadcrumb' %}{% endcomment %}

  <div class="grid" data-section-id="{{ section.id }}" data-section-type="article-template">
    <article class="grid__item post-large--four-fifths" itemscope itemtype="http://schema.org/Article">
      <div class="grid">
        <div class="grid__item post-large--ten-twelfths">
          <header class="section-header">
            <h1 class="section-header__title">{{ article.title }}</h1>
          </header>

          <p>
            <time datetime="{{ article.published_at | date: '%Y-%m-%d' }}">
              {{- article.published_at | date: format: 'month_day_year' -}}
            </time>
            {% if section.settings.article_author_enable %}
              <span class="meta-sep">&#8226;</span>
              {{ article.author }}
            {% endif %}
            {% if article.tags.size > 0 %}
              <span class="meta-sep">&#8226;</span>
              {% for tag in article.tags %}
                <a href="{{ blog.url }}/tagged/{{ tag | handle }}">{{ tag }}</a>
                {%- unless forloop.last %}
                  <span class="meta-sep">&#8226;</span>
                {% endunless %}
              {% endfor %}
            {% endif %}
          </p>

          <div class="rte" itemprop="articleBody">
            {{ article.content }}
          </div>

          {% if section.settings.social_sharing %}
            <hr class="hr--clear hr--small">
            {% include 'social-sharing' %}
          {% endif %}

          {% comment %}
            Create links to the next and previous articles, if available.
          {% endcomment %}
          {% if blog.next_article or blog.previous_article %}
            <hr class="hr--clear hr--small">
            <p class="clearfix">
              {% if blog.previous_article %}
                <span class="left"> &larr; {{ 'blogs.article.older_post' | t | link_to: blog.previous_article }} </span>
              {% endif %}
              {% if blog.next_article %}
                <span class="right"> {{ 'blogs.article.newer_post' | t | link_to: blog.next_article }} &rarr; </span>
              {% endif %}
            </p>
          {% endif %}

          {% if blog.comments_enabled? %}
            <hr class="hr--clear hr--small">

            {% comment %}
              Just like blog.liquid page, define how many comments should be on each page.
            {% endcomment %}

            {% comment %}
              #comments is required, it is used as an anchor link by Shopify.
            {% endcomment %}
            <div id="comments">
              {% if comment and comment.created_at %}
                <p class="note form-success">
                  {% if blog.moderated? %}
                    {{ 'blogs.comments.success_moderated' | t }}
                  {% else %}
                    {{ 'blogs.comments.success' | t }}
                  {% endif %}
                </p>
              {% endif %}

              {% if number_of_comments > 0 %}
                <ul>
                  {% comment %}
                    If a comment was just submitted with no blank field, show it.
                  {% endcomment %}
                  {% if comment and comment.created_at %}
                    <li id="{{ comment.id }}" class="comment first{% if article.comments_count == 0 %} last{% endif %}">
                      {% include 'comment' %}
                    </li>
                  {% endif %}

                  {% comment %}
                    Showing the rest of the comments.
                  {% endcomment %}
                  {% for comment in article.comments %}
                    <li
                      id="{{ comment.id }}"
                      class="comment{% unless number_of_comments > article.comments_count %}{% if forloop.first %} first{% endif %}{% endunless %}{% if forloop.last %} last {% endif %}"
                    >
                      {% include 'comment' %}
                    </li>
                  {% endfor %}
                </ul>

                {% if paginate.pages > 1 %}
                  <hr class="--clear hr--small">
                  <div class="text-center">
                    {% include 'pagination-custom' %}
                  </div>
                {% endif %}

                <hr class="hr--clear hr--small">
              {% endif %}

              {% comment %}
                Comment submission form
              {% endcomment %}
              {% form 'new_comment', article %}
                <h2 class="h4">{{ 'blogs.comments.title' | t }}</h2>

                {{ form.errors | default_errors }}

                <div class="grid">
                  <div class="grid__item post-large--one-half">
                    <label for="CommentAuthor" class="label--hidden">{{ 'blogs.comments.name' | t }}</label>
                    <input
                      {% if form.errors contains 'author' %}
                        class="error"
                      {% endif %}
                      type="text"
                      name="comment[author]"
                      placeholder="{{ 'blogs.comments.name' | t }}"
                      id="CommentAuthor"
                      value="{{ form.author }}"
                      autocapitalize="words"
                    >

                    <label for="CommentEmail" class="label--hidden">{{ 'blogs.comments.email' | t }}</label>
                    <input
                      {% if form.errors contains 'email' %}
                        class="error"
                      {% endif %}
                      type="email"
                      name="comment[email]"
                      placeholder="{{ 'blogs.comments.email' | t }}"
                      id="CommentEmail"
                      value="{{ form.email }}"
                      autocorrect="off"
                      autocapitalize="off"
                    >
                  </div>

                  <div class="grid__item">
                    <label for="CommentBody" class="label--hidden">{{ 'blogs.comments.message' | t }}</label>
                    <textarea
                      {% if form.errors contains 'body' %}
                        class="error"
                      {% endif %}
                      name="comment[body]"
                      id="CommentBody"
                      placeholder="{{ 'blogs.comments.message' | t }}"
                    >{{ form.body }}</textarea>
                  </div>
                </div>

                {% if blog.moderated? %}
                  <p>
                    <small>{{ 'blogs.comments.moderated' | t }}</small>
                  </p>
                {% endif %}

                <input type="submit" class="btn" value="{{ 'blogs.comments.post' | t }}">
              {% endform %}
            </div>
          {% endif %}
        </div>
      </div>
    </article>

    <aside class="sidebar grid__item post-large--one-fifth">
      {% include 'blog-sidebar' %}
    </aside>
  </div>
{% endpaginate %}

{% schema %}
{
  "name": "Article",
  "settings": [
    {
      "type": "checkbox",
      "id": "article_author_enable",
      "label": "Show blog post author"
    },
    {
      "type": "checkbox",
      "id": "social_sharing",
      "label": "Enable blog post sharing"
    }
  ]
}
{% endschema %}
