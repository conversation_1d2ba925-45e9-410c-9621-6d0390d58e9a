<style>
  .page-blog-wrapper {
    max-width: 1560px;
    margin: 0 auto;
    padding: 0 24px;
  }

  {% comment %} TITLE {% endcomment %}
  .page-blog-title {
    padding-bottom: 96px;
    text-align: center;
  }

  .page-blog-title-heading {
    visibility: hidden;
  }

  .page-blog-title-intro {
    font-size: 40px;
    font-weight: 700;
    color: #141414;
    margin: 60px 0 16px 0;
  }

  .page-blog-title-content {
    font-size: 16px;
    font-weight: 500;
    color: #8C8C8C;
    margin: 0;
  }

  {% comment %} POST LIST {% endcomment %}
  .page-blog-post-list {
    display: flex;
    flex-direction: row;
    gap: 40px;
  }

  {% comment %} left and right {% endcomment %}
  .page-blog-post-list-left .post-list-item-read-more-btn-wrapper,
  .page-blog-post-list-right .post-list-item-read-more-btn-wrapper {
  }

  .page-blog-post-list-left .post-list-item-read-more-btn,
  .page-blog-post-list-right .post-list-item-read-more-btn {
    display: inline-block;
    font-size: 18px;
    font-weight: 500;
    color: #FFFFFF;
    background: #141414;
    padding: 9px 24px;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    border-radius: 4px;
    transition: 0.3s ease-in-out;
    opacity: 1;
  }

  .page-blog-post-list-left .post-list-item-read-more-btn:hover,
  .page-blog-post-list-right .post-list-item-read-more-btn:hover {
    opacity: 0.7;
  }

  {% comment %} LEFT {% endcomment %}
  .page-blog-post-list-left {
    width: 50%;
  }

  .page-blog-post-list-left .post-list-item {
    padding: 24px;
    border-radius: 16px;
    border: 1px solid #F0F0F0;
    transition: .3s ease;
  }

  .page-blog-post-list-left .post-list-item:hover {
    box-shadow: 0 25px 35px #70798b33;
  }

  {% comment %} img {% endcomment %}
  .page-blog-post-list-left .post-list-item-image-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .page-blog-post-list-left .post-list-item-image {
    overflow: hidden;
    border-radius: 8px;
    display: flex ;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .page-blog-post-list-left .post-list-item-image img {
    width: 100%;
    height: 100%;
    aspect-ratio: 32/13;
    object-fit: cover;
    transition: 0.6s ease;
  }

  .page-blog-post-list-left .post-list-item-image img:hover {
    transform: scale(1.1);
  }

  {% comment %} content {% endcomment %}
  .page-blog-post-list-left .post-list-item-content {
    padding: 24px 24px 21px 8px;
  }

  .page-blog-post-list-left .post-list-item-link {
    text-decoration: none;
    font-size: 36px;
    font-weight: 700;
    color: #141414;
  }

  .page-blog-post-list-left .post-list-item-author {
    font-size: 18px;
    font-weight: 500;
    margin-top: 16px;
    text-transform: capitalize;
    font-style: italic;
    color: #8C8C8C;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
  }

  .page-blog-post-list-left .post-list-item-excerpt {
    font-size: 16px;
    font-weight: 500;
    color: #141414;
    margin: 24px 0 32px 0;
    line-height: 1.5;
  }

  {% comment %} RIGHT {% endcomment %}
  .page-blog-post-list-right {
    width: 50%;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .page-blog-post-list-right .post-list-item {
    display: flex;
    gap: 32px;
    padding: 24px 35px 24px 24px;
    border-radius: 16px;
    border: 1px solid #F0F0F0;
    transition: 0.3s ease-in-out;
    height: 33.33%;
  }

  .page-blog-post-list-right .post-list-item:hover {
    box-shadow: 0 25px 35px #70798b33;
  }

  .page-blog-post-list-right .post-list-item-link {
    text-decoration: none;
    font-size: 24px;
    font-weight: 700;
    color: #141414;
  }

  .page-blog-post-list-right .post-list-item-date {
    margin-top: 16px;
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: 500;
    color: #8C8C8C;
    font-style: italic;
  }

  .page-blog-post-list-right .post-list-item-author {
    display: none;
  }

  .page-blog-post-list-right .post-list-item-image-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 16;
  }

  .page-blog-post-list-right .post-list-item-image {
    overflow: hidden;
    border-radius: 8px;
    display: flex ;
    justify-content: center;
    align-items: center;
  }

  .page-blog-post-list-right .post-list-item-image img {
    width: 100%;
    height: 100%;
    aspect-ratio: 32/13;
    object-fit: cover;
    transition: 0.6s ease;
  }

  .page-blog-post-list-right .post-list-item-image img:hover {
    transform: scale(1.1);
  }

  .page-blog-post-list-right .post-list-item-content {
    flex: 13;
  }

  {% comment %} LATEST POSTS {% endcomment %}
  .page-blog-post-latest-posts {
    padding: 120px 0;
  }

  .page-blog-post-latest-posts-title {
    font-size: 40px;
    font-weight: 700;
    color: #141414;
    text-align: center;
    padding-bottom: 56px;
  }

  .page-blog-post-latest-posts-nav {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 64px;
    font-weight: 500;
    font-size: 24px;
    padding-bottom: 32px;
    transition: 0.3s ease-in-out;
  }

  .page-blog-post-latest-posts-nav a {
    text-decoration: none;
    color: #141414;
  }

  .page-blog-post-latest-posts-nav a:hover {
    color: #336CA8;
  }

  .page-blog-post-latest-posts-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 30px;
    row-gap: 30px;
  }

  .page-blog-post-latest-posts-list-item {
    position: relative;
    display: none;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-in-out, transform 0.6s ease-in-out;
    border: 1px solid #F0F0F0;
    border-radius: 16px;
  }

  .page-blog-post-latest-posts-list-item.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .page-blog-post-latest-posts-load-more-btn-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
  }

  .page-blog-post-latest-posts-load-more-btn {
    background: #141414;
    color: #FFFFFF;
    padding: 9px 110px;
    border-radius: 4px;
    cursor: pointer;
    transition: 0.3s ease-in-out;
    opacity: 1;
    font-size: 24px;
    font-weight: 500;
    border: none;
  }

  .page-blog-post-latest-posts-load-more-btn:hover {
    opacity: 0.7;
  }

  .page-blog-post-latest-posts-list-item-image-wrapper {
    overflow: hidden;
    border-radius: 16px 16px 0 0;
  }

  .page-blog-post-latest-posts-list-item-image-wrapper img {
    width: 100%;
    height: 100%;
    aspect-ratio: 32/13;
    object-fit: cover;
    transition: 0.6s ease-in-out;
  }

  .page-blog-post-latest-posts-list-item-image-wrapper img:hover {
    transform: scale(1.1);
  }

  .page-blog-post-latest-posts-list-item-content-wrapper {
    width: 100%;
  }

  .page-blog-post-latest-posts-list-item-content {
    text-align: center;
    padding: 16px;
  }

  .page-blog-post-latest-posts-list-item-link {
    text-decoration: none;
    font-size: 24px;
    font-weight: 700;
    color: #141414;
  }

  .page-blog-post-latest-posts-list-item-date {
    margin-top: 16px;
    font-size: 18px;
    font-weight: 500;
    color: #8C8C8C;
  }

  /* underline hover */
  .link-underline {
    background-image: linear-gradient(90deg, currentColor, currentColor);
    background-repeat: no-repeat;
    background-size: 0% 1px;
    background-position: 0% 100%;
    transition: 1s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: inline;
  }

  .link-underline:hover {
    background-size: 100% 1px;
    color: #336CA8;
  }

  @media (max-width: 1024px) {
    .page-blog-post-list {
      flex-direction: column;
    }

    .page-blog-post-list-left,
    .page-blog-post-list-right {
      width: unset;
    }

    .page-blog-post-latest-posts-list {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 768px) {
    .page-blog-post-latest-posts-list {
      grid-template-columns: repeat(1, 1fr);
    }

    .page-blog-post-list-left .post-list-item-excerpt {
      display: none;
    }

    .page-blog-post-list-left .post-list-item-read-more-btn-wrapper,
    .page-blog-post-list-right .post-list-item-read-more-btn-wrapper {
      display: none;
    }
  }

  @media (max-width: 600px) {
    .page-blog-wrapper {
      padding: 0 4vw;
    }

    .page-blog-title {
      padding: 0 5.33vw 5.33vw 5.33vw;
    }

    .page-blog-title-intro {
      font-size: 6.67vw;
      margin: 0 0 2.67vw 0;
    }

    .page-blog-title-content {
      font-size: 4vw;
    }

    .page-blog-post-list {
      gap: 4vw;
    }

    {% comment %} left {% endcomment %}
    .page-blog-post-list-left .post-list-item {
      padding: 4vw;
      border-radius: 2.67vw;
      gap: 4vw;
    }

    .page-blog-post-list-left .post-list-item-image{
      border-radius: 1.67vw;
    }

    .page-blog-post-list-left .post-list-item-content{
      padding: 4vw 1.67vw 2.17vw 1.33vw;
    }

    .page-blog-post-list-left .post-list-item-link {
      font-size: 6vw;
    }

    .page-blog-post-list-left .post-list-item-author {
      font-size: 4vw;
      margin-top: 2.67vw;
      gap: 4vw;
    }

    {% comment %} right {% endcomment %}
    .page-blog-post-list-right{
      gap: 3.33vw;
    }

    .page-blog-post-list-right .post-list-item {
      padding: 4vw 1.17vw 4vw 4vw;
      border-radius: 2.67vw;
      gap: 4vw;
    }

    .page-blog-post-list-right .post-list-item-image-wrapper {
      flex: 2;
    }

    .page-blog-post-list-right .post-list-item-content{
      flex: 3;
    }

    .page-blog-post-list-right .post-list-item-link {
      font-size: 4vw;
      margin-top: 1.33vw;
    }

    .page-blog-post-list-right .post-list-item-date{
      font-size: 3vw;
      margin: 2.67vw 0 0 0;
      display: flex;
      gap: 4vw;
    }

    .page-blog-post-list-right .post-list-item-author {
      display: inline;
    }

    {% comment %} latest posts {% endcomment %}
    .page-blog-post-latest-posts {
      padding: 12vw 0 8vw 0;
    }

    .page-blog-post-latest-posts-title {
      font-size: 6.67vw;
      padding-bottom: 6.67vw;
    }

    .page-blog-post-latest-posts-nav {
      gap: 9.33vw;
      font-size: 4vw;
      padding-bottom: 5.33vw;
      width: 90%;
      margin: 0 auto;
    }

    .page-blog-post-latest-posts-list {
      column-gap: 4vw;
      row-gap: 4vw;
    }

    .page-blog-post-latest-posts-list-item {
      border-radius: 2.67vw;
    }

    .page-blog-post-latest-posts-load-more-btn {
      padding: 2.27vw 20vw;
      font-size: 4vw;
      border-radius: 0.67vw;
    }

    .page-blog-post-latest-posts-load-more-btn:active,
    .page-blog-post-latest-posts-load-more-btn:focus {
      opacity: 0.7;
    }

    .page-blog-post-latest-posts-load-more-btn:hover {
      opacity: 1;
    }

    .page-blog-post-latest-posts-list-item-image-wrapper {
      border-radius: 1.67vw 1.67vw 0 0;
    }

    .page-blog-post-latest-posts-list-item-content {
      padding: 2.67vw;
    }

    .page-blog-post-latest-posts-list-item-link {
      font-size: 4.67vw;
    }

    .page-blog-post-latest-posts-list-item-date {
      font-size: 3vw;
      margin-top: 1.67vw;
    }
  }
</style>

{% comment %} GET ALL ARTICLES FROM ALL BLOGS {% endcomment %}
{% assign all_articles = '' | split: '' %}
{% for link in linklists.blogs.links %}
  {% assign blog_handle = link.handle %}
  {% assign current_blog = blogs[blog_handle] %}
  {% if current_blog %}
    {% assign all_articles = all_articles | concat: current_blog.articles %}
  {% endif %}
{% endfor %}
{% assign sorted_articles = all_articles | sort: 'published_at' | reverse %}

<div class="page-blog-wrapper">
  <div class="page-blog-title">
    <h1 class="page-blog-title-heading">{{ page.title }}</h1>
    <p class="page-blog-title-intro">The Way to a Better Sitting Experience</p>
    <p class="page-blog-title-content">
      We provide lastest News or some expert guide to help you choose the perfect products.
    </p>
  </div>

  <div class="page-blog-post-list">
    <div class="page-blog-post-list-left">
      {% for article in sorted_articles limit: 1 %}
        <div class="post-list-item">
          <div class="post-list-item-image-wrapper">
            <a class="post-list-item-image" href="{{ article.url }}">
              <img
                src="{{ article.image | image_url }}"
                alt="{{ article.image.alt }}"
                width="{{ article.image.width }}"
                height="{{ article.image.height }}"
                loading="lazy"
              >
            </a>
          </div>

          <div class="post-list-item-content">
            <a class="post-list-item-link link-underline" href="{{ article.url }}">
              {{ article.title }}
            </a>

            <div class="post-list-item-author">
              <span>By {{ article.author }}</span>
              <span>{{ article.published_at | date: '%d %B %Y' }}</span>
            </div>

            <p class="post-list-item-excerpt">
              {{ article.excerpt_or_content | strip_html | truncate: 200 }}
            </p>

            <div class="post-list-item-read-more-btn-wrapper">
              <a class="post-list-item-read-more-btn" href="{{ article.url }}">
                <span>Read more</span>
              </a>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>

    <div class="page-blog-post-list-right">
      {% for article in sorted_articles offset: 1 limit: 3 %}
        <div class="post-list-item">
          <div class="post-list-item-image-wrapper">
            <a class="post-list-item-image" href="{{ article.url }}">
              <img
                src="{{ article.image | image_url }}"
                alt="{{ article.image.alt }}"
                width="{{ article.image.width }}"
                height="{{ article.image.height }}"
                loading="lazy"
              >
            </a>
          </div>
          <div class="post-list-item-content">
            <a class="post-list-item-link link-underline" href="{{ article.url }}">
              {{ article.title }}
            </a>
            <div class="post-list-item-date">
              <span class="post-list-item-author">By {{ article.author }}</span>
              <span>{{ article.published_at | date: '%d %B %Y' }}</span>
            </div>
            <div class="post-list-item-read-more-btn-wrapper">
              <a class="post-list-item-read-more-btn" href="{{ article.url }}">
                <span>Read more</span>
              </a>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
  </div>

  <div class="page-blog-post-latest-posts">
    <div class="page-blog-post-latest-posts-title">Latest Posts</div>
    <div class="page-blog-post-latest-posts-nav">
      {% for link in linklists.blogs.links %}
        <a href="{{ link.url }}">
          {{ link.title }}
        </a>
      {% endfor %}
    </div>

    <div class="page-blog-post-latest-posts-list" id="blog-post-list">
      {% assign sorted_articles = sorted_articles %}
      {% for article in sorted_articles offset: 4 %}
        <div class="page-blog-post-latest-posts-list-item">
          <div class="page-blog-post-latest-posts-list-item-image-wrapper">
            <img
              src="{{ article.image | image_url }}"
              alt="{{ article.image.alt }}"
              width="{{ article.image.width }}"
              height="{{ article.image.height }}"
              loading="lazy"
            >
          </div>
          <div class="page-blog-post-latest-posts-list-item-content-wrapper">
            <div class="page-blog-post-latest-posts-list-item-content">
              <a class="page-blog-post-latest-posts-list-item-link link-underline" href="{{ article.url }}">
                {{ article.title }}
              </a>
              <div class="page-blog-post-latest-posts-list-item-date">
                {{ article.published_at | date: '%d %B %Y' }}
              </div>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
    {% comment %} load more button {% endcomment %}
    <div class="page-blog-post-latest-posts-load-more-btn-wrapper">
      <button id="load-more-btn" class="page-blog-post-latest-posts-load-more-btn">Load more</button>
    </div>
  </div>
</div>

<script>
  // Show 6 posts at a time, load more on button click with fade-in transition
  document.addEventListener('DOMContentLoaded', function () {
    const posts = document.querySelectorAll('.page-blog-post-latest-posts-list-item');
    const loadMoreBtn = document.getElementById('load-more-btn');

    let visibleCount = 6;
    const increment = 6;

    function showPosts() {
      const end = Math.min(visibleCount, posts.length);
      for (let i = 0; i < end; i++) {
        const post = posts[i];

        if (!post.classList.contains('visible')) {
          // Step 1: Make it visible for animation to apply
          post.style.display = 'block';

          // Step 2: Force reflow (this line ensures transition will trigger)
          void post.offsetWidth;

          // Step 3: Add the class that triggers opacity + transform transition
          post.classList.add('visible');
        }
      }

      // Hide button if all posts are visible
      if (visibleCount >= posts.length) {
        loadMoreBtn.style.display = 'none';
      }
    }

    loadMoreBtn.addEventListener('click', function () {
      visibleCount += increment;
      showPosts();
    });

    // Initial load
    showPosts();
  });
</script>
