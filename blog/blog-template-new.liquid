<div class="blog-container">
  {% paginate blog.articles by 5 %}
    <div class="blog-header">
      <div class="blog-header-category">browse category</div>
      <h1 class="blog-header-title">{{ blog.title }}</h1>
      <div class="blog-header-articles-count">
        <span>{{ blog.articles_count }} Articles</span>
      </div>
    </div>

    <div class="blog-articles">
      {% for article in blog.articles %}
        <article class="blog-article">
          <div class="blog-article-image-wrapper">
            <a class="blog-article-image" href="{{ article.url }}">
              <img
                src="{{ article.image | image_url }}"
                alt="{{ article.title }}"
                width="{{ article.image.width }}"
                height="{{ article.image.height }}"
              >
            </a>
          </div>

          <div class="blog-article-content">
            <a class="blog-article-title link-underline" href="{{ article.url }}">
              {{ article.title }}
            </a>

            <div class="blog-article-author">
              <span>By {{ article.author }}</span>
              <span>{{ article.published_at | date: '%B %d, %Y' }}</span>
            </div>

            <p class="blog-article-excerpt">
              {{ article.excerpt_or_content | strip_html | truncate: 200 }}
            </p>

            <a class="blog-article-read-more-btn" href="{{ article.url }}">
              <span>Read more</span>
            </a>
          </div>
        </article>
      {% endfor %}
    </div>

    <div class="blog-pagination">
      {% if paginate.previous %}
        <a class="blog-pagination-arrow" href="{{ paginate.previous.url }}">
          <svg
            style="transform: scaleX(-1);"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            fill="none"
            version="1.1"
            width="24"
            height="24"
            viewBox="0 0 24 24"
          >
            <defs><clipPath id="master_svg0_163_8424"><rect x="24" y="24" width="24" height="24" rx="0"/></clipPath></defs><g transform="matrix(-1,0,0,-1,48,48)" clip-path="url(#master_svg0_163_8424)"><g></g><g><path d="M36.70711,30.707107Q36.84776,30.566455,36.92388,30.382683Q37,30.198912,37,30Q37,29.9015086,36.98078,29.80491Q36.96157,29.708311,36.92388,29.617317Q36.88619,29.526322,36.83147,29.44443Q36.77675,29.362537,36.70711,29.292893Q36.63746,29.223249,36.55557,29.16853Q36.47368,29.113812,36.38268,29.076121Q36.29169,29.038429999999998,36.19509,29.019215Q36.09849,29,36,29Q35.80109,29,35.61732,29.07612Q35.43355,29.152241,35.29289,29.292893L35.292519999999996,29.293268L29.292893,35.29289Q29.152241,35.43355,29.07612,35.61732Q29,35.80109,29,36Q29,36.19891,29.07612,36.38268Q29.152241,36.56645,29.292893,36.70711L35.29192,42.7061L35.29289,42.7071Q35.43355,42.8478,35.61732,42.9239Q35.80109,43,36,43Q36.09849,43,36.19509,42.9808Q36.29169,42.961600000000004,36.38268,42.9239Q36.47368,42.8862,36.55557,42.8315Q36.63746,42.7767,36.70711,42.7071Q36.77675,42.6375,36.83147,42.5556Q36.88619,42.4737,36.92388,42.3827Q36.96157,42.2917,36.98078,42.1951Q37,42.0985,37,42Q37,41.8011,36.92388,41.6173Q36.84776,41.4335,36.70711,41.2929L31.41421,36L36.70673,30.707482L36.70711,30.707107L36.70711,30.707107Z" fill-rule="evenodd" fill="#141414" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M42.70711,30.707107Q42.84776,30.566455,42.92388,30.382683Q43,30.198912,43,30Q43,29.9015086,42.98078,29.80491Q42.96157,29.708311,42.92388,29.617317Q42.88619,29.526322,42.83147,29.44443Q42.77675,29.362537,42.70711,29.292893Q42.63746,29.223249,42.55557,29.16853Q42.47368,29.113812,42.38268,29.076121Q42.29169,29.038429999999998,42.19509,29.019215Q42.09849,29,42,29Q41.80109,29,41.61732,29.07612Q41.43355,29.152241,41.29289,29.292893L41.292519999999996,29.293268L35.292893,35.29289Q35.152241,35.43355,35.07612,35.61732Q35,35.80109,35,36Q35,36.19891,35.07612,36.38268Q35.152241,36.56645,35.292893,36.70711L41.29192,42.7061L41.29289,42.7071Q41.43355,42.8478,41.61732,42.9239Q41.80109,43,42,43Q42.09849,43,42.19509,42.9808Q42.29169,42.961600000000004,42.38268,42.9239Q42.47368,42.8862,42.55557,42.8315Q42.63746,42.7767,42.70711,42.7071Q42.77675,42.6375,42.83147,42.5556Q42.88619,42.4737,42.92388,42.3827Q42.96157,42.2917,42.98078,42.1951Q43,42.0985,43,42Q43,41.8011,42.92388,41.6173Q42.84776,41.4335,42.70711,41.2929L37.41421,36L42.70673,30.707482L42.70711,30.707107L42.70711,30.707107Z" fill-rule="evenodd" fill="#141414" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g>
          </svg>
        </a>
      {% endif %}
      {% for part in paginate.parts %}
        {% comment %} judge if part.title is a number or '...' {% endcomment %}
        {% assign part_is_number = part.title | plus: 0 %}
        {% if part.is_link %}
          <a class="blog-pagination-btn" href="{{ part.url }}">{{ part.title }}</a>
        {% elsif part_is_number == 0 %}
          <span class="blog-pagination-btn ellipsis">{{ part.title }}</span>
        {% else %}
          <span class="blog-pagination-btn current">{{ part.title }}</span>
        {% endif %}
      {% endfor %}
      {% if paginate.next %}
        <a class="blog-pagination-arrow" href="{{ paginate.next.url }}">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            fill="none"
            version="1.1"
            width="24"
            height="24"
            viewBox="0 0 24 24"
          >
            <defs><clipPath id="master_svg0_163_8424"><rect x="24" y="24" width="24" height="24" rx="0"/></clipPath></defs><g transform="matrix(-1,0,0,-1,48,48)" clip-path="url(#master_svg0_163_8424)"><g></g><g><path d="M36.70711,30.707107Q36.84776,30.566455,36.92388,30.382683Q37,30.198912,37,30Q37,29.9015086,36.98078,29.80491Q36.96157,29.708311,36.92388,29.617317Q36.88619,29.526322,36.83147,29.44443Q36.77675,29.362537,36.70711,29.292893Q36.63746,29.223249,36.55557,29.16853Q36.47368,29.113812,36.38268,29.076121Q36.29169,29.038429999999998,36.19509,29.019215Q36.09849,29,36,29Q35.80109,29,35.61732,29.07612Q35.43355,29.152241,35.29289,29.292893L35.292519999999996,29.293268L29.292893,35.29289Q29.152241,35.43355,29.07612,35.61732Q29,35.80109,29,36Q29,36.19891,29.07612,36.38268Q29.152241,36.56645,29.292893,36.70711L35.29192,42.7061L35.29289,42.7071Q35.43355,42.8478,35.61732,42.9239Q35.80109,43,36,43Q36.09849,43,36.19509,42.9808Q36.29169,42.961600000000004,36.38268,42.9239Q36.47368,42.8862,36.55557,42.8315Q36.63746,42.7767,36.70711,42.7071Q36.77675,42.6375,36.83147,42.5556Q36.88619,42.4737,36.92388,42.3827Q36.96157,42.2917,36.98078,42.1951Q37,42.0985,37,42Q37,41.8011,36.92388,41.6173Q36.84776,41.4335,36.70711,41.2929L31.41421,36L36.70673,30.707482L36.70711,30.707107L36.70711,30.707107Z" fill-rule="evenodd" fill="#141414" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M42.70711,30.707107Q42.84776,30.566455,42.92388,30.382683Q43,30.198912,43,30Q43,29.9015086,42.98078,29.80491Q42.96157,29.708311,42.92388,29.617317Q42.88619,29.526322,42.83147,29.44443Q42.77675,29.362537,42.70711,29.292893Q42.63746,29.223249,42.55557,29.16853Q42.47368,29.113812,42.38268,29.076121Q42.29169,29.038429999999998,42.19509,29.019215Q42.09849,29,42,29Q41.80109,29,41.61732,29.07612Q41.43355,29.152241,41.29289,29.292893L41.292519999999996,29.293268L35.292893,35.29289Q35.152241,35.43355,35.07612,35.61732Q35,35.80109,35,36Q35,36.19891,35.07612,36.38268Q35.152241,36.56645,35.292893,36.70711L41.29192,42.7061L41.29289,42.7071Q41.43355,42.8478,41.61732,42.9239Q41.80109,43,42,43Q42.09849,43,42.19509,42.9808Q42.29169,42.961600000000004,42.38268,42.9239Q42.47368,42.8862,42.55557,42.8315Q42.63746,42.7767,42.70711,42.7071Q42.77675,42.6375,42.83147,42.5556Q42.88619,42.4737,42.92388,42.3827Q42.96157,42.2917,42.98078,42.1951Q43,42.0985,43,42Q43,41.8011,42.92388,41.6173Q42.84776,41.4335,42.70711,41.2929L37.41421,36L42.70673,30.707482L42.70711,30.707107L42.70711,30.707107Z" fill-rule="evenodd" fill="#141414" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g>
          </svg>
        </a>
      {% endif %}
    </div>
  {% endpaginate %}
</div>

<style>
  /* underline hover */
  .link-underline {
    background-image: linear-gradient(90deg, currentColor, currentColor);
    background-repeat: no-repeat;
    background-size: 0% 1px;
    background-position: 0% 100%;
    transition: 1s cubic-bezier(0.25, 0.8, 0.25, 1);
    display: inline;
  }

  .link-underline:hover {
    background-size: 100% 1px;
    color: #336ca8;
  }

  .blog-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 96px 24px 64px 24px;
  }

  .blog-header {
    border: 1px solid #f0f0f0;
    padding: 32px 24px;
    border-radius: 16px;
  }

  .blog-header-category {
    font-size: 24px;
    font-weight: 500;
    color: #141414;
  }

  .blog-header-title {
    font-size: 36px;
    font-weight: 700;
    color: #141414;
    border-bottom: 2px solid;
    padding-bottom: 8px;
    margin: 24px 0 8px 0;
  }

  .blog-header-articles-count {
    text-align: right;
    width: 100%;
  }

  .blog-header-articles-count span {
    padding: 5px 8px;
    display: inline-block;
    border-radius: 2px;
    background-color: #f0f0f0;
    color: #141414;
    font-size: 12px;
    font-weight: 500;
  }

  .blog-articles {
    display: flex;
    flex-direction: column;
    padding: 32px 0 40px 0;
    gap: 24px;
  }

  .blog-article {
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 16px;
    border: 1px solid #f0f0f0;
    transition: 0.3s ease-in-out;
    padding: 24px 32px 24px 24px;
    gap: 24px;
  }

  .blog-article:hover {
    box-shadow: 0px 25px 35px 0px rgba(112, 121.00000000000001, 139, 0.2);
  }

  .blog-article-image-wrapper {
    flex: 7;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .blog-article-image {
    overflow: hidden;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .blog-article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 70 / 51;
    transition: 0.6s ease-in-out;
  }

  .blog-article-image img:hover {
    transform: scale(1.15);
  }

  .blog-article-content {
    flex: 8;
  }

  .blog-article-title {
    font-size: 24px;
    font-weight: 700;
    margin: 20px 0;
    word-wrap: break-word;
    text-decoration: none;
    color: #141414;
  }

  .blog-article-author {
    font-size: 18px;
    font-weight: 500;
    color: #8c8c8c;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
    text-transform: capitalize;
    font-style: italic;
    margin-top: 16px;
  }

  .blog-article-excerpt {
    font-size: 16px;
    font-weight: 500;
    color: #141414;
    line-height: 1.5;
    margin: 24px 0 0 0;
  }

  .blog-article-read-more-btn {
    display: inline-block;
    font-size: 18px;
    font-weight: 500;
    color: #ffffff;
    background: #141414;
    padding: 9px 24px;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    border-radius: 4px;
    transition: 0.3s ease-in-out;
    opacity: 1;
    margin-top: 32px;
  }

  .blog-article-read-more-btn:hover {
    opacity: 0.7;
  }

  {% comment %} pagination {% endcomment %}
  .blog-pagination {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 8px;
  }

  .blog-pagination-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    color: #141414;
    background-color: #ffffff;
    text-decoration: none;
    cursor: pointer;
    transition: 0.3s ease-in-out;
    border-radius: 4px;
    font-size: 24px;
    font-weight: 700;
  }

  .blog-pagination-btn:hover {
    background-color: #F0F0F0;
  }

  .blog-pagination-btn.current {
    background-color: #141414;
    color: #ffffff;
  }

  .blog-pagination-btn.ellipsis {
    cursor: default;
  }

  .blog-pagination-arrow {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: 0.3s ease-in-out;
    border-radius: 4px;
  }

  .blog-pagination-arrow:hover {
    background-color: #F0F0F0;
  }

  @media (max-width: 900px) {
    .blog-article {
      flex-direction: column;
      gap: 14px;
    }

    .blog-article-content {
      padding: 10px;
      width: calc(100% - 20px);
    }

    .blog-article-excerpt {
      display: none;
    }
  }

  @media (max-width: 600px) {
    .blog-container {
      padding: 6.67vw 4vw 9.33vw 4vw;
    }

    .blog-header {
      padding: 4vw;
      border-radius: 2.67vw;
    }

    .blog-header-category {
      font-size: 4vw;
    }

    .blog-header-title {
      font-size: 6vw;
      margin: 4vw 0 1.33vw 0;
    }

    .blog-header-articles-count span {
      font-size: 3vw;
      padding: 0.83vw 1.33vw;
    }

    .blog-articles {
      padding: 4vw 0 6.67vw 0;
      gap: 4vw;
    }

    .blog-article {
      padding: 4vw 4vw 5.33vw 4vw;
      gap: 4vw;
    }

    .blog-article-image {
      border-radius: 1.33vw;
    }

    .blog-article-content {
      padding: 0 2vw 0 1.33vw;
      width: unset;
    }

    .blog-article-title {
      font-size: 4.67vw;
      margin:0;
    }

    .blog-article-author {
      font-size: 4vw;
      margin-top: 2.67vw;
      gap: 4vw;
    }

    .blog-article-read-more-btn {
      font-size: 4vw;
      margin-top: 5.33vw;
      padding: 2.25vw 5.33vw;
      border-radius: 0.67vw;
    }

    .blog-pagination {
      gap: 1.33vw;
    }

    .blog-pagination-btn {
      border-radius: 0.67vw;
      font-size: 4vw;
      width: 6.67vw;
      height: 6.67vw;
    }

    .blog-pagination-arrow {
      border-radius: 0.67vw;
      width: 6.67vw;
      height: 6.67vw;
    }

    .blog-pagination-arrow svg {
      width: 4vw;
      height: 4vw;
    }

  }
</style>
