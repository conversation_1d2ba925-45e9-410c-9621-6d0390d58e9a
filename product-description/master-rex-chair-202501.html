<!-- Slick.js CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.css">

<style>
    .master-rex-chair-description {
        --large-heading-size: 52px;
        --body-size: 24px;
        --medium-body-size: 18px;
        --small-body-size: 16px;
        width: 100%;
        max-width: 1920px;
        margin: 0 auto;
    }

    .master-rex-chair-description img {
        display: block;
    }

    /* slick.js custom dot style */
    .slick-dot-custom {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        padding: 0;
        list-style: none;
        display: flex;
        width: fit-content;
        margin: 1em auto;
    }

    .slick-dot-custom .slick-active button {
        background-color: #FFFFFF;
    }

    .slick-dot-custom button {
        width: 14px;
        height: 14px;
        box-sizing: border-box;
        border: 1px solid #FFFFFF;
        color: transparent;
        background-color: transparent;
        padding: 0;
        border-radius: 14px;
        cursor: pointer;
        margin: 0 3px;
    }

    /* intro */
    .master-rex-chair-intro {
        width: 100%;
        position: relative;
        margin-bottom: -30px;
    }

    .master-rex-chair-intro .intro__text__wrapper {
        position: absolute;
        bottom: 8.98%;
        width: 100%;
    }

    .master-rex-chair-intro .intro__text {
        width: 83.54%;
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 18.14%;
        color: #FFFFFF;
    }

    .master-rex-chair-intro .intro__text__title {
        font-size: var(--large-heading-size);
        font-weight: 700;
        flex: 461;
    }

    .master-rex-chair-intro .intro__text__content {
        font-size: var(--body-size);
        line-height: 1.5;
        flex: 852;
    }

    .master-rex-chair-intro .intro__background {
        overflow: hidden;
        display: flex;
        justify-content: center;
    }

    .master-rex-chair-intro .intro__background img {
        max-width: 1920px;
        height: auto;
    }

    /* support */
    .master-rex-chair-support {
        width: 100%;
        position: relative;
    }

    .master-rex-chair-support .support__text__wrapper {
        width: 100%;
        position: absolute;
        top: 10.34%;
    }

    .master-rex-chair-support .support__text {
        color: #FFFFFF;
        width: 47.71%;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
    }

    .master-rex-chair-support .support__text__title {
        font-size: var(--large-heading-size);
        font-weight: 700;
    }

    .master-rex-chair-support .support__text__content {
        font-size: var(--body-size);
        line-height: 1.5;
        margin-top: 3.49%;
    }

    .master-rex-chair-support .support__background {
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .master-rex-chair-support .support__background img {
        max-width: 1920px;
        height: auto;
    }

    /* function */
    .master-rex-chair-function {
        width: 100%;
        position: relative;
        padding-bottom: 40px;
    }

    .master-rex-chair-function .function__text__wrapper {
        position: absolute;
        top: 29.78%;
        right: 7.83%;
        width: 30.94%;
    }

    .master-rex-chair-function .function__text {
        color: #FFFFFF;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .master-rex-chair-function .function__text__title {
        font-size: var(--large-heading-size);
        font-weight: 700;
    }

    .master-rex-chair-function .function__text__content {
        font-size: var(--body-size);
        line-height: 1.5;
        margin-top: 5.39%;
    }

    .master-rex-chair-function .function__background {
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .master-rex-chair-function .function__background img {
        max-width: 1920px;
        height: auto;
    }

    /* feature */
    .master-rex-chair-feature {
        width: 83.54%;
        margin: 0 auto;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        margin-top: 80px;
        margin-bottom: 80px;
        gap: 11.03%;
    }

    .master-rex-chair-feature .feature__text {
        width: 100%;
        flex: 667;
    }

    .master-rex-chair-feature .feature__text__title {
        font-size: var(--large-heading-size);
        font-weight: 700;
    }

    .master-rex-chair-feature .feature__text__content {
        font-size: var(--body-size);
        line-height: 1.5;
        margin-top: 4.8%;
    }

    .master-rex-chair-feature .feature__background {
        flex: 760;
    }

    .master-rex-chair-feature .feature__background img {
        max-width: 760px;
        width: 100%;
        height: auto;
    }

    /* rest */
    .master-rex-chair-rest {
        width: 100%;
        display: flex;
    }

    .master-rex-chair-rest .rest__left-side img {
        max-width: 1120px;
        width: 100%;
        height: auto;
    }

    .master-rex-chair-rest .rest__right-side {
        position: relative;
    }

    .master-rex-chair-rest .rest__right-side .rest__text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #FFFFFF;
        width: 72.5%;
    }

    .master-rex-chair-rest .rest__right-side .rest__text__title {
        font-size: var(--large-heading-size);
        font-weight: 700;
    }

    .master-rex-chair-rest .rest__right-side .rest__text__content {
        font-size: var(--body-size);
        line-height: 1.5;
        margin-top: 4%;
    }

    .master-rex-chair-rest .rest__right-side .rest__background {
        width: 100%;
    }

    .master-rex-chair-rest .rest__right-side .rest__background img {
        max-width: 800px;
        width: 100%;
        height: auto;
    }

    /* upholstery */
    .visiable-pu .switch-pu {
        box-shadow: 0 0 0 3px #fff;
    }

    .visiable-pu .upholstery-pu {
        opacity: 1;
        height: auto;
    }

    .visiable-fabric .switch-fabric {
        box-shadow: 0 0 0 3px #fff;
    }

    .visiable-fabric .upholstery-fabric {
        opacity: 1;
        height: auto;
    }

    .product-section--upholstery {
        width: 100%;
        position: relative;
    }

    .upholstery-switcher-wrap {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 9.25%;
        z-index: 2;
        display: flex;
    }

    .switch-pu {
        box-shadow: 0 0 0 3px #fff
    }

    .upholstery-pu {
        display: block;
    }

    .switch-fabric {
        box-shadow: 0 0 0 3px #fff
    }

    .upholstery-fabric {
        opacity: 1;
        height: auto;
    }

    .upholstery-switch {
        border-radius: 10px;
        width: 200px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-weight: 700;
        font-size: 18px;
        margin: 0 8px;
        cursor: pointer;
        box-shadow: 0 0 0 1px #fff;
        transition: all 300ms ease-in-out;
        background: #00000045;
    }

    .upholstery-switch:hover {
        box-shadow: 0 0 0 3px #fff
    }

    .upholstery-sections-list {
        width: 100%;
        position: relative;
        background-color: #000000;
    }

    .upholstery-section {
        width: 100%;
        overflow: hidden;
        position: relative;
        transition: 2.5s ease-out;
        opacity: 0;
        height: 0;
    }

    .upholstery-content-wrap {
        position: absolute;
        top: 25%;
        left: 12.76%;
        color: #FAFAFA;
    }

    .upholstery-title {
        font-size: var(--large-heading-size);
        font-weight: 700;
    }

    .upholstery-paragraph {
        font-size: var(--body-size);
        line-height: 1.5;
        margin-top: 16px;
        max-width: 650px;
    }

    .upholstery-paragraph .p-tip {
        font-size: var(--medium-body-size);
        margin-top: 8px;
        display: block;
        line-height: 1.5;
    }

    .upholstery-background {
        overflow: hidden;
        display: flex;
        justify-content: center;
    }

    .upholstery-background img {
        max-width: 1920px;
        height: auto;
    }

    /* components */
    .master-series-components {
        width: 100%;
        padding-top: 6.67%;
        padding-bottom: 6.67%;
    }

    .master-series-components .components-title {
        text-align: center;
        margin: 3.75% auto;
        font-size: var(--large-heading-size);
        color: #141414;
        font-weight: 700;
    }

    .master-series-components .component-group-wrap {
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden;
    }

    .master-series-components .component-group {
        width: fit-content;
        display: flex;
        margin: 0 auto;
    }

    .master-series-components .component-group .component {
        width: 418px;
        margin: 0 12px;
    }

    .master-series-components .component-group .component img {
        width: 100%;
        height: auto;
    }

    .master-series-components .component__desc {
        margin-top: 24px;
    }

    .master-series-components .component__name {
        font-size: var(--body-size);
        color: #141414;
        font-weight: 700;
    }

    .master-series-components .component__content {
        font-size: var(--small-body-size);
        color: #8C8C8C;
        font-weight: 400;
        width: 95%;
        margin-top: 12px;
    }

    @media screen and (max-width: 1440px) {
        .master-rex-chair-description {
            --large-heading-size: 42px;
            --body-size: 22px;
            --medium-body-size: 16px;
        }

        /* intro */
        .master-rex-chair-intro .intro__text__wrapper {
            bottom: 10.61%;
        }

        .master-rex-chair-intro .intro__text {
            width: 87.29%;
            gap: 9.375%;
        }

        .master-rex-chair-intro .intro__text__title {
            font-size: 36px;
            flex: 16;
        }

        .master-rex-chair-intro .intro__text__content {
            flex: 41;
        }

        /* support */
        .master-rex-chair-support .support__text {
            width: 58.26%;
        }

        /* function */
        .master-rex-chair-function .function__text__wrapper {
            top: 24.35%;
            right: 5.07%;
            width: 32.29%;
        }

        /* feature */
        .master-rex-chair-feature {
            width: 87.78%;
            gap: 6.04%;
        }

        .master-rex-chair-feature .feature__background img {
            max-width: 608px;
        }

        .master-rex-chair-feature .feature__text {
            flex: 569;
        }

        .master-rex-chair-feature .feature__text__content {
            margin-top: 2.8%;
        }

        .master-rex-chair-feature .feature__background {
            flex: 608;
        }

        /* rest */
        .master-rex-chair-rest .rest__right-side .rest__text {
            width: 77%;
        }

        /* img */
        .master-rex-chair-intro .intro__background img,
        .master-rex-chair-support .support__background img,
        .master-rex-chair-function .function__background img {
            max-width: 1440px;
        }

        .slick-dot-custom button {
            width: 14px;
            height: 14px;
        }

        .upholstery-content-wrap {
            top: 18.67%;
            left: 6.11%;
        }

        .upholstery-background img {
            max-width: 1440px;
        }

        .master-series-components .components-title {
            margin: 0 auto;
            margin-bottom: 4.44%;
        }

        .master-series-components .component-group .component {
            width: 398px;
        }

        .master-series-components .component__name {
            font-size: 24px;
        }

    }

    @media screen and (max-width: 1200px) {
        .master-rex-chair-description {
            --large-heading-size: 36px;
            --body-size: 20px;
        }

        .slick-dot-custom button {
            width: 12px;
            height: 12px;
        }

        /* intro */
        .master-rex-chair-intro .intro__text {
            width: 91.67%;
            gap: 4.75%;
        }

        .master-rex-chair-intro .intro__text__title {
            font-size: var(--large-heading-size);
            flex: 321;
        }

        .master-rex-chair-intro .intro__text__content {
            flex: 722;
        }

        /* support */
        .master-rex-chair-support .support__text__wrapper {
            top: 7.72%;
        }

        .master-rex-chair-support .support__text {
            width: 62%;
        }

        .master-rex-chair-support .support__text__content {
            margin-top: 8px;
        }

        /* function */
        .master-rex-chair-function {
            padding-bottom: 0px;
        }

        .master-rex-chair-function .function__text__wrapper {
            top: 16.43%;
            right: 4.33%;
            width: 33%;
        }

        .master-rex-chair-function .function__text__content {
            margin-top: 8px;
        }

        /* feature */
        .master-rex-chair-feature {
            width: 92%;
            gap: 3.67%;
        }

        .master-rex-chair-feature .feature__text {
            flex: 113;
        }

        .master-rex-chair-feature .feature__text__content {
            margin-top: 8px;
        }

        .master-rex-chair-feature .feature__background {
            flex: 152;
        }

        /* rest */
        .master-rex-chair-rest .rest__right-side .rest__text__content {
            margin-top: 8px;
        }

        /* img */
        .master-rex-chair-intro .intro__background img,
        .master-rex-chair-support .support__background img,
        .master-rex-chair-function .function__background img {
            max-width: 1200px;
        }

        .upholstery-background img {
            max-width: 1200px;
        }

        .upholstery-content-wrap {
            top: 12.8%;
            left: 6%;
        }

        .upholstery-paragraph {
            max-width: 549px;
        }

        .master-series-components .component__name {
            font-size: 22px;
        }

        .master-series-components .component-group .component {
            width: 321px;
        }
    }

    @media screen and (max-width: 900px) {
        .master-rex-chair-description {
            --large-heading-size: 28px;
            --body-size: 18px;
            --medium-body-size: 14px;
        }

        .slick-dot-custom button {
            width: 10px;
            height: 10px;
        }

        .master-rex-chair-support .support__text {
            width: 62%;
            min-width: 584px;
        }

        .master-rex-chair-function .function__text__wrapper {
            top: 11.61%;
            right: 7.11%;
            width: 42.67%;
        }

        .master-rex-chair-rest .rest__right-side .rest__text {
            width: 84.5%;
        }

        /* feature */
        .master-rex-chair-feature {
            flex-direction: column;
            margin: 5.33% 7.78%;
            width: unset;
        }

        .master-rex-chair-feature .feature__background img {
            max-width: 760px;
        }

        .master-rex-chair-feature .feature__text__content {
            margin-bottom: 24px;
        }

        .master-rex-chair-intro .intro__background img,
        .master-rex-chair-support .support__background img,
        .master-rex-chair-function .function__background img {
            max-width: 900px;
        }

        .upholstery-content-wrap {
            top: 10.67%;
            left: 5.33%;
        }

        .upholstery-paragraph {
            margin-top: 8px;
            max-width: 510px;
        }

    }

    @media screen and (max-width: 800px) {

        /* rest */
        .master-rex-chair-rest {
            flex-direction: column-reverse;
        }

        .master-rex-chair-rest .rest__right-side .rest__text__content {
            margin-top: 8px;
        }
    }

    @media (max-width: 600px) {

        .master-rex-chair-description {
            --large-heading-size: 7vw;
            --body-size: 4.67vw;
            --medium-body-size: 3vw;
            --small-body-size: 2.67vw;
        }

        .master-rex-chair-intro {
            display: flex;
            flex-direction: column-reverse;
        }

        .master-rex-chair-intro .intro__text__wrapper {
            position: unset;
            width: 100%;
            background-color: #000000;
        }

        .master-rex-chair-intro .intro__text {
            width: unset;
            flex-direction: column;
            text-align: center;
            margin: 8vw 4.33vw 0 4.33vw;
        }

        .master-rex-chair-intro .intro__text__content {
            margin-top: 2.67vw;
        }

        .master-rex-chair-intro .intro__background img {
            width: 100%;
        }

        /* support */
        .master-rex-chair-support {
            flex-direction: column-reverse;
        }

        .master-rex-chair-support .support__text__wrapper {
            width: 100%;
            position: unset;
            background-color: #000000;
        }

        .master-rex-chair-support .support__text {
            width: unset;
            min-width: unset;
            text-align: left;
            padding: 8vw 5.33vw 0 5.33vw;
        }

        .master-rex-chair-support .support__background img {
            width: 100%;
        }

        /* function */
        .master-rex-chair-function {
            padding-bottom: 0;
        }

        .master-rex-chair-function .function__text__wrapper {
            width: 81%;
            left: 9.5vw;
            right: 9.5vw;
            top: 6.55vw;
        }

        .master-rex-chair-function .function__text {
            text-align: center;
        }

        .master-rex-chair-function .function__text__content {
            margin-top: 2.67vw;
        }

        .master-rex-chair-function .function__background img {
            width: 100%;
        }

        /* feature */
        .master-rex-chair-feature {
            flex-direction: column;
            margin: 8vw 5.33vw;
            width: unset;
        }

        .master-rex-chair-feature .feature__text__content {
            margin-top: 2.67vw;
            margin-bottom: 4vw;
        }

        /* rest */
        .master-rex-chair-rest {
            flex-direction: column-reverse;
        }

        .master-rex-chair-rest .rest__right-side .rest__text {
            width: unset;
            margin: 22.67vw 5.33vw 0 5.33vw;
            top: unset;
            left: unset;
            transform: unset;
        }

        .master-rex-chair-rest .rest__right-side .rest__text__content {
            margin-top: 2.67vw;
        }

        /* upholstery */
        .upholstery-switch {
            width: 33.33vw;
            height: 8.33vw;
            font-size: 3vw;
            border-radius: 1.67vw;
        }

        .upholstery-content-wrap {
            top: 5.5%;
            left: 50%;
            transform: translate(-50%, 0);
            width: 90%;
        }

        .upholstery-paragraph {
            font-size: 4vw;
            margin-top: 2.67vw;
        }

        .upholstery-background {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .upholstery-background img {
            width: 100%;
            height: auto;
        }

        /* components */
        .master-series-components {
            padding-top: 12vw;
            padding-bottom: 8vw;
        }

        .master-series-components .components-title {
            margin: 0;
            margin-bottom: 8vw;
        }

        .master-series-components .component__desc {
            margin-top: 4vw;
        }

        .master-series-components .component__name {
            font-size: 4vw;
        }

        .master-series-components .component__content {
            margin-top: 2vw;
        }

        .master-series-components .component-group .component {
            margin: 0 2vw;
            width: 67vw;
        }

    }
</style>

<div class="master-rex-chair-description">

    <div class="master-rex-chair-intro">
        <div class="intro__background intro__slider-container">
            <picture>
                <source media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_1_sp_600x.jpg?v=1737598199">
                <img alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_1_1920x.jpg?v=1737598199">
            </picture>
            <picture>
                <source media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_1-2_sp_600x.jpg?v=1737598199">
                <img alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_1-2_1920x.jpg?v=1737598199">
            </picture>
        </div>
        <div class="intro__text__wrapper">
            <div class="intro__text">
                <div class="intro__text__title">
                    Enhanced Comfort for<br>
                    Work and Leisure
                </div>
                <div class="intro__text__content">
                    With its adjustable features and a sleek, modern aesthetic, Boulies Master Rex chair offers
                    unparalleled sitting comfort and better body support, whether you’re working hard or relaxing.
                    Designed with the integrated leg rest, this chair ensures pressure-free reclining. Ergonomic support
                    promotes better posture and boosts productivity throughout your day.
                </div>
            </div>
        </div>
    </div>

    <div class="master-rex-chair-support">
        <div class="support__text__wrapper">
            <div class="support__text">
                <div class="support__text__title">
                    Customizable Integrated Lumbar Support
                </div>
                <div class="support__text__content">
                    Adding the up and down adjustment, you can fine-tune the lumbar support to precisely align with your
                    spine's natural curvature. Whether you require a higher or lower level of support, this chair allows
                    you to customize it to your exact specifications. Say goodbye to generic, one-size-fits-all
                    solutions
                    and embrace a chair that adapts to your body.
                </div>
            </div>
        </div>
        <div class="support__background">
            <picture>
                <source media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_2_sp_600x.jpg?v=1737598493">
                <img alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_2_1920x.jpg?v=1737598493">
            </picture>
        </div>
    </div>

    <div class="master-rex-chair-function">
        <div class="function__text__wrapper">
            <div class="function__text">
                <div class="function__text__title">
                    Comfortable Recline and
                    Rocking Function
                </div>
                <div class="function__text__content">
                    Boulies Master Rex chair offers the back recline from 95° to 165°, allowing you to find the perfect
                    angle
                    for work, leisure, or relax. With our robust and stable tilt mechanism, it ensures smooth rocking or
                    a securely fixed tilt backwards position. Designed for ultimate relaxation and ergonomic support,
                    Boulies Master Rex chair provides enhanced comfort at every angle position.
                </div>
            </div>
        </div>
        <div class="function__background">
            <picture>
                <source media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_3_sp_600x.jpg?v=1737598597">
                <source media="(max-width:900px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_3-900px_900x.jpg?v=1737598597">
                <img alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_3_1920x.jpg?v=1737598596">
                </source>
            </picture>
        </div>
    </div>

    <div class="master-rex-chair-feature">
        <div class="feature__text">
            <div class="feature__text__title">
                Tailored Round Edge
                Cushion Design
            </div>
            <div class="feature__text__content">
                Adopted a round edge design of Boulies Master, Boulies Master Rex chair seat cushion is carefully
                crafted
                to provide optimal ergonomics. By eliminating sharp corners and embracing smooth curves, this cushion
                conforms to the natural contours of your body, promoting proper posture and reducing pressure on your
                hips and thighs. Furthermore, it offers a wider seating area where you can find your ideal seating
                posture and enjoy a comfortable experience tailored to you.
            </div>
        </div>
        <div class="feature__background">
            <picture>
                <img alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_4_800x.jpg?v=1737598746">
            </picture>
        </div>
    </div>

    <div class="master-rex-chair-feature">
        <div class="feature__text">
            <div class="feature__text__title">
                Aluminum Constructed
                4D Armrests
            </div>
            <div class="feature__text__content">
                Experience the pinnacle of luxury and durability with Boulies Aluminum constructed 4D armrests. The
                internal
                structure is crafted from high-grade aluminum alloy, making the armrests stronger, smoother, and more
                durable than ever before. We’ve also redesigned the arm pods to be softer, providing optimal comfort and
                support for your elbows.
            </div>
        </div>
        <div class="feature__background">
            <picture>
                <img alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_5_800x.jpg?v=1737598746">
            </picture>
        </div>
    </div>

    <div class="master-rex-chair-feature">
        <div class="feature__text">
            <div class="feature__text__title">
                Enhanced Stable Tilt Mechanism
            </div>
            <div class="feature__text__content">
                Boulies enhanced tilt mechanism allows for a smooth 15° rocking and tilt backwards position locked,
                providing
                a relaxing experience when leaning back. With its improved design and durable materials, the tilt
                mechanism
                ensures exceptional stability for a secure and comfortable seat experience.
            </div>
        </div>
        <div class="feature__background">
            <picture>
                <img alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_6_800x.jpg?v=1737598747">
            </picture>
        </div>
    </div>

    <div class="master-rex-chair-rest">
        <div class="rest__left-side">
            <picture>
                <source media="(max-width:800px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_7_sp_800x.jpg?v=1737598747">
                <img alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_7_1120x.jpg?v=1737598746">
            </picture>
        </div>

        <div class="rest__right-side">
            <div class="rest__text">
                <div class="rest__text__title">
                    Integrated Soft Leg Rest
                </div>
                <div class="rest__text__content">
                    Equipped with a pressure-free leg rest, Boulies Master Rex chair helps you enjoy a thoughtful add-on
                    enhanced relaxation. Your thighs will enjoy the ultimate comfort after a long day of work or
                    entertainment. Effortlessly activate this feature and let your body relax in no time, ensuring a
                    seamless transition to comfort and rejuvenation.
                </div>
            </div>
            <div class="rest__background">
                <picture>
                    <img alt=""
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_8_800x.jpg?v=1737598747">
                </picture>
            </div>
        </div>
    </div>

    <div class="product-section--upholstery visiable-pu">
        <div class="upholstery-switcher-wrap">
            <div class="upholstery-switch switch-pu" data-active-class="visiable-pu">Ultraflex PU</div>
            <div class="upholstery-switch switch-fabric" data-active-class="visiable-fabric">W/R Fabric</div>
        </div>
        <div class="upholstery-sections-list">
            <div class="upholstery-section upholstery-pu">
                <div class="upholstery-content-wrap">
                    <div class="upholstery-title">
                        Boulies Ultraflex PU
                    </div>
                    <div class="upholstery-paragraph">
                        After rigorous testing and countless iterations, we proudly present our signature Boulies
                        Ultraflex PU leather. Crafted with microfiber suede inspiration from the luxurious upholstery
                        found in sports cars, our Ultraflex PU leather is a remarkable blend of durability and indulgent
                        comfort. It delivers a buttery soft and smooth feel that enhances your sitting experience and
                        becomes the perfect companion for every gaming enthusiast and work alike.
                    </div>
                </div>
                <div class="upholstery-background">
                    <picture>
                        <source media="(max-width:600px)"
                            srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_9_sp_800x.jpg?v=1737598746">
                        <img
                            src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_9_1920x.jpg?v=1737598746">
                    </picture>
                </div>
            </div>
            <div class="upholstery-section upholstery-fabric">
                <div class="upholstery-content-wrap">
                    <div class="upholstery-title">
                        Boulies Water Repellent Fabric
                    </div>
                    <div class="upholstery-paragraph">
                        An upgrade that takes durability and softness to new heights. We have carefully selected premium
                        materials and refined the manufacturing process to create a fabric that can excel in both
                        endurance and comfort. Additionally, stay cool and comfortable with its breathable properties.
                        No need to worry about spills or accidents, as the fabric is specially treated to repel water
                        and prevent absorption.
                        <span class="p-tip">
                            * The hydrophobic effect will gradually disappear over time. The degree and speed of the
                            disappearance are related to the method and intensity of use.
                        </span>
                    </div>
                </div>
                <div class="upholstery-background">
                    <picture>
                        <source media="(max-width:600px)"
                            srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_9-2_sp_800x.jpg?v=1737598747">
                        <img
                            src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_9-2_1920x.jpg?v=1737598747">
                    </picture>
                </div>
            </div>
        </div>
    </div>

    <div class="master-series-components">
        <div class="components-title">Featured Components </div>
        <div class="component-group-wrap">
            <div class="component-group">
                <div class="component">
                    <img src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_10_500x.jpg?v=1737598746"
                        alt="">
                    <div class="component__desc">
                        <div class="component__name">
                            XL PU casters
                        </div>
                        <p class="component__content">
                            Made to be durable yet wooden floor friendly, XL PU caster is so smooth that can be used on
                            the clean wooden floor.
                        </p>
                    </div>
                </div>
                <div class="component">
                    <img src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_11_500x.jpg?v=1737598746"
                        alt="">
                    <div class="component__desc">
                        <div class="component__name">
                            Aluminum base
                        </div>
                        <p class="component__content">
                            Upgraded with a premium metal to provide extremely stability and strength, each base is not
                            only perfectly balanced but also strong bearing capacity.
                        </p>
                    </div>
                </div>
                <div class="component">
                    <img src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_Rex-detail_12_500x.jpg?v=1737598746"
                        alt="">
                    <div class="component__desc">
                        <div class="component__name">
                            Class-4 gas lift
                        </div>
                        <p class="component__content">
                            The best class of the gas hydraulics for stability and safety, including safety
                            certifications such as BIFMA(by SGS).
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- specification start -->
    <div class="bt20-cd_spec" id="bt20-collectionSpecifications">
        <div class="bprtdd_item bprtdd_detail" id="singleProductDetail">
            <h2 class="bprtdd_detailTitle">Product Warranty</h2>
            <div class="bprtdd_detailWrapper clearfix">
                <div class="bprtdd_warrantySection"><span class="bps_warrantyTime" data-warrantytime="24"> 2 years
                        limited
                        Warranty <br> <a href="/pages/warranty">Learn More</a> </span></div>
                <div class="bprtdd_warrantySection"><span class="bps_warrantyTime" data-warrantytime="24"> 14 days
                        return
                        <br> <a href="/pages/return-and-refund">Learn More</a> </span></div>
            </div>
        </div>
        <div class="bprtdd_item bprtdd_detail" id="singleProductDetail">
            <h2 class="bprtdd_detailTitle">Product Details</h2>
            <div class="bprtdd_detailWrapper clearfix">
                <div class="bprtdd_detail_line">
                    <div class="bprtdd_detail_line_head">
                        <h3>DIMENSIONS</h3>
                    </div>
                    <div class="bprtdd_detail_line_body">
                        <div class="bt51-dimensions-list">
                            <a href="https://cdn.shopify.com/s/files/1/2127/6275/files/master_rex_dimensions.jpg?v=1737597939"
                                class="bt51-dimension dynamic-gallery-item">
                                <img src="https://cdn.shopify.com/s/files/1/2127/6275/files/master_rex_dimensions_300x.jpg?v=1737597939"
                                    alt="" class="bt51-dimension-image-preview">
                                <p class="bt51-dimension-decription">Technical Specification - Master Rex</p>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="bprtdd_detail_line">
                    <div class="bprtdd_detail_line_head">
                        <h3>FUNCTIONS</h3>
                    </div>
                    <div class="bprtdd_detail_line_body">
                        <div class="bprtdd_detail_section">360° swivel</div>
                        <div class="bprtdd_detail_section">Seat height adjustable</div>
                        <div class="bprtdd_detail_section">Full-length backrest recline</div>
                        <div class="bprtdd_detail_section">Built-in lumbar support</div>
                        <div class="bprtdd_detail_section">15° rocking</div>
                        <div class="bprtdd_detail_section">Tilt position lock</div>
                        <div class="bprtdd_detail_section">Armrest 8 ways adjustable</div>
                        <div class="bprtdd_detail_section">Built-in leg rest</div>
                    </div>
                </div>
                <div class="bprtdd_detail_line">
                    <div class="bprtdd_detail_line_head">
                        <h3>SPECIFICATION</h3>
                    </div>
                    <div class="bprtdd_detail_line_body">
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Upholstery</span> <span
                                class="bprtdd_detail_itemDescribe">
                                Boulies ultraflex leather (with partial perforated) + microfiber suede<br> Boulies W/R
                                fabric + microfiber </span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Frame</span> <span
                                class="bprtdd_detail_itemDescribe">Extra
                                Strong Steel Frame </span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName"> Five star foot base</span> <span
                                class="bprtdd_detail_itemDescribe">Aluminum Base</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Foam</span> <span
                                class="bprtdd_detail_itemDescribe">100%
                                Cold-cure foam</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Tilt mechanism</span> <span
                                class="bprtdd_detail_itemDescribe">Multi tilt</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Casters</span> <span
                                class="bprtdd_detail_itemDescribe">6cm
                                PU casters</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Hydraulics</span> <span
                                class="bprtdd_detail_itemDescribe">Class 4 (Top Class)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- specification end -->

</div>


<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

<script>
    // Due to loading order issues, load slick finally
    $(window).on('load', function () {
        $('.intro__slider-container').slick({
            slidesToShow: 1,
            slidesToScroll: 1,
            dots: true,
            arrows: false,
            speed: 300,
            dotsClass: 'slick-dot-custom'
        });

        document.querySelector('.product-section--upholstery').addEventListener('click', function (e) {
            if (e.target.classList.contains('upholstery-switch')) {
                this.classList.remove('visiable-fabric', 'visiable-pu');
                this.classList.add(e.target.dataset.activeClass);
            };
        });
    });
</script>