<!-- Slick.js CSS -->
<link
    rel="stylesheet"
    href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.min.css" />
<link
    rel="stylesheet"
    href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.css" />

<style>
    .ep180-product-description {
        --large-heading-size: 48px;
        --medium-heading-size: 36px;
        --body-size: 24px;
        --heading-color: #141414;
        --body-color: #595959;

        width: 100%;
        max-width: 1920px;
        margin: 0 auto;
    }

    .ep180-product-description img {
        display: block;
    }

    /* poster */
    .ep180-description-poster img {
        width: 100%;
        height: auto;
    }

    /* ready */
    .ep180-description-ready {
        width: 100%;
        position: relative;
    }

    .ep180-description-ready__background {
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .ep180-description-ready__background img {
        max-width: 1920px;
        height: auto;
    }

    .ep180-description-ready__text {
        position: absolute;
        left: 8.44%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 562px;
    }

    .ep180-description-ready__text__title {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .ep180-description-ready__text__content {
        font-size: var(--body-size);
        color: var(--body-color);
        margin-top: 16px;
        line-height: 1.5;
    }

    /* last */
    .ep180-description-last {
        width: 100%;
        position: relative;
    }

    .ep180-description-last__background {
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .ep180-description-last__background img {
        max-width: 1920px;
        height: auto;
    }

    .ep180-description-last__text {
        position: absolute;
        right: 7.08%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 608px;
    }

    .ep180-description-last__text__title {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .ep180-description-last__text__content {
        font-size: var(--body-size);
        color: var(--body-color);
        margin-top: 16px;
        line-height: 1.5;
    }

    /* function */
    .ep180-description-function {
        width: 100%;
        background-color: #f0f0f0;
        display: flex;
        flex-direction: row-reverse;
    }

    .ep180-description-function__img {
        flex: 840;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .ep180-description-function__img img {
        max-width: 840px;
        width: 100%;
        height: auto;
    }

    .ep180-description-function__text {
        flex: 1080;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .ep180-description-function__text__title {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
        width: 62.96%;
    }

    .ep180-description-function__text__content {
        font-size: var(--body-size);
        color: var(--body-color);
        margin-top: 16px;
        line-height: 1.5;
        width: 62.96%;
    }

    .ep180-description-function__text__img {
        margin-top: 32px;
        width: 62.96%;
    }

    .ep180-description-function__text__img img {
        max-width: 680px;
        width: 100%;
        height: auto;
    }

    .ep180-description-customisable {
        width: 100%;
    }

    .ep180-description-customisable__text {
        margin: 120px auto 40px;
        width: 76.875%;
        display: flex;
        flex-direction: row;
        gap: 15.51%;
    }

    .ep180-description-customisable__text__title {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
        flex: 473;
    }

    .ep180-description-customisable__text__content {
        font-size: var(--body-size);
        color: var(--body-color);
        line-height: 1.5;
        flex: 774;
    }

    .ep180-description-customisable__text__content .text-strong {
        font-weight: 700;
        color: #141414;
    }

    /* img-group-slider-container */
    .ep180-description__img-group {
        width: 100%;
        margin-bottom: 72px;
    }

    .slick-slide {
        height: unset !important;
    }

    .ep180-description__img-item {
        position: relative;
        margin: 0 12px;
        max-width: 1050px;
    }

    .ep180-description__img-item-background {
        width: 100%;
    }

    .ep180-description__img-item-background img {
        width: 100%;
        height: auto;
    }

    .ep180-description__img-item-text {
        position: absolute;
        left: 6.1%;
        bottom: 16%;
        max-width: 390px;
    }

    .ep180-description__img-item-text__title {
        font-size: var(--medium-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .ep180-description__img-item-text__content {
        font-size: var(--body-size);
        color: var(--body-color);
        margin-top: 16px;
        line-height: 1.5;
    }

    .ep180-description__img-group-arrow {
        display: flex;
        justify-content: end;
        align-items: center;
        gap: 16px;
        width: 88.44%;
        margin-top: 24px;
    }

    .ep180-description__img-group-arrow svg {
        width: 64px;
        height: 64px;
        cursor: pointer;
        transition: opacity 0.3s ease;
    }

    .ep180-description__img-group-arrow svg:hover {
        opacity: 0.7;
    }

    /* lock */
    .ep180-description-lock {
        width: 100%;
        position: relative;
    }

    .ep180-description-lock__background {
        width: 100%;
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .ep180-description-lock__background img {
        max-width: 1920px;
        height: auto;
    }

    .ep180-description-lock__text {
        position: absolute;
        right: 7.71%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 518px;
    }

    .ep180-description-lock__text__title {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .ep180-description-lock__text__content {
        font-size: var(--body-size);
        color: var(--body-color);
        line-height: 1.5;
        margin-top: 16px;
    }

    .ep180-description__text-tip {
        margin-top: 8px;
        display: block;
    }

    /* armrest */
    .ep180-description-armrest {
        width: 100%;
        position: relative;
    }

    .ep180-description-armrest__background {
        width: 100%;
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .ep180-description-armrest__background img {
        max-width: 1920px;
        height: auto;
    }

    .ep180-description-armrest__text {
        position: absolute;
        left: 9.9%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 593px;
    }

    .ep180-description-armrest__text__title {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .ep180-description-armrest__text__content {
        font-size: var(--body-size);
        color: var(--body-color);
        line-height: 1.5;
        margin-top: 16px;
    }

    /* support */
    .ep180-description-support {
        width: 80.21%;
        margin: 96px auto;
    }

    .ep180-description-support__title {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
        max-width: 541px;
    }

    .ep180-description-support__img-group {
        display: flex;
        flex-direction: row;
        gap: 24px;
        margin-top: 40px;
    }

    .ep180-description-support__img-item {
        width: 50%;
    }

    .ep180-description-support__img-item img {
        width: 100%;
        height: auto;
    }

    .ep180-description-support__text__content {
        --body-size: 20px;
        font-size: var(--body-size);
        color: var(--body-color);
        line-height: 1.5;
        margin: 16px 9px 0;
    }

    /* caster */
    .ep180-description-caster {
        width: 100%;
        position: relative;
    }

    .ep180-description-caster__background {
        width: 100%;
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .ep180-description-caster__background img {
        max-width: 1920px;
        height: auto;
    }

    .ep180-description-caster__text {
        position: absolute;
        left: 50%;
        bottom: 7.18%;
        transform: translateX(-50%);
        max-width: 1117px;
        width: 100%;
        color: #ffffff;
        text-align: center;
    }

    .ep180-description-caster__text__title {
        font-size: var(--large-heading-size);
        font-weight: 700;
    }

    .ep180-description-caster__text__content {
        font-size: var(--body-size);
        line-height: 1.5;
        margin-top: 16px;
    }

    @media (max-width: 1440px) {
        .ep180-product-description {
            --large-heading-size: 42px;
            --medium-heading-size: 28px;
            --body-size: 24px;
        }

        .ep180-description-ready__background img,
        .ep180-description-last__background img,
        .ep180-description-armrest__background img,
        .ep180-description-lock__background img,
        .ep180-description-caster__background img {
            max-width: 1440px;
        }

        .ep180-description-ready__text {
            left: 7.64%;
            max-width: 464px;
        }
        .ep180-description-last__text {
            max-width: 496px;
        }

        .ep180-description-function__text {
            align-items: unset;
        }

        .ep180-description-function__text__title,
        .ep180-description-function__text__content,
        .ep180-description-function__text__img {
            width: unset;
        }

        .ep180-description-function__text__title {
            margin-left: 14.2%;
        }
        .ep180-description-function__text__content {
            margin: 1.98% 8.02% 0 14.2%;
        }
        .ep180-description-function__text__img {
            margin: 3.95% 21.6% 0 14.2%;
        }

        .ep180-description-customisable__text {
            margin: 96px auto 32px;
            gap: 11.01%;
        }

        .ep180-description__img-item {
            max-width: 840px;
        }

        .ep180-description__img-item-text {
            max-width: 327px;
        }

        .ep180-description__img-group-arrow svg {
            width: 56px;
            height: 56px;
        }

        .ep180-description__img-group {
            margin-bottom: 52px;
        }

        .ep180-description__img-item-text__content {
            --body-size: 20px;
        }

        .ep180-description-lock__text {
            right: 3.33%;
            max-width: 494px;
        }

        .ep180-description-armrest__text {
            left: 11.11%;
            max-width: 496px;
        }

        .ep180-description-support {
            margin: 80px auto;
        }

        .ep180-description-support__img-group {
            margin-top: 32px;
        }

        .ep180-description-support__text__content {
            --body-size: 20px;
        }

        .ep180-description-caster__text {
            max-width: 865px;
        }
    }

    @media (max-width: 1200px) {
        .ep180-product-description {
            --large-heading-size: 36px;
            --medium-heading-size: 24px;
            --body-size: 20px;
        }

        .ep180-description-ready__background img,
        .ep180-description-last__background img,
        .ep180-description-armrest__background img,
        .ep180-description-lock__background img,
        .ep180-description-caster__background img {
            max-width: 1200px;
        }

        .ep180-description-ready__text {
            left: 6.67%;
            max-width: 380px;
        }

        .ep180-description-last__text {
            right: 6.67%;
            max-width: 384px;
        }

        .ep180-description-function__text__title {
            margin-left: 11.85%;
        }
        .ep180-description-function__text__content {
            margin: 2.34% 4.11% 0 11.85%;
        }
        .ep180-description-function__text__img {
            margin: 3.56% 28.89% 0 11.85%;
        }

        .ep180-description-customisable__text {
            margin: 72px auto 24px;
            width: 82.17%;
            gap: 9.63%;
        }

        .ep180-description__img-item {
            max-width: 720px;
            margin: 0 8px;
        }

        .ep180-description__img-item-text {
            left: 4.44%;
            bottom: 13.63%;
            max-width: 291px;
        }

        .ep180-description__img-group-arrow svg {
            width: 48px;
            height: 48px;
        }

        .ep180-description__img-item-text__content {
            --body-size: 18px;
        }

        .ep180-description-lock__text {
            right: 5.17%;
            max-width: 348px;
        }

        .ep180-description-armrest__text {
            left: 11.25%;
            max-width: 421px;
        }

        .ep180-description-support {
            margin: 64px auto;
            width: 78%;
        }

        .ep180-description-support__img-group {
            margin-top: 24px;
            gap: 16px;
        }

        .ep180-description-support__text__content {
            --body-size: 16px;
        }

        .ep180-description-caster__text__content {
            --body-size: 18px;
        }
    }

    @media (max-width: 900px) {
        .ep180-product-description {
            --large-heading-size: 36px;
            --medium-heading-size: 36px;
            --body-size: 24px;
        }

        .ep180-description-ready__background img,
        .ep180-description-last__background img {
            width: 100%;
        }

        .ep180-description-lock__background img,
        .ep180-description-armrest__background img,
        .ep180-description-caster__background img {
            max-width: 900px;
        }

        .ep180-description-ready,
        .ep180-description-last {
            display: flex;
            flex-direction: column-reverse;
        }

        .ep180-description-ready__text,
        .ep180-description-last__text {
            position: unset;
            transform: unset;
            max-width: unset;
            padding: 72px 56px 32px 56px;
            background-color: #f5f5f5;
        }

        .ep180-description-ready__background img,
        .ep180-description-last__background img {
            width: 100%;
        }

        .ep180-description-function {
            display: flex;
            flex-direction: column-reverse;
        }

        .ep180-description-function__img {
            width: 100%;
        }

        .ep180-description-function__text {
            margin: 54px 56px 56px 56px;
        }

        .ep180-description-function__text__title,
        .ep180-description-function__text__content,
        .ep180-description-function__text__img {
            width: 100%;
        }

        .ep180-description-function__text__title {
            margin: 0;
        }

        .ep180-description-function__text__content {
            margin: 16px 0 0 0;
        }

        .ep180-description-function__text__img {
            margin: 32px 0 0 0;
        }

        .ep180-description-function__text__img img {
            max-width: 788px;
        }

        .ep180-description-function__img img {
            max-width: 900px;
        }

        .ep180-description-customisable__text {
            flex-direction: column;
            margin: 56px 6.22% 32px;
            gap: 16px;
            width: unset;
        }

        .ep180-description-customisable__text__title {
            max-width: 358px;
        }

        .ep180-description__img-slider-container {
            margin: 32px 0;
        }

        .ep180-description__img-group {
            margin: 0;
        }

        .ep180-description__img-item {
            margin: 24px 3.33% 0;
            max-width: unset;
        }

        .ep180-description__img-item-text {
            left: 6.67%;
            bottom: 12%;
            max-width: 260px;
        }

        .ep180-description__img-item-text__title {
            font-size: 36px;
        }

        .ep180-description__img-item-text__content {
            font-size: 24px;
            margin-top: 16px;
        }

        .ep180-description__img-group-arrow {
            display: none;
        }

        .ep180-description-lock__text {
            top: 6.49%;
            left: 6.22%;
            right: 6.22%;
            transform: unset;
            max-width: unset;
        }

        .ep180-description-lock__text__content {
            margin-top: 16px;
        }

        .ep180-description__text-tip {
            margin-top: 16px;
        }

        .ep180-description-armrest__text {
            left: 8.33%;
            max-width: 387px;
        }

        .ep180-description-armrest__text__content {
            margin-top: 16px;
        }

        .ep180-description-support {
            width: 87.56%;
        }

        .ep180-description-support__img-group {
            margin-top: 32px;
        }

        .ep180-description-support__text__content {
            margin: 16px 3px 0;
        }

        .ep180-description-caster__text {
            width: 83%;
        }
    }

    @media (max-width: 600px) {
        .ep180-product-description {
            --large-heading-size: 7vw;
            --medium-heading-size: 4.67vw;
            --body-size: 4.67vw;
        }

        .ep180-description-ready__background img,
        .ep180-description-last__background img,
        .ep180-description-armrest__background img,
        .ep180-description-lock__background img,
        .ep180-description-caster__background img {
            width: 100%;
        }

        .ep180-description-ready,
        .ep180-description-last {
            display: flex;
            flex-direction: column-reverse;
        }

        .ep180-description-ready__text,
        .ep180-description-last__text {
            position: unset;
            transform: unset;
            max-width: unset;
            padding: 8vw 5.33vw 9vw 5.33vw;
        }

        .ep180-description-ready__text__content,
        .ep180-description-last__text__content {
            margin-top: 2.67vw;
        }

        .ep180-description-ready__background img,
        .ep180-description-last__background img {
            width: 100%;
        }

        .ep180-description-function {
            display: flex;
            flex-direction: column-reverse;
        }

        .ep180-description-function__img {
            width: 100%;
        }

        .ep180-description-function__text {
            margin: 8vw auto 6.67vw;
        }

        .ep180-description-function__text__title,
        .ep180-description-function__text__content,
        .ep180-description-function__text__img {
            width: 89.33vw;
        }

        .ep180-description-function__text__content {
            margin: 2.67vw 0 0 0;
        }

        .ep180-description-function__text__img {
            margin: 4vw 0 0 0;
        }

        .ep180-description-customisable__text {
            width: 89.33vw;
            flex-direction: column;
            margin: 8vw auto;
            gap: 2.67vw;
        }

        .ep180-description-customisable__text__title {
            max-width: 68.33vw;
        }

        .ep180-description__img-slider-container {
            margin: 0;
        }

        .ep180-description__img-group {
            margin: 0;
        }

        .ep180-description__img-item {
            margin: 0;
        }

        .ep180-description__img-item-text {
            top: 8vw;
            left: 50%;
            transform: translateX(-50%);
            width: 82.33vw;
            max-width: unset;
            text-align: center;
        }

        .ep180-description__img-item-text__title {
            font-size: 6vw;
        }

        .ep180-description__img-item-text__content {
            font-size: 4vw;
            margin-top: 2.67vw;
        }

        .ep180-description__img-group-arrow {
            display: none;
        }

        .ep180-description-lock__text {
            top: 8vw;
            right: 50%;
            left: unset;
            transform: translateX(50%);
            max-width: unset;
            width: 89.33vw;
        }

        .ep180-description-lock__text__content {
            margin-top: 2.67vw;
        }

        .ep180-description__text-tip {
            margin-top: 2.67vw;
        }

        .ep180-description-armrest__text {
            top: 8vw;
            left: 50%;
            transform: translateX(-50%);
            max-width: unset;
            width: 89.33vw;
        }

        .ep180-description-armrest__text__content {
            margin-top: 2.67vw;
        }

        .ep180-description-support {
            width: 89.33vw;
            margin: 8vw auto;
        }

        .ep180-description-support__title {
            max-width: unset;
        }

        .ep180-description-support__img-group {
            flex-direction: column;
            gap: 5.33vw;
            margin-top: 5.33vw;
        }

        .ep180-description-support__img-item {
            width: 100%;
        }

        .ep180-description-support__text__content {
            margin: 2.67vw 0 0;
            --body-size: 4.67vw;
        }

        .ep180-description-caster__text {
            bottom: 6.67vw;
            left: 50%;
            transform: translateX(-50%);
            width: 89.33vw;
            text-align: left;
        }

        .ep180-description-caster__text__content {
            margin-top: 2.67vw;
            --body-size: 4.67vw;
        }
    }
</style>

<div class="ep180-product-description">
    <div class="ep180-description-poster">
        <picture>
            <source
                media="(max-width:600px)"
                srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_1-sp_600x.jpg?v=1747979515" />
            <img
                alt=""
                src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_1_1920x.jpg?v=1747979516"
        /></picture>
    </div>

    <div class="ep180-description-ready">
        <div class="ep180-description-ready__background">
            <picture>
                <source
                    media="(max-width: 600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_2-sp_600x.jpg?v=1747979515" />
                <source
                    media="(max-width: 900px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_2-900px_900x.jpg?v=1748400435" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_2_1920x.jpg?v=1747979515" />
            </picture>
        </div>
        <div class="ep180-description-ready__text">
            <div class="ep180-description-ready__text__title">5 Minutues &amp; 6 Screws - Get your Chair Ready</div>
            <div class="ep180-description-ready__text__content">
                Only 6 screws required to assemble. You’ll have your chair ready in just 5 minutes – no special skills needed. Whether you’re worried about
                complicated assembly or placing a bulk order and concerned about assembling multiple chairs, Boulies EP180 makes it effortless.
            </div>
        </div>
    </div>

    <div class="ep180-description-last">
        <div class="ep180-description-last__background">
            <picture>
                <source
                    media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_3-sp_600x.jpg?v=1747979515" />
                <source
                    media="(max-width: 900px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_3-900px_900x.jpg?v=1748400435" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_3_1920x.jpg?v=1747979515"
            /></picture>
        </div>
        <div class="ep180-description-last__text">
            <div class="ep180-description-last__text__title">
                Strong,<br />
                Solid,<br />
                And Built To Last
            </div>
            <div class="ep180-description-last__text__content">
                Equipped with Boulies new heavy-duty tilt mechanism, the EP180 chair can support up to 160kg in weight. Designed form strength and durability,
                this solid tilt mechanism ensures long-term performance and reliable support for everyday use.
            </div>
        </div>
    </div>

    <div class="ep180-description-function">
        <div class="ep180-description-function__img">
            <picture>
                <source
                    media="(max-width:900px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_5-900px_900x.jpg?v=1748400436" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_5_900x.jpg?v=1747979515" />
            </picture>
        </div>
        <div class="ep180-description-function__text">
            <div class="ep180-description-function__text__title">Seat Tilt Function</div>
            <div class="ep180-description-function__text__content">
                For added flexibility, Boulies EP180 features a seat tilt function which allows you to lean forwards for focused work or recline slightly
                backwards to relax. Whether you’re sitting for short tasks or long hours, the EP180 offers the support you need in every position—helping reduce
                strain and improve overall comfort.
            </div>
            <div class="ep180-description-function__text__img">
                <img
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_4_900x.jpg?v=1747979515"
                    alt="" />
            </div>
        </div>
    </div>

    <div class="ep180-description-customisable">
        <div class="ep180-description-customisable__text">
            <div class="ep180-description-customisable__text__title">Customisable Comfort for Individuals</div>
            <div class="ep180-description-customisable__text__content">
                Boulies EP180 is designed to adapt to people in different heights. Whether you’re sharing the chai with a partner or simply want a more
                personalised fit or want to accommodates users of varying heights, you can adjust <span class="text-strong">the backrest height</span>,
                <span class="text-strong">the seat depth</span> and <span class="text-strong">the seat height</span> yo suit bodies perfectly.
            </div>
        </div>
    </div>

    <div class="ep180-description__img-group">
        <div class="ep180-description__img-slider-container">
            <div class="ep180-description__img-item">
                <div class="ep180-description__img-item-background">
                    <picture>
                        <source
                            media="(max-width:600px)"
                            srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_6-sp_600x.jpg?v=1747979515" />
                        <img
                            alt=""
                            src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_6_1050x.jpg?v=1747979515"
                    /></picture>
                </div>
                <div class="ep180-description__img-item-text">
                    <div class="ep180-description__img-item-text__title">Seat depth adjustment</div>
                    <div class="ep180-description__img-item-text__content">Seat depth can adjust within 45.5 - 50cm to support your thig.</div>
                </div>
            </div>

            <div class="ep180-description__img-item">
                <div class="ep180-description__img-item-background">
                    <picture>
                        <source
                            media="(max-width:600px)"
                            srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_7-sp_600x.jpg?v=1747979515" />
                        <img
                            alt=""
                            src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_7_1050x.jpg?v=1747979515"
                    /></picture>
                </div>
                <div class="ep180-description__img-item-text">
                    <div class="ep180-description__img-item-text__title">Seat height adjustment</div>
                    <div class="ep180-description__img-item-text__content">
                        A seat height range from 44 to 51 cm to maintain your height.
                        <br />* circular from floor to the front of the seat.
                    </div>
                </div>
            </div>

            <div class="ep180-description__img-item">
                <div class="ep180-description__img-item-background">
                    <picture>
                        <source
                            media="(max-width:600px)"
                            srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_8-sp_600x.jpg?v=1747979515" />
                        <img
                            alt=""
                            src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_8_1050x.jpg?v=1747979515"
                    /></picture>
                </div>
                <div class="ep180-description__img-item-text">
                    <div class="ep180-description__img-item-text__title">Backrest height adjustment</div>
                    <div class="ep180-description__img-item-text__content">Adjust within 56 - 63 cm to fit your back.</div>
                </div>
            </div>
        </div>

        <div class="ep180-description__img-group-arrow">
            <div id="ep180-description__img-group-arrow-left">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    fill="none"
                    version="1.1"
                    width="64"
                    height="64"
                    viewbox="0 0 64 64">
                    <g transform="matrix(-1,0,0,-1,128,128)">
                        <g>
                            <g>
                                <g>
                                    <path
                                        d="M95.99995195312499,69.33349609375C81.306551953125,69.33349609375,69.333251953125,81.30679609375,69.333251953125,96.00019609374999C69.333251953125,110.69349609375,81.306551953125,122.66679609375001,95.99995195312499,122.66679609375001C110.693251953125,122.66679609375001,122.66655195312501,110.69349609375,122.66655195312501,96.00019609374999C122.66655195312501,81.30679609375,110.693251953125,69.33349609375,95.99995195312499,69.33349609375C95.99995195312499,69.33349609375,95.99995195312499,69.33349609375,95.99995195312499,69.33349609375ZM103.43995195312499,97.41349609375C103.43995195312499,97.41349609375,94.026551953125,106.82679609375,94.026551953125,106.82679609375C93.626551953125,107.22679609375001,93.119951953125,107.41349609375,92.613251953125,107.41349609375C92.106551953125,107.41349609375,91.599951953125,107.22679609375001,91.19995195312501,106.82679609375C90.426551953125,106.05349609375,90.426551953125,104.77349609375,91.19995195312501,104.00019609374999C91.19995195312501,104.00019609374999,99.19995195312501,96.00019609374999,99.19995195312501,96.00019609374999C99.19995195312501,96.00019609374999,91.19995195312501,88.00019609374999,91.19995195312501,88.00019609374999C90.426551953125,87.22679609375,90.426551953125,85.94679609375,91.19995195312501,85.17349609375C91.973251953125,84.40019609375,93.253251953125,84.40019609375,94.026551953125,85.17349609375C94.026551953125,85.17349609375,103.43995195312499,94.58679609375,103.43995195312499,94.58679609375C104.239951953125,95.36019609375,104.239951953125,96.64019609375,103.43995195312499,97.41349609375C103.43995195312499,97.41349609375,103.43995195312499,97.41349609375,103.43995195312499,97.41349609375Z"
                                        fill="#141414"
                                        fill-opacity="1"></path>
                                </g>
                                <g
                                    transform="matrix(-1,0,0,-1,256,256)"
                                    style="opacity: 0"></g>
                            </g>
                        </g>
                    </g>
                </svg>
            </div>
            <div id="ep180-description__img-group-arrow-right">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    fill="none"
                    version="1.1"
                    width="64"
                    height="64"
                    viewbox="0 0 64 64">
                    <g>
                        <g>
                            <g>
                                <g>
                                    <path
                                        d="M31.999951953125,5.33349609375C17.306551953125002,5.33349609375,5.333251953125,17.306796093750002,5.333251953125,32.00019609375C5.333251953125,46.69349609375,17.306551953125002,58.66679609375,31.999951953125,58.66679609375C46.693251953125,58.66679609375,58.666551953125,46.69349609375,58.666551953125,32.00019609375C58.666551953125,17.306796093750002,46.693251953125,5.33349609375,31.999951953125,5.33349609375C31.999951953125,5.33349609375,31.999951953125,5.33349609375,31.999951953125,5.33349609375ZM39.439951953125,33.41349609375C39.439951953125,33.41349609375,30.026551953125,42.82679609375,30.026551953125,42.82679609375C29.626551953125,43.22679609375,29.119951953125,43.41349609375,28.613251953125,43.41349609375C28.106551953125,43.41349609375,27.599951953125,43.22679609375,27.199951953125,42.82679609375C26.426551953125,42.05349609375,26.426551953125,40.77349609375,27.199951953125,40.00019609375C27.199951953125,40.00019609375,35.199951953125,32.00019609375,35.199951953125,32.00019609375C35.199951953125,32.00019609375,27.199951953125,24.00019609375,27.199951953125,24.00019609375C26.426551953125,23.22679609375,26.426551953125,21.94679609375,27.199951953125,21.17349609375C27.973251953125,20.40019609375,29.253251953125,20.40019609375,30.026551953125,21.17349609375C30.026551953125,21.17349609375,39.439951953125,30.58679609375,39.439951953125,30.58679609375C40.239951953125,31.36019609375,40.239951953125,32.64019609375,39.439951953125,33.41349609375C39.439951953125,33.41349609375,39.439951953125,33.41349609375,39.439951953125,33.41349609375Z"
                                        fill="#141414"
                                        fill-opacity="1"></path>
                                </g>
                                <g
                                    transform="matrix(-1,0,0,-1,128,128)"
                                    style="opacity: 0"></g>
                            </g>
                        </g>
                    </g>
                </svg>
            </div>
        </div>
    </div>

    <div class="ep180-description-lock">
        <div class="ep180-description-lock__background">
            <picture>
                <source
                    media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_9-sp_600x.jpg?v=1747979515" />
                <source
                    media="(max-width:900px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_9-900px_900x.jpg?v=1748400436" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_9_1920x.jpg?v=1747979515"
            /></picture>
        </div>
        <div class="ep180-description-lock__text">
            <div class="ep180-description-lock__text__title">Lock at Any Angle You Need</div>
            <div class="ep180-description-lock__text__content">
                The Boulies EP180 lets you recline up to 103° and lock the backrest at any angle from 79° to 113°, giving you full control over your comfort.
                Whether you’re leaning back to relax or finding the perfect upright position to support your back, the chair adapts to you. Ideal for both work
                and rest, it offers tailored support whenever you need it.
                <br />
                <span class="ep180-description__text-tip">* The degrees refer solely to the backrest recline and do not include the seat tilt angle.</span>
            </div>
        </div>
    </div>

    <div class="ep180-description-armrest">
        <div class="ep180-description-armrest__background">
            <picture>
                <source
                    media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_10-sp_600x.jpg?v=1747979515" />
                <source
                    media="(max-width:900px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_10-900px_900x.jpg?v=1748400435" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_10_1920x.jpg?v=1747979515"
            /></picture>
        </div>
        <div class="ep180-description-armrest__text">
            <div class="ep180-description-armrest__text__title">Detachable 3D Armrests</div>
            <div class="ep180-description-armrest__text__content">
                This armrest can adjust up, down and angles to your exact requirements. The arm top pad supports your elbows in softness, providing sublime
                support as you sit. And it's detachable, offering you the freedom of choice in your seating experience.
            </div>
        </div>
    </div>

    <div class="ep180-description-support">
        <div class="ep180-description-support__title">Cushioned Comfort Meets <br />Breathable Support</div>

        <div class="ep180-description-support__img-group">
            <div class="ep180-description-support__img-item">
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_11_800x.jpg?v=1747979515" />
                <div class="ep180-description-support__text__content">
                    Designed for those who spend long hours at their desk, the Boulies EP180 features a plush cushioned seat that offers the perfect balance of
                    softness and support. Ideal for individuals working 8+ hours a day, the seat cushion helps alleviate pressure on the tailbone, delivering
                    lasting comfort throughout the day.
                </div>
            </div>

            <div class="ep180-description-support__img-item">
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_12_800x.jpg?v=1747979515" />
                <div class="ep180-description-support__text__content">
                    The chair also comes with a high-tension mesh backrest that promotes airflow to keep you cool, while providing firm and reliable support for
                    your back. Whether you're tackling deadlines or attending virtual meetings, the EP180 ensures you stay comfortable and well-supported from
                    start to finish.
                </div>
            </div>
        </div>
    </div>

    <div class="ep180-description-caster">
        <div class="ep180-description-caster__background">
            <picture>
                <source
                    media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_13-sp_600x.jpg?v=1747979515" />
                <source
                    media="(max-width:900px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_13-900px_900x.jpg?v=1748400436" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180-detail_13_1920x.jpg?v=1747979515"
            /></picture>
        </div>
        <div class="ep180-description-caster__text">
            <div class="ep180-description-caster__text__title">Smooth and Floor-Friendly PU Casters</div>
            <div class="ep180-description-caster__text__content">
                Equipped with high-quality PU casters, the Boulies EP180 glides effortlessly across most floor types without leaving scratches or marks. These
                durable wheels offer quiet, smooth movement—perfect for both home and office use.
            </div>
        </div>
    </div>

    <!-- specification start -->
    <div
        class="bt20-cd_spec"
        id="bt20-collectionSpecifications">
        <div
            class="bprtdd_item bprtdd_detail"
            id="singleProductDetail">
            <h2 class="bprtdd_detailTitle">Product Warranty</h2>
            <div class="bprtdd_detailWrapper clearfix">
                <div class="bprtdd_warrantySection">
                    <span
                        class="bps_warrantyTime"
                        data-warrantytime="24">
                        2 years limited Warranty <br />
                        <a href="/pages/warranty">Learn More</a>
                    </span>
                </div>
                <div class="bprtdd_warrantySection">
                    <span
                        class="bps_warrantyTime"
                        data-warrantytime="24">
                        14 days return <br />
                        <a href="/pages/return-and-refund">Learn More</a>
                    </span>
                </div>
            </div>
        </div>
        <div
            class="bprtdd_item bprtdd_detail"
            id="singleProductDetail">
            <h2 class="bprtdd_detailTitle">Product Details</h2>
            <div>
                <div class="bprtdd_detail_line">
                    <div class="bprtdd_detail_line_head">
                        <h3>DIMENSIONS</h3>
                    </div>
                    <div class="bprtdd_detail_line_body">
                        <div class="bt51-dimensions-list">
                            <a
                                href="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180_dimensions.jpg?v=1748401305"
                                class="bt51-dimension dynamic-gallery-item">
                                <img
                                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP180_dimensions_300x.jpg?v=1748401305"
                                    alt=""
                                    class="bt51-dimension-image-preview" />
                                <p class="bt51-dimension-decription">Technical Specification - ep180</p>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="bprtdd_detail_line">
                    <div class="bprtdd_detail_line_head">
                        <h3>FUNCTIONS</h3>
                    </div>
                    <div class="bprtdd_detail_line_body">
                        <div class="bprtdd_detail_section">360° swivel</div>
                        <div class="bprtdd_detail_section">Backrest recline (79°-113°)</div>
                        <div class="bprtdd_detail_section">Adjustable backrest height</div>
                        <div class="bprtdd_detail_section">Seat depth adjustment</div>
                        <div class="bprtdd_detail_section">Seat tilt forwards and position lock (2°)</div>
                        <div class="bprtdd_detail_section">Seat tilt tilt and position lock (12°)</div>
                        <div class="bprtdd_detail_section">Armrest 6 ways adjustable</div>
                    </div>
                </div>
                <div class="bprtdd_detail_line">
                    <div class="bprtdd_detail_line_head">
                        <h3>SPECIFICATION</h3>
                    </div>
                    <div class="bprtdd_detail_line_body">
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Upholstery</span>
                            <span class="bprtdd_detail_itemDescribe">Airy Chenille Mesh fabric</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Frame</span> <span class="bprtdd_detail_itemDescribe">Reinforced plastic frame</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Five star foot base</span>
                            <span class="bprtdd_detail_itemDescribe">Heavy Duty Engineering plastic Base</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Tilt mechanism</span> <span class="bprtdd_detail_itemDescribe">Multi tilt</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Casters</span> <span class="bprtdd_detail_itemDescribe">6cm PU casters</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Hydraulics</span> <span class="bprtdd_detail_itemDescribe">Class 3 (Heavy-duty)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- specification end -->
</div>

<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.4/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

<script>
    $(".ep180-description__img-slider-container").slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        centerMode: true,
        infinite: true,
        dots: false,
        variableWidth: true,
        arrows: true,
        prevArrow: $("#ep180-description__img-group-arrow-left"),
        nextArrow: $("#ep180-description__img-group-arrow-right"),
        responsive: [
            {
                breakpoint: 901,
                settings: "unslick",
            },
        ],
    });
</script>
