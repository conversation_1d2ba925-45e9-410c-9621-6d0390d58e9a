<style>
    .op300-product-description {
        --large-heading-size: 48px;
        --medium-heading-size: 36px;
        --body-size: 24px;
        --heading-color: #141414;
        --body-color: #595959;

        width: 100%;
        max-width: 1920px;
        margin: 0 auto;
    }

    .op300-product-description img {
        display: block;
    }

    /* intro */
    .op300-intro {
        padding: 6.67% 14.58% 5% 13.54%;
        background-color: #f4f4f4;
    }

    .op300-intro__text {
        display: flex;
        justify-content: space-between;
        gap: 10.71%;
    }

    .op300-intro__text__title {
        flex: 114;
        font-size: var(--medium-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .op300-intro__text__content {
        flex: 205;
        font-size: var(--body-size);
        color: var(--body-color);
        line-height: 1.5;
    }

    .op300-poster img {
        width: 100%;
        height: auto;
    }

    /* detail */
    .op300-detail {
        width: 100%;
        background-color: #141414;
    }

    .op300-detail__title {
        font-size: var(--large-heading-size);
        color: #ffffff;
        font-weight: 700;
        text-align: center;
        padding: 96px 0 24px 0;
    }

    .op300-detail__content {
        display: grid;
        grid-template-columns: 35fr 66fr 35fr;
        grid-template-rows: auto;
        gap: 16px;
        align-items: center;
        padding: 0 20.83% 180px 20.83%;
    }

    /* block */
    .op300-detail__block {
        width: 100%;
        max-width: 280px;
        aspect-ratio: 1 / 1;
        border-radius: 32px;
        background-color: #f8891d;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: auto;
    }

    .op300-detail__block svg {
        width: 64.29%;
        height: auto;
    }

    .op300-detail__img-2,
    .op300-detail__img-3,
    .op300-detail__img-4,
    .op300-detail__img-5,
    .op300-detail__img-6,
    .op300-detail__img-7 {
        position: relative;
    }

    .op300-detail__img-2::before,
    .op300-detail__img-3::before,
    .op300-detail__img-4::before,
    .op300-detail__img-5::before,
    .op300-detail__img-6::before,
    .op300-detail__img-7::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: white;
        margin-top: auto;
        border-radius: 32px;
    }

    .op300-detail__img-2 img,
    .op300-detail__img-3 img,
    .op300-detail__img-4 img,
    .op300-detail__img-5 img,
    .op300-detail__img-6 img,
    .op300-detail__img-7 img {
        position: relative;
    }

    .op300-detail__content img {
        width: 100%;
        height: auto;
    }

    .op300-detail__img-column {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-top: auto;
    }

    /* img-3 */
    .op300-detail__img-3::before {
        width: 100%;
        height: 77.5%;
    }

    .op300-detail__img-3::before,
    .op300-detail__img-3 img {
        max-width: 528px;
    }

    /* img-4 */
    .op300-detail__img-4::before {
        width: 100%;
        height: 100%;
    }

    .op300-detail__img-4::before {
        max-width: 360px;
    }

    .op300-detail__img-4 img {
        max-width: 360px;
        width: 128.57%;
    }

    /* img-2 */
    .op300-detail__img-2::before {
        width: 100%;
        height: 100%;
    }

    /* img-5 */
    .op300-detail__img-5::before {
        width: 100%;
        height: 100%;
    }

    .op300-detail__img-2::before,
    .op300-detail__img-5::before,
    .op300-detail__img-2 img,
    .op300-detail__img-5 img {
        max-width: 280px;
    }

    /* img-group */
    .op300-detail__img-group {
        grid-column: span 3;
        display: flex;
        gap: 16px;
    }

    /* img-6 */
    .op300-detail__img-6::before {
        width: 100%;
        height: 100%;
    }

    .op300-detail__img-6::before,
    .op300-detail__img-6 img {
        max-width: 392px;
    }

    /* img-7 */
    .op300-detail__img-7::before {
        width: 100%;
        height: 100%;
    }

    .op300-detail__img-7::before,
    .op300-detail__img-7 img {
        max-width: 712px;
    }

    /* mechanism */
    .op300-mechanism {
        width: 100%;
        position: relative;
    }

    .op300-mechanism__background {
        display: flex;
        justify-content: end;
        overflow: hidden;
    }

    .op300-mechanism__background img {
        max-width: 1920px;
        height: auto;
    }

    .op300-mechanism__text {
        position: absolute;
        left: 9.22%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 647px;
    }

    .op300-mechanism__text__big {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .op300-mechanism__text__small {
        font-size: var(--body-size);
        color: var(--body-color);
        margin-top: 16px;
        line-height: 1.5;
    }

    /* backrest */
    .op300-backrest {
        width: 100%;
        position: relative;
    }

    .op300-backrest__background {
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .op300-backrest__background img {
        max-width: 1920px;
        height: auto;
    }

    .op300-backrest__text {
        position: absolute;
        right: 8.91%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 667px;
    }

    .op300-backrest__text__big {
        font-size: var(--medium-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .op300-backrest__text__small {
        font-size: var(--body-size);
        color: var(--body-color);
        margin-top: 16px;
        line-height: 1.5;
    }

    .op300-backrest__text__small.tip {
        font-size: 22px;
    }

    /* seat */
    .op300-seat {
        width: 100%;
        position: relative;
    }

    .op300-seat__background {
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .op300-seat__background img {
        max-width: 1920px;
        height: auto;
    }

    .op300-seat__text {
        position: absolute;
        left: 11.46%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 663px;
    }

    .op300-seat__text__big {
        font-size: var(--medium-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .op300-seat__text__small {
        font-size: var(--body-size);
        color: var(--body-color);
        margin-top: 16px;
        line-height: 1.5;
    }

    /* function */
    .op300-function {
        width: 100%;
        position: relative;
    }

    .op300-function__background {
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .op300-function__background img {
        max-width: 1920px;
        height: auto;
    }

    .op300-function__text {
        position: absolute;
        right: 7.71%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 690px;
    }

    .op300-function__text__big {
        font-size: var(--medium-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .op300-function__text__small {
        font-size: var(--body-size);
        color: var(--body-color);
        margin-top: 16px;
        line-height: 1.5;
    }

    /* function-detail */
    .op300-function-detail {
        background-color: #000000;
        padding: 6.67% 7.03%;
        position: relative;
    }

    .op300-function-detail__wrapper {
        width: 100%;
        position: relative;
        background-color: #ededed;
        overflow: hidden;
    }

    .op300-function-detail__background {
        display: flex;
        justify-content: center;
        overflow: hidden;
        opacity: 0;
        height: 0;
        transition: opacity 0.5s ease-in-out;
    }

    .op300-function-detail__background.show {
        opacity: 1;
        height: auto;
    }

    .op300-function-detail__background img {
        max-width: 1650px;
        height: auto;
    }

    .op300-function-detail__text-list {
        position: absolute;
        left: 6.18%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 663px;
        display: flex;
        flex-direction: column;
    }

    .op300-function-detail__text-item {
        border-top: 1px solid #bfbfbf;
        border-bottom: 1px solid #bfbfbf;
        padding: 24px 0 24px 3px;
        transition: all 0.5s ease-in-out;
        pointer-events: auto;
    }

    .op300-function-detail__text-item.selected {
        border-top: 2px solid #141414;
        pointer-events: none;
    }

    .op300-function-detail__text__big {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: var(--medium-heading-size);
        color: var(--heading-color);
        font-weight: 700;
        cursor: pointer;
    }

    #op300-function-detail-btn {
        transition: 0.5s ease-in-out;
        flex-shrink: 0;
    }

    #op300-function-detail-btn.rotate {
        transform: rotateX(180deg);
    }

    .op300-function-detail__text__small {
        font-size: var(--body-size);
        color: var(--body-color);
        line-height: 1.5;
        margin: 0;
        max-height: 0;
        transition: all 0.5s ease-in-out;
        overflow: hidden;
    }

    .op300-function-detail__text__small.show {
        max-height: 900px;
        margin: 16px 49px 120px 0;
    }

    .op300-function-detail__item-img {
        display: none;
    }

    /* armrest */
    .op300-armrest {
        width: 100%;
        position: relative;
    }

    .op300-armrest__background {
        display: flex;
        justify-content: center;
        overflow: hidden;
    }

    .op300-armrest__background img {
        max-width: 1920px;
        height: auto;
    }

    .op300-armrest__title {
        position: absolute;
        right: 12.24%;
        top: 50%;
        transform: translateY(-50%);
        max-width: 705px;
    }

    .op300-armrest__title__text__big {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .op300-armrest__title__text__small {
        font-size: var(--body-size);
        color: var(--body-color);
        margin-top: 16px;
        line-height: 1.5;
    }

    .op300-armrest__img-group {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin: 24px 12.34% 0 -2.7%;
    }

    .op300-armrest__img-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .op300-armrest__img-item img {
        width: 100%;
        max-width: 175px;
        height: auto;
    }

    .op300-armrest__img-desc {
        font-size: 18px;
        color: var(--heading-color);
        font-weight: 700;
    }

    /* upholstery */
    .op300-upholstery {
        background-color: #f4f4f4;
        padding: 112px 7.03% 128px 7.03%;
    }

    .op300-upholstery__title {
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
        padding-left: 4.85%;
        padding-bottom: 56px;
    }

    .op300-upholstery__img-group {
        width: 100%;
        display: flex;
        flex-direction: row;
    }

    .op300-upholstery__img-item-left,
    .op300-upholstery__img-item-right {
        position: relative;
        transition: width 0.6s ease-in-out;
    }

    .op300-upholstery__img-item-left {
        margin-right: 0.5%;
        width: 76.48%;
        display: flex;
        justify-content: start;
        overflow: hidden;
    }

    .op300-upholstery__img-item-right {
        width: 23.52%;
        display: flex;
        justify-content: end;
        overflow: hidden;
        cursor: pointer;
    }

    .op300-upholstery__img-item-left img,
    .op300-upholstery__img-item-right img {
        max-width: 1262px;
        height: auto;
    }

    .op300-upholstery__img-item__desc-big-left,
    .op300-upholstery__img-item__desc-big-right {
        position: absolute;
        bottom: 11.11%;
        left: 11.11%;
        max-width: 708px;
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
    }

    .op300-upholstery__img-item__desc-small-left,
    .op300-upholstery__img-item__desc-small-right {
        position: absolute;
        bottom: 13.33%;
        left: 19.21%;
        right: 19.21%;
        opacity: 0;
        transition: opacity 0.6s ease-in-out;
    }

    .op300-upholstery__img-item__desc-big-left.show,
    .op300-upholstery__img-item__desc-big-right.show,
    .op300-upholstery__img-item__desc-small-left.show,
    .op300-upholstery__img-item__desc-small-right.show {
        opacity: 1;
    }

    .op300-upholstery__desc__big {
        font-size: var(--medium-heading-size);
        color: #ffffff;
        font-weight: 700;
    }

    .op300-upholstery__desc__small {
        font-size: var(--body-size);
        color: #ffffff;
        line-height: 1.5;
        margin-top: 16px;
    }

    .op300-upholstery__desc__small-title {
        font-size: var(--body-size);
        color: #ffffff;
        text-align: center;
        font-weight: 700;
    }

    /* components */
    .op300-components {
        width: 100%;
        background-color: #f4f4f4;
    }

    .op300-components__title {
        text-align: center;
        padding: 121px 0 76px 0;
        font-size: var(--large-heading-size);
        color: var(--heading-color);
        font-weight: 700;
    }

    .op300-components__component-group-wrap {
        width: 98%;
        overflow-x: auto;
        overflow-y: hidden;
        padding-bottom: 94px;
        margin: 0 auto;
    }

    .op300-components__component-group {
        width: fit-content;
        display: flex;
        margin: 0 auto;
        gap: 24px;
    }

    .op300-components__component {
        width: 418px;
    }

    .op300-components__component img {
        width: 100%;
        height: auto;
    }

    .op300-components__component__desc {
        padding-top: 24px;
        padding-right: 4.3%;
    }

    .op300-components__component__desc .component__name {
        font-size: 24px;
        color: var(--heading-color);
        font-weight: 700;
    }

    .op300-components__component__desc .component__content {
        font-size: 16px;
        margin-top: 12px;
        color: #8c8c8c;
        line-height: 1.5;
    }

    @media screen and (max-width: 1440px) {
        .op300-product-description {
            --large-heading-size: 48px;
            --medium-heading-size: 36px;
            --body-size: 24px;
        }

        /* intro */
        .op300-intro {
            padding: 7.08% 7.64% 5% 5.56%;
            --large-heading-size: 42px;
        }

        /* detail */
        .op300-detail {
            --large-heading-size: 42px;
        }

        .op300-detail__title {
            padding: 72px 0 12px 0;
        }

        .op300-detail__content {
            gap: 12px;
            padding: 0 20.83% 135px 20.83%;
        }

        .op300-detail__img-column {
            gap: 12px;
        }

        .op300-detail__img-group {
            gap: 12px;
        }

        /* mechanism */
        .op300-mechanism__background img {
            max-width: 1440px;
        }

        .op300-mechanism__text {
            left: 8.125%;
            top: 50%;
            transform: translateY(-50%);
        }

        .op300-backrest__background img,
        .op300-seat__background img,
        .op300-function__background img {
            max-width: 1440px;
        }

        .op300-backrest__text {
            right: 9.72%;
            top: 50%;
            transform: translateY(-50%);
            max-width: 536px;
        }

        .op300-seat__text {
            left: 8.82%;
            top: 50%;
            transform: translateY(-50%);
            max-width: 537px;
        }

        .op300-function__text {
            right: 9.72%;
            top: 50%;
            transform: translateY(-50%);
            max-width: 536px;
        }

        /* function-detail */
        .op300-function-detail {
            --medium-heading-size: 28px;
            --body-size: 20px;
        }

        .op300-function-detail__background img {
            max-width: 1237.5px;
        }

        .op300-function-detail__text-list {
            max-width: 497.25px;
        }

        .op300-function-detail__text-item {
            padding: 18px 0 18px 2px;
        }

        .op300-function-detail__text__small.show {
            margin: 10px 37px 50px 0;
        }

        #op300-function-detail-btn {
            width: 48px;
            height: 48px;
        }

        /* upholstery */
        .op300-upholstery {
            --medium-heading-size: 28px;
            --body-size: 20px;
            padding: 80px 0 72px 0;
        }

        .op300-upholstery__title {
            padding-left: 7.03%;
            padding-bottom: 64px;
        }

        .op300-upholstery__desc__small {
            margin-top: 8.5px;
        }

        .op300-upholstery__img-item-left img,
        .op300-upholstery__img-item-right img {
            max-width: 1101px;
        }

        .op300-upholstery__img-item__desc-big-left,
        .op300-upholstery__img-item__desc-big-right {
            left: 6.5%;
            bottom: 7.44%;
            max-width: 620px;
        }

        .op300-upholstery__img-item__desc-small-left,
        .op300-upholstery__img-item__desc-small-right {
            bottom: 11.95%;
        }

        /* armrest */
        .op300-armrest {
            --large-heading-size: 36px;
            --body-size: 20px;
        }

        .op300-armrest__title {
            right: 11.8%;
            max-width: 539px;
        }

        .op300-armrest__title__text__small {
            margin-top: 13px;
        }

        .op300-armrest__img-group {
            margin-top: 6px;
        }

        .op300-armrest__img-desc {
            font-size: 16px;
        }

        .op300-armrest__img-item img {
            max-width: 131px;
        }

        .op300-armrest__background img {
            max-width: 1440px;
        }

        /* components */
        .op300-components {
            --large-heading-size: 42px;
        }

        .op300-components__title {
            padding: 72px 0 56px 0;
        }

        .op300-components__component__desc .component__content {
            margin-top: 12px;
        }
    }

    @media screen and (max-width: 1200px) {
        .op300-product-description {
            --large-heading-size: 36px;
            --medium-heading-size: 36px;
            --body-size: 20px;
        }

        .op300-intro {
            padding: 7.33% 6.67% 5.33% 6.67%;
        }

        .op300-detail {
            --large-heading-size: 36px;
        }

        .op300-detail__title {
            padding: 60px 0 8px 0;
        }

        .op300-detail__content,
        .op300-detail__img-column,
        .op300-detail__img-group {
            gap: 10px;
        }

        .op300-mechanism__text {
            left: 6.67%;
            max-width: 554px;
        }

        .op300-mechanism__text__small,
        .op300-backrest__text__small,
        .op300-seat__text__small,
        .op300-function__text__small {
            margin-top: 8px;
        }

        .op300-mechanism__background img,
        .op300-backrest__background img,
        .op300-seat__background img,
        .op300-function__background img {
            max-width: 1200px;
        }

        .op300-backrest__text {
            right: 8.17%;
            max-width: 468px;
        }

        .op300-backrest__text__small.tip {
            font-size: 18px;
        }

        .op300-seat__text {
            left: 7.42%;
            max-width: 461px;
        }

        .op300-function__text {
            right: 8.17%;
            max-width: 470px;
        }

        /* function-detail */
        .op300-function-detail {
            --medium-heading-size: 24px;
            --body-size: 18px;
        }

        .op300-function-detail__text-list {
            max-width: 414px;
        }

        #op300-function-detail-btn {
            width: 40px;
            height: 40px;
        }

        .op300-function-detail__text__small.show {
            margin: 8px 31px 30px 0;
        }

        /* upholstery */
        .op300-upholstery {
            --medium-heading-size: 24px;
            --body-size: 18px;
            padding: 72px 0 56px 0;
        }

        .op300-upholstery__title {
            padding-bottom: 48px;
        }

        .op300-upholstery__img-item-left img,
        .op300-upholstery__img-item-right img {
            max-width: 918px;
        }

        .op300-upholstery__img-item__desc-big-left,
        .op300-upholstery__img-item__desc-big-right {
            max-width: 515px;
        }

        .op300-upholstery__img-item__desc-small-left,
        .op300-upholstery__img-item__desc-small-right {
            left: 18.21%;
            right: 18.21%;
        }

        /* armrest */
        .op300-armrest {
            --large-heading-size: 24px;
            --body-size: 18px;
        }

        .op300-armrest__title {
            max-width: 450px;
        }

        .op300-armrest__background img {
            max-width: 1200px;
        }

        .op300-armrest__img-desc {
            font-size: 14px;
        }

        .op300-armrest__img-item img {
            max-width: 110px;
        }

        .op300-armrest__title__text__small {
            margin-top: 8.5px;
        }

        /* components */
        .op300-components {
            --large-heading-size: 36px;
        }

        .op300-components__title {
            padding: 64px 0 48px 0;
        }

        .op300-components__component {
            width: 328px;
        }

        .op300-components__component-group-wrapper {
            padding-bottom: 64px;
        }

        .op300-components__component__desc .component__content {
            margin-top: 2px;
        }

        .op300-components__component-group-wrap {
            padding-bottom: 60px;
        }
    }

    @media screen and (max-width: 900px) {
        .op300-product-description {
            --large-heading-size: 32px;
            --medium-heading-size: 32px;
            --body-size: 18px;
        }

        .op300-intro {
            padding: 8% 11.56% 7.56% 8.89%;
        }

        .op300-intro__text {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            gap: 8px;
        }

        /* detail */
        .op300-detail {
            --large-heading-size: 32px;
        }

        .op300-detail__title {
            padding: 45px 0 0.5px 0;
        }

        .op300-detail__content {
            padding-bottom: 85px;
        }

        .op300-detail__content,
        .op300-detail__img-column,
        .op300-detail__img-group {
            gap: 7.5px;
        }

        .op300-mechanism__background {
            justify-content: center;
        }

        .op300-backrest__background img,
        .op300-seat__background img,
        .op300-function__background img {
            max-width: 900px;
        }

        .op300-backrest__text {
            right: 3.89%;
            max-width: 415px;
        }

        .op300-backrest__text__small.tip {
            font-size: 16px;
        }

        .op300-seat__text {
            left: 6.89%;
            max-width: 362px;
        }

        .op300-function__text {
            right: 3.89%;
            max-width: 415px;
        }

        /* function-detail */
        .op300-function-detail {
            padding: 7.11%;
            --medium-heading-size: 36px;
            --body-size: 24px;
        }

        .op300-function-detail__wrapper {
            display: flex;
            flex-direction: column-reverse;
        }

        .op300-function-detail__background {
            display: none;
        }

        .op300-function-detail__background img {
            width: 100%;
            height: auto;
        }

        .op300-function-detail__item-img {
            display: flex;
            justify-content: center;
            overflow: hidden;
            opacity: 0;
            height: 0;
            transition: opacity 0.5s ease-in-out;
            margin: 0 -6.78%;
        }

        .op300-function-detail__item-img.show {
            opacity: 1;
            height: auto;
        }

        .op300-function-detail__item-img img {
            width: 100%;
            height: auto;
        }

        .op300-function-detail__text-list {
            position: unset;
            transform: unset;
            width: unset;
            max-width: unset;
            padding: 7.11% 6.78%;
        }

        #op300-function-detail-btn {
            width: 48px;
            height: 48px;
        }

        .op300-function-detail__text__small.show {
            margin: 16px 62px 48px 0;
        }

        /* upholstery */
        .op300-upholstery {
            padding: 64px 0 48px 0;
        }

        .op300-upholstery__title {
            padding-left: 10.33%;
        }

        .op300-upholstery__img-item-left img,
        .op300-upholstery__img-item-right img {
            max-width: 688px;
        }

        .op300-upholstery__img-item__desc-big-left,
        .op300-upholstery__img-item__desc-big-right {
            max-width: 470px;
        }

        /* components */
        .op300-components {
            --large-heading-size: 32px;
        }

        .op300-components__title {
            padding: 56px 0 48px 0;
        }

        .op300-components__component-group {
            gap: 19px;
            margin-left: 14.11%;
            margin-right: 1%;
        }
    }

    @media screen and (max-width: 600px) {
        .op300-product-description {
            --large-heading-size: 7vw;
            --medium-heading-size: 6vw;
            --body-size: 4.67vw;
        }

        /* intro */
        .op300-intro {
            padding: 8vw 5.33vw 5.33vw 5.33vw;
        }

        .op300-intro__text {
            flex-direction: column;
            gap: 2.67vw;
        }

        /* detail */
        .op300-detail {
            font-size: 7vw;
        }

        .op300-detail__title {
            padding: 8vw 0 5.33vw 0;
        }

        .op300-detail__content {
            padding: 0 8vw 13.33vw 8vw;
            gap: 1.17vw;
        }

        .op300-detail__img-column {
            gap: 1.17vw;
        }

        .op300-detail__img-group {
            gap: 1.17vw;
        }

        .op300-detail__block,
        .op300-detail__img-2::before,
        .op300-detail__img-3::before,
        .op300-detail__img-4::before,
        .op300-detail__img-5::before,
        .op300-detail__img-6::before,
        .op300-detail__img-7::before {
            border-radius: 2.67vw;
        }

        /* mechanism */
        .op300-mechanism__text {
            transform: unset;
            top: 8vw;
            left: 5.33vw;
            right: 5.33vw;
            max-width: unset;
        }

        .op300-mechanism__text__small {
            margin-top: 2.67vw;
        }

        .op300-mechanism__background img {
            width: 100%;
        }

        .op300-mechanism__background img,
        .op300-backrest__background img,
        .op300-seat__background img,
        .op300-function__background img {
            width: 100%;
        }

        .op300-backrest,
        .op300-seat,
        .op300-function {
            display: flex;
            flex-direction: column-reverse;
            background-color: #ededed;
        }

        .op300-backrest__text,
        .op300-seat__text,
        .op300-function__text {
            max-width: unset;
            position: unset;
            transform: unset;
            padding: 8vw 5.33vw 5.33vw 5.33vw;
        }

        .op300-backrest__text__small,
        .op300-seat__text__small,
        .op300-function__text__small {
            margin-top: 2.67vw;
        }

        .op300-backrest__text__small.tip {
            font-size: var(--body-size);
        }

        /* function-detail */
        .op300-function-detail {
            padding: 8vw 5.33vw;
            --medium-heading-size: 5.33vw;
            --body-size: 4vw;
        }

        .op300-function-detail__text-list {
            padding: 8vw 4vw 9.33vw 4vw;
        }

        .op300-function-detail__text-item {
            padding: 3.67vw 0 3.67vw 0;
        }

        .op300-function-detail__text__big {
            gap: 5.83vw;
        }

        .op300-function-detail__text__small.show {
            margin: 2.67vw 10.83vw 4vw 0;
        }

        #op300-function-detail-btn {
            width: 9.33vw;
            height: 9.33vw;
        }

        .op300-function-detail__item-img {
            margin: 0 -4vw;
        }

        /* upholstery */
        .op300-upholstery {
            padding: 8vw 0 0 0;
            --medium-heading-size: 6vw;
            --body-size: 4.67vw;
        }

        .op300-upholstery__title {
            padding: 0 5.33vw 6.67vw 5.33vw;
        }

        .op300-upholstery__img-group {
            flex-direction: column;
        }

        .op300-upholstery__img-item-left,
        .op300-upholstery__img-item-right {
            width: 100% !important;
            pointer-events: none !important;
        }

        .op300-upholstery__img-item__desc-big-left,
        .op300-upholstery__img-item__desc-big-right {
            opacity: 1 !important;
            bottom: 8vw;
            left: 5.33vw;
            right: 5.33vw;
            max-width: unset;
        }

        .op300-upholstery__img-item__desc-small-left,
        .op300-upholstery__img-item__desc-small-right,
        .op300-upholstery__img-item__desc-small-left.show,
        .op300-upholstery__img-item__desc-small-right.show {
            opacity: 0 !important;
        }

        .op300-upholstery__desc__small {
            margin-top: 2.67vw;
        }

        .op300-upholstery__img-item-left img,
        .op300-upholstery__img-item-right img {
            width: 100%;
            height: auto;
        }

        /* armrest */
        .op300-armrest {
            --large-heading-size: 7vw;
            --body-size: 4.67vw;

            display: flex;
            flex-direction: column-reverse;
            background-color: #ededed;
        }

        .op300-armrest__background img {
            width: 100%;
        }

        .op300-armrest__title {
            position: unset;
            transform: unset;
            max-width: unset;
        }

        .op300-armrest__title__text {
            padding: 8vw 5.33vw 2.67vw 5.33vw;
        }

        .op300-armrest__img-group {
            margin: 0 3.6vw 7.67vw 3.6vw;
        }

        .op300-armrest__title__text__small {
            margin-top: 2.67vw;
        }

        .op300-armrest__img-item {
            gap: 2.67vw;
        }

        .op300-armrest__img-item img {
            width: 29.17vw;
            max-width: unset;
        }

        .op300-armrest__img-desc {
            font-size: 3vw;
        }

        /* components */
        .op300-components {
            --large-heading-size: 7vw;
        }

        .op300-components__title {
            padding: 8vw 0 5.33vw 0;
        }

        .op300-components__component-group-wrap {
            padding-bottom: 13.33vw;
        }

        .op300-components__component-group {
            margin-left: 15.17vw;
        }

        .op300-components__component {
            width: 69.67vw;
        }

        .op300-components__component__desc {
            padding: 4vw 3vw 0 0;
        }

        .op300-components__component__desc .component__name {
            font-size: 4.67vw;
        }

        .op300-components__component__desc .component__content {
            font-size: 3.67vw;
            margin-top: 1.17vw;
        }
    }
</style>

<div class="op300-product-description">
    <div class="op300-intro">
        <div class="op300-intro__text">
            <div class="op300-intro__text__title">Ergonomic Comfort For Every Workspace</div>
            <div class="op300-intro__text__content">
                Aimed for a truly ergonomic sitting experience, Boulies OP300 combines functionality, comfort and durability. With a variety of adjustable
                features, the chair ensures a personalised fit, providing optimal support for individuals of different heights. Whether you're working, gaming,
                or relaxing, the Boulies OP300 provides ergonomic support tailored to your needs, making it the perfect addition to any workspace.
            </div>
        </div>
    </div>

    <div class="op300-poster">
        <picture>
            <source
                media="(max-width:600px)"
                srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_1-sp_600x.jpg?v=1743066612" />
            <img
                alt=""
                src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_1_1920x.jpg?v=1743066613"
        /></picture>
    </div>

    <div class="op300-detail">
        <div class="op300-detail__title">Product Detail Display</div>
        <div class="op300-detail__content">
            <div class="op300-detail__img-column">
                <div class="op300-detail__block">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        fill="none"
                        version="1.1"
                        width="181"
                        height="52"
                        viewBox="0 0 181 52">
                        <g>
                            <path
                                d="M16.7432,51.8558Q11.7636,51.8558,7.97468,49.8363Q4.18581,47.8169,2.0929,44.1387Q0,40.4605,0,35.5562L0,16.3717Q0,11.5395,2.0929,7.8613Q4.18581,4.18308,7.97468,2.16366Q11.7636,0.144245,16.7432,0.144245Q21.7229,0.144245,25.5478,2.16366Q29.3728,4.18308,31.4657,7.8613Q33.5586,11.5395,33.5586,16.3717L33.5586,35.5562Q33.5586,40.4605,31.4657,44.1387Q29.3728,47.8169,25.5478,49.8363Q21.7229,51.8558,16.7432,51.8558ZM16.7432,43.129Q19.7743,43.129,21.5785,41.1456Q23.3828,39.1623,23.3828,35.9889L23.3828,16.0832Q23.3828,12.8377,21.5785,10.8544Q19.7743,8.87101,16.7432,8.87101Q13.7843,8.87101,11.9801,10.8544Q10.1758,12.8377,10.1758,16.0832L10.1758,35.9889Q10.1758,39.1623,11.9801,41.1456Q13.7843,43.129,16.7432,43.129ZM58.4569,0.649101Q62.7871,0.649101,66.1429,2.5964Q69.4988,4.54369,71.3391,8.07767Q73.1794,11.6116,73.1794,16.1553Q73.1794,20.6269,71.3391,24.0527Q69.4988,27.4785,66.1429,29.3537Q62.7871,31.2288,58.4569,31.2288L50.6627,31.2288Q50.3018,31.2288,50.3018,31.5895L50.3018,50.3412Q50.3018,50.7018,50.0492,50.9542Q49.7966,51.2067,49.4358,51.2067L40.992,51.2067Q40.6312,51.2067,40.3786,50.9542Q40.126,50.7018,40.126,50.3412L40.126,1.51456Q40.126,1.15395,40.3786,0.901527Q40.6312,0.649101,40.992,0.649101L58.4569,0.649101ZM56.797,23.1512Q59.6116,23.1512,61.3076,21.3121Q63.0036,19.473,63.0036,16.3717Q63.0036,13.1983,61.3076,11.3232Q59.6116,9.44799,56.797,9.44799L50.6627,9.44799Q50.3018,9.44799,50.3018,9.8086L50.3018,22.7906Q50.3018,23.1512,50.6627,23.1512L56.797,23.1512ZM105.872,26.8294Q107.315,30.2913,107.315,34.9792Q107.315,40.0999,105.583,43.7781Q103.851,47.6006,100.495,49.6921Q97.1395,51.7836,92.232,51.7836Q86.3864,51.7836,82.4171,48.7545Q78.4478,45.7254,77.4374,40.6768Q77.0044,38.7295,76.86,35.7725Q76.86,34.9071,77.7261,34.9071L86.242,34.9071Q87.108,34.9071,87.108,35.7725Q87.2524,38.0083,87.5411,38.9459Q87.9019,40.8932,89.1288,41.975Q90.3557,43.0569,92.232,43.0569Q93.9641,43.0569,95.1549,42.0832Q96.3457,41.1096,96.8509,39.3065Q97.4282,37.1429,97.4282,34.258Q97.4282,31.0846,96.7065,29.1373Q95.4797,25.387,92.1599,25.387Q90.8608,25.387,89.4175,26.3967Q89.201,26.5409,88.9123,26.5409Q88.4793,26.5409,88.1906,26.2524L83.9326,21.5645Q83.6439,21.1318,83.6439,20.9154Q83.6439,20.6269,83.9326,20.3384L94.1806,9.88072Q94.325,9.73648,94.2528,9.59223Q94.1806,9.44799,93.9641,9.44799L78.6643,9.44799Q78.3034,9.44799,78.0508,9.19556Q77.7982,8.94314,77.7982,8.58253L77.7982,1.58668Q77.7982,1.22608,78.0508,0.973648Q78.3034,0.721222,78.6643,0.721222L106.161,0.721222Q106.522,0.721222,106.774,0.973648Q107.027,1.22608,107.027,1.58668L107.027,8.79889Q107.027,9.30375,106.594,9.88072L97.5726,19.7614Q97.3561,20.1221,97.7169,20.1942Q100.531,20.699,102.588,22.3939Q104.645,24.0888,105.872,26.8294ZM128.389,52Q121.244,52,116.986,47.7809Q112.728,43.5617,112.728,36.4216L112.728,15.5062Q112.728,8.43828,116.986,4.21914Q121.244,0,128.389,0Q135.533,0,139.828,4.21914Q144.122,8.43828,144.122,15.5062L144.122,36.4216Q144.122,43.5617,139.828,47.7809Q135.533,52,128.389,52ZM128.389,43.2732Q130.915,43.2732,132.43,41.5784Q133.946,39.8835,133.946,36.9986L133.946,15.0014Q133.946,12.1165,132.43,10.4577Q130.915,8.79889,128.389,8.79889Q125.863,8.79889,124.383,10.4577Q122.904,12.1165,122.904,15.0014L122.904,36.9986Q122.904,39.8835,124.383,41.5784Q125.863,43.2732,128.389,43.2732ZM165.267,52Q158.122,52,153.864,47.7809Q149.606,43.5617,149.606,36.4216L149.606,15.5062Q149.606,8.43828,153.864,4.21914Q158.122,0,165.267,0Q172.412,0,176.706,4.21914Q181,8.43828,181,15.5062L181,36.4216Q181,43.5617,176.706,47.7809Q172.412,52,165.267,52ZM165.267,43.2732Q167.793,43.2732,169.309,41.5784Q170.824,39.8835,170.824,36.9986L170.824,15.0014Q170.824,12.1165,169.309,10.4577Q167.793,8.79889,165.267,8.79889Q162.741,8.79889,161.262,10.4577Q159.782,12.1165,159.782,15.0014L159.782,36.9986Q159.782,39.8835,161.262,41.5784Q162.741,43.2732,165.267,43.2732Z"
                                fill="#FFFFFF"
                                fill-opacity="1" />
                        </g>
                    </svg>
                </div>
                <div class="op300-detail__img-2">
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_2_280x.png?v=1743066609"
                        alt="" />
                </div>
            </div>

            <div class="op300-detail__img-3">
                <img
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_3_528x.png?v=1743066609"
                    alt="" />
            </div>

            <div class="op300-detail__img-column">
                <div class="op300-detail__img-4">
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_4_360x.png?v=1743066609"
                        alt="" />
                </div>
                <div class="op300-detail__img-5">
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_5_280x.png?v=1743066609"
                        alt="" />
                </div>
            </div>

            <div class="op300-detail__img-group">
                <div class="op300-detail__img-6">
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_6_392x.png?v=1743066609"
                        alt="" />
                </div>
                <div class="op300-detail__img-7">
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_7_712x.png?v=1743066609"
                        alt="" />
                </div>
            </div>
        </div>
    </div>

    <div class="op300-mechanism">
        <div class="op300-mechanism__background">
            <picture>
                <source
                    media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_8-sp_600x.jpg?v=1743066612" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_8_1920x.png?v=1743066613"
            /></picture>
        </div>
        <div class="op300-mechanism__text">
            <div class="op300-mechanism__text__big">Boulies Multi Tilt Mechanism</div>
            <div class="op300-mechanism__text__small">
                Engineered for a tailored seating experience with Boulies Chairs, the Boulies OP300 maintains with our Multi Tilt Mechanism to help you get a
                comfortable and ergonomic sitting posture. Built for durability and adjustments, the tilt mechanism provides long-lasting support while adapting
                to your unique needs.
            </div>
        </div>
    </div>

    <div class="op300-backrest">
        <div class="op300-backrest__background">
            <picture>
                <source
                    media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_9-sp_600x.png?v=1745917574" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_9_1920x.png?v=1745917575"
            /></picture>
        </div>
        <div class="op300-backrest__text">
            <div class="op300-backrest__text__big">Ergonomic Backrest Recline</div>
            <div class="op300-backrest__text__small">
                The Boulies OP300’s backrest can be easily adjusted to lean forward or backwards, offering the ideal balance between focus and relaxation.
                Furthermore, Boulies engineers the backrest to perfectly align with the spine's natural curve to help reduce strain on your back during long
                hours of sitting.
            </div>
            <div class="op300-backrest__text__small tip">*While adjust seat angle, the backrest can recline up to 113°.</div>
        </div>
    </div>

    <div class="op300-seat">
        <div class="op300-seat__background">
            <picture>
                <source
                    media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_10-sp_600x.jpg?v=1743066611" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_10_1920x.jpg?v=1743066612"
            /></picture>
        </div>
        <div class="op300-seat__text">
            <div class="op300-seat__text__big">Adjustable Seat Depth for a Perfect Fit</div>
            <div class="op300-seat__text__small">
                Ensured a customised and ergonomic sitting experience, the Boulies OP300 Multi tilt mechanism features a seat depth adjustment to accommodate
                individuals of different sizes. By allowing you to adjust the seat’s depth, this feature helps maintain proper thigh support and posture,
                reducing strain and enhancing overall comfort during long hours of sitting.
            </div>
        </div>
    </div>

    <div class="op300-function">
        <div class="op300-function__background">
            <picture>
                <source
                    media="(max-width:600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_11-sp_600x.jpg?v=1749798821" />
                <img
                    alt=""
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_11_1920x.jpg?v=1749798822"
            /></picture>
        </div>
        <div class="op300-function__text">
            <div class="op300-function__text__big">Adjustable Seat Angle &amp; Rocking Function</div>
            <div class="op300-function__text__small">
                The Boulies OP300 is designed to adapt to your seating needs with its adjustable seat angle or rocking function. Whether you prefer to lean back
                and relax or maintain an upright posture for focused work, this feature provides the flexibility to suit different seating preferences. Enjoy a
                dynamic sitting experience that supports both productivity and relaxation.
            </div>
        </div>
    </div>

    <div class="op300-function-detail">
        <div class="op300-function-detail__wrapper">
            <div
                class="op300-function-detail__background show"
                id="function-detail-img-1">
                <picture>
                    <source
                        media="(max-width:900px)"
                        srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-sp_900x.jpg?v=1743066612" />
                    <img
                        alt=""
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12_1920x.jpg?v=1743066612"
                /></picture>
            </div>
            <div
                class="op300-function-detail__background"
                id="function-detail-img-2">
                <picture>
                    <source
                        media="(max-width:900px)"
                        srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-2-sp_900x.jpg?v=1743066612" />
                    <img
                        alt=""
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-2_1920x.jpg?v=1743066612"
                /></picture>
            </div>
            <div
                class="op300-function-detail__background"
                id="function-detail-img-3">
                <picture>
                    <source
                        media="(max-width:900px)"
                        srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-3-sp_900x.jpg?v=1743066612" />
                    <img
                        alt=""
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-3_1920x.jpg?v=1743066612"
                /></picture>
            </div>

            <div class="op300-function-detail__text-list">
                <div class="op300-function-detail__text-item selected">
                    <div class="op300-function-detail__text__big">
                        <span>Height Adjustable Backrest</span>
                        <svg
                            id="op300-function-detail-btn"
                            class="rotate"
                            data-img="function-detail-img-1"
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            fill="none"
                            version="1.1"
                            width="64"
                            height="64"
                            viewbox="0 0 64 64">
                            <defs>
                                <clippath id="master_svg0_89_1257">
                                    <rect
                                        x="0"
                                        y="0"
                                        width="64"
                                        height="64"
                                        rx="0"></rect>
                                </clippath>
                            </defs>
                            <g clip-path="url(#master_svg0_89_1257)">
                                <g>
                                    <g transform="matrix(0,1,-1,0,71.99904823303223,-25.332582473754883)">
                                        <path
                                            d="M51.994565353393554,23.838435879638673C51.168995353393555,23.104598879638672,49.90485535339356,23.17896187963867,49.171018353393556,24.00452687963867C48.437181353393555,24.830092879638674,48.51154435339355,26.09423287963867,49.33710935339356,26.828072879638672C49.33710935339356,26.828072879638672,51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673C51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673ZM66.66581535339355,39.55543287963867C66.66581535339355,39.55543287963867,68.03911535339356,41.00953287963867,68.03911535339356,41.00953287963867C68.44681535339356,40.624432879638675,68.67411535339355,40.085932879638676,68.66561535339355,39.52523287963867C68.65711535339355,38.96463287963867,68.41371535339356,38.43323287963867,67.99451535339355,38.060632879638675C67.99451535339355,38.060632879638675,66.66581535339355,39.55543287963867,66.66581535339355,39.55543287963867C66.66581535339355,39.55543287963867,66.66581535339355,39.55543287963867,66.66581535339355,39.55543287963867ZM49.29258935339355,53.21243287963867C48.48955435339356,53.97083287963867,48.453386353393554,55.23673287963867,49.21181035339355,56.03993287963867C49.970235353393555,56.84283287963867,51.23604535339356,56.879132879638675,52.03908535339355,56.12073287963867C52.03908535339355,56.12073287963867,49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867C49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867ZM49.33710935339356,26.828072879638672C49.33710935339356,26.828072879638672,65.33711535339356,41.050332879638674,65.33711535339356,41.050332879638674C65.33711535339356,41.050332879638674,67.99451535339355,38.060632879638675,67.99451535339355,38.060632879638675C67.99451535339355,38.060632879638675,51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673C51.994565353393554,23.838435879638673,49.33710935339356,26.828072879638672,49.33710935339356,26.828072879638672C49.33710935339356,26.828072879638672,49.33710935339356,26.828072879638672,49.33710935339356,26.828072879638672ZM65.29261535339356,38.10143287963867C65.29261535339356,38.10143287963867,49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867C49.29258935339355,53.21243287963867,52.03908535339355,56.12073287963867,52.03908535339355,56.12073287963867C52.03908535339355,56.12073287963867,68.03911535339356,41.00953287963867,68.03911535339356,41.00953287963867C68.03911535339356,41.00953287963867,65.29261535339356,38.10143287963867,65.29261535339356,38.10143287963867C65.29261535339356,38.10143287963867,65.29261535339356,38.10143287963867,65.29261535339356,38.10143287963867Z"
                                            fill="#8C8C8C"
                                            fill-opacity="1"></path>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                    <div class="op300-function-detail__text__small show">
                        Everyone deserves the right support. With an adjustable backrest height, the OP300 ensures a personalised fit, making it suitable for
                        individuals of different heights. Designed with both functionality and comfort in mind, the Boulies OP300 is the perfect addition to any
                        workspace, delivering ergonomic support tailored to your needs.
                    </div>
                    <div
                        class="op300-function-detail__item-img show"
                        id="function-detail-img-1">
                        <picture>
                            <source
                                media="(max-width:900px)"
                                srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-sp_900x.jpg?v=1743066612" />
                            <img
                                alt=""
                                src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12_1920x.jpg?v=1743066612"
                        /></picture>
                    </div>
                </div>
                <div class="op300-function-detail__text-item">
                    <div class="op300-function-detail__text__big">
                        <span>Multi-Directional Adjustable Headrest</span>
                        <svg
                            id="op300-function-detail-btn"
                            data-img="function-detail-img-2"
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            fill="none"
                            version="1.1"
                            width="64"
                            height="64"
                            viewbox="0 0 64 64">
                            <defs>
                                <clippath id="master_svg0_89_1257">
                                    <rect
                                        x="0"
                                        y="0"
                                        width="64"
                                        height="64"
                                        rx="0"></rect>
                                </clippath>
                            </defs>
                            <g clip-path="url(#master_svg0_89_1257)">
                                <g>
                                    <g transform="matrix(0,1,-1,0,71.99904823303223,-25.332582473754883)">
                                        <path
                                            d="M51.994565353393554,23.838435879638673C51.168995353393555,23.104598879638672,49.90485535339356,23.17896187963867,49.171018353393556,24.00452687963867C48.437181353393555,24.830092879638674,48.51154435339355,26.09423287963867,49.33710935339356,26.828072879638672C49.33710935339356,26.828072879638672,51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673C51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673ZM66.66581535339355,39.55543287963867C66.66581535339355,39.55543287963867,68.03911535339356,41.00953287963867,68.03911535339356,41.00953287963867C68.44681535339356,40.624432879638675,68.67411535339355,40.085932879638676,68.66561535339355,39.52523287963867C68.65711535339355,38.96463287963867,68.41371535339356,38.43323287963867,67.99451535339355,38.060632879638675C67.99451535339355,38.060632879638675,66.66581535339355,39.55543287963867,66.66581535339355,39.55543287963867C66.66581535339355,39.55543287963867,66.66581535339355,39.55543287963867,66.66581535339355,39.55543287963867ZM49.29258935339355,53.21243287963867C48.48955435339356,53.97083287963867,48.453386353393554,55.23673287963867,49.21181035339355,56.03993287963867C49.970235353393555,56.84283287963867,51.23604535339356,56.879132879638675,52.03908535339355,56.12073287963867C52.03908535339355,56.12073287963867,49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867C49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867ZM49.33710935339356,26.828072879638672C49.33710935339356,26.828072879638672,65.33711535339356,41.050332879638674,65.33711535339356,41.050332879638674C65.33711535339356,41.050332879638674,67.99451535339355,38.060632879638675,67.99451535339355,38.060632879638675C67.99451535339355,38.060632879638675,51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673C51.994565353393554,23.838435879638673,49.33710935339356,26.828072879638672,49.33710935339356,26.828072879638672C49.33710935339356,26.828072879638672,49.33710935339356,26.828072879638672,49.33710935339356,26.828072879638672ZM65.29261535339356,38.10143287963867C65.29261535339356,38.10143287963867,49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867C49.29258935339355,53.21243287963867,52.03908535339355,56.12073287963867,52.03908535339355,56.12073287963867C52.03908535339355,56.12073287963867,68.03911535339356,41.00953287963867,68.03911535339356,41.00953287963867C68.03911535339356,41.00953287963867,65.29261535339356,38.10143287963867,65.29261535339356,38.10143287963867C65.29261535339356,38.10143287963867,65.29261535339356,38.10143287963867,65.29261535339356,38.10143287963867Z"
                                            fill="#8C8C8C"
                                            fill-opacity="1"></path>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                    <div class="op300-function-detail__text__small">
                        The Boulies OP300 features a fully adjustable headrest that moves in height, angle, and depth, offering personalised neck or head
                        support for every user. Whether you're working, reading, or relaxing, this ergonomic design helps reduce strain and enhances comfort,
                        ensuring proper support throughout the day.
                    </div>
                    <div
                        class="op300-function-detail__item-img"
                        id="function-detail-img-2">
                        <picture>
                            <source
                                media="(max-width:900px)"
                                srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-2-sp_900x.jpg?v=1743066612" />
                            <img
                                alt=""
                                src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-2_1920x.jpg?v=1743066612"
                        /></picture>
                    </div>
                </div>
                <div class="op300-function-detail__text-item">
                    <div class="op300-function-detail__text__big">
                        <span>Wide &amp; Comfortable Seat Cushion</span>
                        <svg
                            id="op300-function-detail-btn"
                            data-img="function-detail-img-3"
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            fill="none"
                            version="1.1"
                            width="64"
                            height="64"
                            viewbox="0 0 64 64">
                            <defs>
                                <clippath id="master_svg0_89_1257">
                                    <rect
                                        x="0"
                                        y="0"
                                        width="64"
                                        height="64"
                                        rx="0"></rect>
                                </clippath>
                            </defs>
                            <g clip-path="url(#master_svg0_89_1257)">
                                <g>
                                    <g transform="matrix(0,1,-1,0,71.99904823303223,-25.332582473754883)">
                                        <path
                                            d="M51.994565353393554,23.838435879638673C51.168995353393555,23.104598879638672,49.90485535339356,23.17896187963867,49.171018353393556,24.00452687963867C48.437181353393555,24.830092879638674,48.51154435339355,26.09423287963867,49.33710935339356,26.828072879638672C49.33710935339356,26.828072879638672,51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673C51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673ZM66.66581535339355,39.55543287963867C66.66581535339355,39.55543287963867,68.03911535339356,41.00953287963867,68.03911535339356,41.00953287963867C68.44681535339356,40.624432879638675,68.67411535339355,40.085932879638676,68.66561535339355,39.52523287963867C68.65711535339355,38.96463287963867,68.41371535339356,38.43323287963867,67.99451535339355,38.060632879638675C67.99451535339355,38.060632879638675,66.66581535339355,39.55543287963867,66.66581535339355,39.55543287963867C66.66581535339355,39.55543287963867,66.66581535339355,39.55543287963867,66.66581535339355,39.55543287963867ZM49.29258935339355,53.21243287963867C48.48955435339356,53.97083287963867,48.453386353393554,55.23673287963867,49.21181035339355,56.03993287963867C49.970235353393555,56.84283287963867,51.23604535339356,56.879132879638675,52.03908535339355,56.12073287963867C52.03908535339355,56.12073287963867,49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867C49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867ZM49.33710935339356,26.828072879638672C49.33710935339356,26.828072879638672,65.33711535339356,41.050332879638674,65.33711535339356,41.050332879638674C65.33711535339356,41.050332879638674,67.99451535339355,38.060632879638675,67.99451535339355,38.060632879638675C67.99451535339355,38.060632879638675,51.994565353393554,23.838435879638673,51.994565353393554,23.838435879638673C51.994565353393554,23.838435879638673,49.33710935339356,26.828072879638672,49.33710935339356,26.828072879638672C49.33710935339356,26.828072879638672,49.33710935339356,26.828072879638672,49.33710935339356,26.828072879638672ZM65.29261535339356,38.10143287963867C65.29261535339356,38.10143287963867,49.29258935339355,53.21243287963867,49.29258935339355,53.21243287963867C49.29258935339355,53.21243287963867,52.03908535339355,56.12073287963867,52.03908535339355,56.12073287963867C52.03908535339355,56.12073287963867,68.03911535339356,41.00953287963867,68.03911535339356,41.00953287963867C68.03911535339356,41.00953287963867,65.29261535339356,38.10143287963867,65.29261535339356,38.10143287963867C65.29261535339356,38.10143287963867,65.29261535339356,38.10143287963867,65.29261535339356,38.10143287963867Z"
                                            fill="#8C8C8C"
                                            fill-opacity="1"></path>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                    <div class="op300-function-detail__text__small">
                        Featured a wide-sized seat cushion, Boulies OP300 chair is suitable for individuals of various weights. This seat cushion is designed
                        for softness yet supportive, providing exceptional comfort while helping to relieve pressure on your tailbone.
                    </div>
                    <div
                        class="op300-function-detail__item-img"
                        id="function-detail-img-3">
                        <picture>
                            <source
                                media="(max-width:900px)"
                                srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-3-sp_900x.jpg?v=1743066612" />
                            <img
                                alt=""
                                src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_12-3_1920x.jpg?v=1743066612"
                        /></picture>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="op300-upholstery">
        <div class="op300-upholstery__title">
            Two Backrest Material Options
            <br />
            For Customised Comfort
        </div>
        <div class="op300-upholstery__img-group">
            <div class="op300-upholstery__img-item-left">
                <picture>
                    <source
                        media="(max-width: 600px)"
                        srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_13-sp_600x.jpg?v=1743066612" />
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_13-unfold_1262x.jpg?v=1743066613"
                        alt=""
                /></picture>
                <div class="op300-upholstery__img-item__desc-big-left show">
                    <div class="op300-upholstery__desc__big">Mesh Backrest – Maximum Breathability</div>
                    <div class="op300-upholstery__desc__small">
                        The mesh backrest is designed to enhance airflow, keeping you cool and comfortable, even during long hours of sitting. Its breathable
                        structure helps prevent heat buildup, making it an excellent choice for those who prioritise ventilation and freshness.
                    </div>
                </div>
                <div class="op300-upholstery__img-item__desc-small-left">
                    <div class="op300-upholstery__desc__small-title">Mesh Backrest<br />– Maximum Breathability</div>
                </div>
            </div>

            <div class="op300-upholstery__img-item-right">
                <picture>
                    <source
                        media="(max-width: 600px)"
                        srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_14-sp_600x.jpg?v=1743066613" />
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_14-2-unfold_x1262.jpg?v=1743066614"
                        alt=""
                /></picture>
                <div class="op300-upholstery__img-item__desc-big-right">
                    <div class="op300-upholstery__desc__big">Padded Mesh Backrest – Soft Yet Breathable</div>
                    <div class="op300-upholstery__desc__small">
                        The padded mesh backrest offers a perfect blend of softness and breathability. Using Boulies' specialised sewing technique, this design
                        provides extra comfort while maintaining airflow, ensuring a supportive yet plush seating experience.
                    </div>
                </div>
                <div class="op300-upholstery__img-item__desc-small-right show">
                    <div class="op300-upholstery__desc__small-title">Padded Mesh Backrest<br />– Soft Yet Breathable</div>
                </div>
            </div>
        </div>
    </div>

    <div class="op300-armrest">
        <div class="op300-armrest__background">
            <picture>
                <source
                    media="(max-width: 600px)"
                    srcset="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_15-sp.jpg?v=1743066613" />
                <img
                    alt="Armrest Background"
                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_15_1920x.jpg?v=1743066613"
            /></picture>
        </div>
        <div class="op300-armrest__title">
            <div class="op300-armrest__title__text">
                <div class="op300-armrest__title__text__big">Boulies Adjustable Armrests</div>
                <div class="op300-armrest__title__text__small">
                    This armrest can adjust up, down and angles to your exact requirements. The arm top pad supports your elbows in softness, providing sublime
                    support as you sit. The soft arm pad top is perfect for supporting your elbow. And for those who prefer an unencumbered sit, it's
                    detachable, offering you the freedom of choice in your seating experience.
                </div>
            </div>
            <div class="op300-armrest__img-group">
                <div class="op300-armrest__img-item">
                    <img
                        alt="Height Adjustment"
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_15-height_adjustment_175x.png?v=1743066608" />
                    <div class="op300-armrest__img-desc">Height Adjustment</div>
                </div>
                <div class="op300-armrest__img-item">
                    <img
                        alt="Depth Adjustment"
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_15-depth_adjustment_175x.png?v=1743066609" />
                    <div class="op300-armrest__img-desc">Depth Adjustment</div>
                </div>
                <div class="op300-armrest__img-item">
                    <img
                        alt="Pivot Adjustment"
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_15-pivot_adjustment_175x.png?v=1743066609" />
                    <div class="op300-armrest__img-desc">Pivot Adjustment</div>
                </div>
            </div>
        </div>
    </div>

    <div class="op300-components">
        <div class="op300-components__title">Featured Components</div>
        <div class="op300-components__component-group-wrap">
            <div class="op300-components__component-group">
                <div class="op300-components__component">
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_16_418x.jpg?v=1743066609"
                        alt="" />
                    <div class="op300-components__component__desc">
                        <div class="component__name">Heavy-duty Gas Lift</div>
                        <div class="component__content">
                            Equipped with a premium gas lift mechanism that provides a stable and heavy-duty support system. This robust component is
                            meticulously engineered to offer seamless height adjustment and to withstand the demands of long-term use.
                        </div>
                    </div>
                </div>
                <div class="op300-components__component">
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_17_418x.jpg?v=1743066609"
                        alt="" />
                    <div class="op300-components__component__desc">
                        <div class="component__name">PU Casters</div>
                        <div class="component__content">
                            Made to be durable yet wooden floor friendly, XL PU caster is so smooth that can be used on the clean wooden floor.
                        </div>
                    </div>
                </div>
                <div class="op300-components__component">
                    <img
                        src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300-detail_18_418x.jpg?v=1743066609"
                        alt="" />
                    <div class="op300-components__component__desc">
                        <div class="component__name">Heavy-duty Wheel Base</div>
                        <div class="component__content">
                            Engineered with premium plastic to provide extremely stability and strength, each base is not only perfectly balanced but also
                            strong bearing capacity.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- specification start -->
    <div
        class="bt20-cd_spec"
        id="bt20-collectionSpecifications">
        <div
            class="bprtdd_item bprtdd_detail"
            id="singleProductDetail">
            <h2 class="bprtdd_detailTitle">Product Warranty</h2>
            <div class="bprtdd_detailWrapper clearfix">
                <div class="bprtdd_warrantySection">
                    <span
                        class="bps_warrantyTime"
                        data-warrantytime="24">
                        2 years limited Warranty <br />
                        <a href="/pages/warranty">Learn More</a>
                    </span>
                </div>
                <div class="bprtdd_warrantySection">
                    <span
                        class="bps_warrantyTime"
                        data-warrantytime="24">
                        14 days return <br />
                        <a href="/pages/return-and-refund">Learn More</a>
                    </span>
                </div>
            </div>
        </div>
        <div
            class="bprtdd_item bprtdd_detail"
            id="singleProductDetail">
            <h2 class="bprtdd_detailTitle">Product Details</h2>
            <div>
                <div class="bprtdd_detail_line">
                    <div class="bprtdd_detail_line_head">
                        <h3>DIMENSIONS</h3>
                    </div>
                    <div class="bprtdd_detail_line_body">
                        <div class="bt51-dimensions-list">
                            <a
                                href="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300_dimensions.jpg?v=1747908341"
                                class="bt51-dimension dynamic-gallery-item">
                                <img
                                    src="https://cdn.shopify.com/s/files/1/2127/6275/files/EP300_dimensions_300x.jpg?v=1747908341"
                                    alt=""
                                    class="bt51-dimension-image-preview" />
                                <p class="bt51-dimension-decription">Technical Specification - OP300</p>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="bprtdd_detail_line">
                    <div class="bprtdd_detail_line_head">
                        <h3>FUNCTIONS</h3>
                    </div>
                    <div class="bprtdd_detail_line_body">
                        <div class="bprtdd_detail_section">360° swivel</div>
                        <div class="bprtdd_detail_section">backrest recline (85°-113°)</div>
                        <div class="bprtdd_detail_section">Adjustable backrest Height</div>
                        <div class="bprtdd_detail_section">Seat rocking (9.5°)</div>
                        <div class="bprtdd_detail_section">Seat Tilt position Lock (-1.7°-7.8°)</div>
                        <div class="bprtdd_detail_section">Adjustable head support</div>
                        <div class="bprtdd_detail_section">Seat depth adjustment</div>
                        <div class="bprtdd_detail_section">Armrest 6 ways adjustable</div>
                    </div>
                </div>
                <div class="bprtdd_detail_line">
                    <div class="bprtdd_detail_line_head">
                        <h3>SPECIFICATION</h3>
                    </div>
                    <div class="bprtdd_detail_line_body">
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Upholstery</span> <span class="bprtdd_detail_itemDescribe"> Airy Mesh / Padded Mesh</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Frame</span> <span class="bprtdd_detail_itemDescribe">Reinforced plastic frame</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName"> Five star foot base</span>
                            <span class="bprtdd_detail_itemDescribe">Heavy Duty Engineering plastic Base</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Tilt mechanism</span> <span class="bprtdd_detail_itemDescribe">Multi tilt</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Casters</span> <span class="bprtdd_detail_itemDescribe">6cm PU casters</span>
                        </div>
                        <div class="bprtdd_detail_section">
                            <span class="bprtdd_detail_itemName">Hydraulics</span> <span class="bprtdd_detail_itemDescribe">Class 3 (Heavy Duty)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- specification end -->
</div>

<script>
    // function detail toggle
    document.querySelectorAll(".op300-function-detail__text-item").forEach((div) => {
        div.addEventListener("click", function () {
            const sibling = div.querySelector(".op300-function-detail__text__small");

            const btn = div.querySelector("#op300-function-detail-btn");
            const imgId = btn.getAttribute("data-img");
            const imgs = document.querySelectorAll("#" + imgId);

            const allBtns = document.querySelectorAll("#op300-function-detail-btn");
            const isOnlyExpanded = [...allBtns].every((b) => b === btn || !b.classList.contains("rotate"));
            if (isOnlyExpanded && btn.classList.contains("rotate")) {
                return;
            }

            sibling.classList.toggle("show");
            btn.classList.toggle("rotate");
            div.classList.toggle("selected");
            imgs?.forEach((img) => {
                if (img) img.classList.toggle("show");
            });

            allBtns.forEach((otherBtn) => {
                if (otherBtn !== btn) {
                    const otherParent = otherBtn.parentElement;
                    const otherSibling = otherParent.nextElementSibling;
                    const otherParentList = otherParent.parentElement;
                    const otherImgId = otherBtn.getAttribute("data-img");
                    const otherImgs = document.querySelectorAll("#" + otherImgId);

                    otherSibling.classList.remove("show");
                    otherBtn.classList.remove("rotate");
                    otherParentList.classList.remove("selected");
                    otherImgs?.forEach((img) => {
                        if (img) img.classList.remove("show");
                    });
                }
            });
        });
    });

    // upholstery left click
    document.querySelector(".op300-upholstery__img-item-left").addEventListener("click", function () {
        let left = document.querySelector(".op300-upholstery__img-item-left");
        let right = document.querySelector(".op300-upholstery__img-item-right");
        left.style.width = "76.48%";
        right.style.width = "23.52%";
        left.style.cursor = "default";
        right.style.cursor = "pointer";

        document.querySelector(".op300-upholstery__img-item__desc-big-right").classList.remove("show");
        document.querySelector(".op300-upholstery__img-item__desc-small-right").classList.add("show");

        document.querySelector(".op300-upholstery__img-item__desc-small-left").classList.remove("show");
        document.querySelector(".op300-upholstery__img-item__desc-big-left").classList.add("show");
    });

    // upholstery right click
    document.querySelector(".op300-upholstery__img-item-right").addEventListener("click", function () {
        let left = document.querySelector(".op300-upholstery__img-item-left");
        let right = document.querySelector(".op300-upholstery__img-item-right");
        left.style.width = "23.52%";
        right.style.width = "76.48%";
        left.style.cursor = "pointer";
        right.style.cursor = "default";

        document.querySelector(".op300-upholstery__img-item__desc-small-right").classList.remove("show");
        document.querySelector(".op300-upholstery__img-item__desc-big-right").classList.add("show");

        document.querySelector(".op300-upholstery__img-item__desc-small-left").classList.add("show");
        document.querySelector(".op300-upholstery__img-item__desc-big-left").classList.remove("show");
    });
</script>
