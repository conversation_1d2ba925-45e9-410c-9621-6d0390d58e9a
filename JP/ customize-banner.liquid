<a
  href="{% if section.settings.url %}{{ section.settings.url }}{% else %}javascript:void(0){% endif %}"
  class="b5-banner b5-banner--{{ section.settings.alignment }}"
>
  <picture>
    <source media="(max-width: 650px)" srcset="{{ section.settings.sp_image.src | img_url: '650x' }}">
    <img
      class="b5-banner-image"
      src="{{ section.settings.pc_image.src | img_url: 'master' }}"
      alt="boulies chairs index banner"
    >
  </picture>
  {% if section.settings.Show_title %}
    {% capture locale_title %}{{ request.locale.iso_code }}_title{% endcapture %}
    {% capture locale_subtitle %}{{ request.locale.iso_code }}_subtitle{% endcapture %}
    <div class="bt20-it_content">
      <div class="bt20-it_basicContent">
        <div class="bt20-it_firstContent">{{ section.settings[locale_title] }}</div>
        <div
          class="bt20-it_secondContent"
          {% if section.settings.is_inverted %}
            style="text-shadow: 1px 1px #88898b;"
          {% endif %}
        >
          {{ section.settings[locale_subtitle] }}
        </div>
      </div>
    </div>
  {% endif %}
</a>

{% schema %}
{
  "name": "Frontpage Single Banner",
  "class": "customize--banner",
  "settings": [
    {
      "id": "pc_image",
      "type": "image_picker",
      "label": "PC banner image"
    },
    {
      "id": "sp_image",
      "type": "image_picker",
      "label": "SP Banner image"
    },
    {
      "id": "url",
      "type": "url",
      "label": "Banner URL"
    },
    {
      "id": "Show_title",
      "type": "checkbox",
      "label": "Show banner title"
    },
    {
      "id": "en_title",
      "type": "text",
      "label": "Banner title"
    },
    {
      "id": "en_subtitle",
      "type": "text",
      "label": "Banner sub title"
    },
    {
      "type": "checkbox",
      "id": "is_inverted",
      "label": "Inverted Text Color",
      "info": "背景图片较浅文字看不清时勾选。",
      "default": false
    },
    {
      "type": "select",
      "id": "alignment",
      "label": "Alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    }
  ],
  "presets": [
    {
      "name": "Frontpage Single Banner",
      "category": "Boulies Custom"
    }
  ]
}
{% endschema %}

{% stylesheet %}
  .b5-banner {
    position: relative;
    user-select: none;
    display: flex;
    justify-content: center;
    width: 100%;
    height: 45vw;
    overflow: hidden;
    text-align: center;
  }
  .b5-banner-image {
    height: 100%;
  }
  .bt20-it_content {
    position: absolute;
    top: 50%;
    left: 13%;
    transform: translateY(-50%);
    width: 40vw;
    text-align: left;
  }
  .b5-banner--right .bt20-it_content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%);
    width: 40vw;
  }
  .bt20-it_basicContent {
    color: #fff;
  }
  .bt20-it_secondContent {
    font-size: 0.9375vw;
    max-width: 616px;
    line-height: 1.5;
  }
  .bt20-it_firstContent {
    font-size: 2.92vw;
    text-transform: uppercase;
    font-weight: 700;
    margin-bottom: 1.25vw;
  }
  @media screen and (max-width: 650px) {
    .b5-banner {
      height: auto;
    }
    .b5-banner-image {
      width: 100%;
      height: auto;
    }
    .bt20-it_content,
    .b5-banner--right .bt20-it_content {
      top: unset;
      bottom: -6%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      text-align: center;
      display: block;
    }
    .bt20-it_firstContent {
      font-size: 9.33vw;
      margin-bottom: 4vw;
    }
    .bt20-it_secondContent {
      font-size: 4vw;
      margin: 0 auto;
      /* width: 86.67vw; */
    }
  }
{% endstylesheet %}
