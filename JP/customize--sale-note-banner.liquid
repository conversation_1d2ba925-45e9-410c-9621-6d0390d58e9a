<style>
  :root {
      --sale-note-bar-text-color: {{ section.settings.text_color }};
      --sale-note-bar-scroll-animate: {{ section.settings.scroll_speed }} wordsLoop linear infinite normal;
      --sale-note-bar-scrolling-margin: {{ section.settings.scroll_margin }};
  }
</style>
<a
  href="{{ section.settings.url }}"
  class="b5-sale-note-bar"
  style="color: var(--sale-note-bar-text-color);"
  data-start="{{ settings.promotion_start_date }}"
  data-end="{{ settings.promotion_end_date }}"
>
  <div class="b5-sale-information">
    {% comment %}
      <div class="b5-sale-information__name">
        <span class="b5-sale-note-pc-only">Now on &nbsp;</span><strong>{{ settings.promotion_name }}</strong>
      </div>
      <span class="b5-sale-information__divider"></span>
      <div class="b5-sale-information_discount">
        <div class="b5-sale-scroll-note">
          Up to &nbsp;<strong>{{ settings.promotion_max_discount }} OFF</strong>&nbsp; discount, as low as &nbsp;<strong>
            {{- settings.promotion_min_instalment_price -}}</strong
          >&nbsp; a month with Klarna.
        </div>
      </div>
    {% endcomment %}
    <div class="b5-sale-information__name">{{ settings.promotion_text }}</div>
  </div>
  {% if section.settings.show_checkout %}
    <div class="b5-check-button-like b5-sale-note-pc-only">
      今ぐすチェック
      <svg
        class="b5-check-button-like__arrow"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        version="1.1"
        width="14"
        height="14"
        viewBox="0 0 19.5 16"
      >
        <g><path fill="{{ section.settings.text_color }}" transform="matrix(0,1,-1,0,19.5,-19.5)" d="M26.79289,0.292893C27.183419999999998,-0.0976311,27.816580000000002,-0.0976311,28.20711,0.292893C28.20711,0.292893,35.2071,7.29289,35.2071,7.29289C35.5976,7.68342,35.5976,8.31658,35.2071,8.70711C34.8166,9.09763,34.1834,9.09763,33.7929,8.70711C33.7929,8.70711,28.5,3.41421,28.5,3.41421C28.5,3.41421,28.5,18.5,28.5,18.5C28.5,19.0523,28.05228,19.5,27.5,19.5C26.94772,19.5,26.5,19.0523,26.5,18.5C26.5,18.5,26.5,3.41421,26.5,3.41421C26.5,3.41421,21.20711,8.70711,21.20711,8.70711C20.816580000000002,9.09763,20.183417,9.09763,19.792893,8.70711C19.4023689,8.31658,19.4023689,7.68342,19.792893,7.29289C19.792893,7.29289,26.79289,0.292893,26.79289,0.292893C26.79289,0.292893,26.79289,0.292893,26.79289,0.292893Z"></path></g>
      </svg>
    </div>
  {% endif %}
</a>

{% schema %}
{
  "name": "Sale Note Bar",
  "class": "customize--sale-note-banner",
  "settings": [
    {
      "id": "url",
      "type": "url",
      "label": "Banner URL",
      "default": "/collections/all"
    },
    {
      "id": "show_checkout",
      "type": "checkbox",
      "label": "Show checkout button",
      "default": true
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#336CA8",
      "info": "调整促销信息字体颜色。"
    },
    {
      "id": "scroll_speed",
      "type": "text",
      "label": "Text Scroll Speed on Mobile",
      "default": "15s",
      "info": "调整手机页面下促销信息滚动速度。"
    },
    {
      "id": "scroll_margin",
      "type": "text",
      "label": "Text Scroll Margin on Mobile",
      "default": "-358px",
      "info": "一般情况下不需要动，除非折扣数额超过3位数。"
    }
  ],
  "presets": [
    {
      "name": "Sale Note Bar",
      "category": "Boulies Custom"
    }
  ]
}
{% endschema %}

{% stylesheet %}
  a.b5-sale-note-bar {
    width: 100%;
    overflow: hidden;
    display: none;
    text-align: center;
    text-decoration: none;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 700;
    padding: 12px 0;
  }
  .b5-sale-information {
    display: flex;
    align-items: center;
  }
  .b5-check-button-like {
    display: flex;
    align-items: center;
    margin-left: 24px;
    font-weight: bold;
  }
  .b5-check-button-like .b5-check-button-like__arrow {
    position: relative;
    left: 10px;
    transition: left 300ms ease-in-out;
  }
  a.b5-sale-note-bar:hover .b5-check-button-like__arrow {
    left: 15px;
  }
  span.b5-sale-information__divider {
    width: 1px;
    height: 24px;
    display: inline-block;
    background: #d6d6d6;
    margin: 0 10px;
  }

  @keyframes wordsLoop {
    0% {
      left: 100%;
    }
    100% {
      left: var(--sale-note-bar-scrolling-margin);
    }
  }
  @-webkit-keyframes wordsLoop {
    0% {
      left: 100%;
    }
    100% {
      left: var(--sale-note-bar-scrolling-margin);
    }
  }

  @media screen and (max-width: 650px) {
    .b5-sale-note-pc-only {
      display: none;
    }

    .b5-sale-information {
      width: 100%;
    }
    .b5-sale-information_discount {
      display: inline-block;
      flex: 1 1 auto;
      overflow: hidden;
    }
    .b5-sale-scroll-note {
      width: max-content;
      position: relative;
      animation: var(--sale-note-bar-scroll-animate);
      white-space: nowrap;
    }

    a.b5-sale-note-bar {
      justify-content: flex-start;
      font-size: 3.33vw;
      padding: 3.33vw 0;
    }
    .b5-sale-information__name {
      margin: 0 auto;
      flex: 0 0 max-content;
      width: max-content;
      white-space: nowrap;
    }

    span.b5-sale-information__divider {
      margin-left: 0;
      margin-right: 4vw;
    }
  }
{% endstylesheet %}

{% javascript %}
  var note_bar = document.querySelector('.b5-sale-note-bar');
  var note_bar_startDate = new Date(note_bar.dataset.start).getTime();
  var note_bar_endDate = new Date(note_bar.dataset.end).getTime();
  var note_bar_localDate = new Date().getTime();
  if (note_bar_endDate > note_bar_localDate || note_bar_startDate < note_bar_localDate) {
    note_bar.style.display = 'flex';
  }
{% endjavascript %}
