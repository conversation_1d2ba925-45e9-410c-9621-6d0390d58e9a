<style>
    .assembly-video-wrapper {
        max-width: 1100px;
        margin: 128px auto 96px;
        display: flex;
        flex-direction: column;
        gap: 96px;
    }

    .assembly-video-title {
        font-size: 24px;
        font-weight: 700;
        color: 141414;
        margin-bottom: 32px;
    }

    /* 响应式视频封面图片 */
    .assembly-video-item video {
        border-radius: 8px;
    }

    /* 桌面端 */
    @media (min-width: 768px) {
        .assembly-video-item video[data-poster-desktop] {
            object-fit: cover;
        }
    }

    /* 移动端 */
    @media (max-width: 767px) {
        .assembly-video-wrapper {
            gap: 48px;
        }

        .assembly-video-title {
            font-size: 20px;
            margin-bottom: 24px;
        }
    }
</style>

<div class="bouliesPageTitle black">
    <div
        class="bouliesPageTitleBackgroundImage"
        style="background-image: url('https://cdn.shopify.com/s/files/1/0648/5157/4996/files/assembly_video.jpg?v=1751617665')"></div>
    <h1 class="bouliesPageTitle_heading">組立動画</h1>
</div>
<div class="wrapper">
    <div class="bouliesPageContent">
        <div class="assembly-video-wrapper">
            <div class="assembly-video-item">
                <div class="assembly-video-title">組み立て方法（動画）Master・Master Rex共通</div>
                <video
                    controls
                    width="100%"
                    height="auto"
                    preload="metadata"
                    muted
                    poster="https://cdn.shopify.com/s/files/1/0648/5157/4996/files/master_assembly_video_poster.jpg?v=1751617665">
                    <source
                        src="https://cdn.shopify.com/videos/c/o/v/4e1c3c9205e24659986bbe3ad5f3b8aa.mp4"
                        type="video/mp4" />
                    <p>お使いのブラウザは動画再生に対応していません。</p>
                </video>
            </div>

            <div class="assembly-video-item">
                <div class="assembly-video-title">組み立て方法（動画）NUBI</div>
                <video
                    controls
                    width="100%"
                    height="auto"
                    preload="metadata"
                    muted
                    poster="https://cdn.shopify.com/s/files/1/0648/5157/4996/files/NUBI_assembly_video_poster.jpg?v=1751617665">
                    <source
                        src="https://cdn.shopify.com/videos/c/o/v/e95906ac224949bfa5a4afcc4b357b85.mp4"
                        type="video/mp4" />
                    <p>お使いのブラウザは動画再生に対応していません。</p>
                </video>
            </div>

            <div class="assembly-video-item">
                <div class="assembly-video-title">組み立て方法（動画）OP180</div>
                <video
                    controls
                    width="100%"
                    height="auto"
                    preload="metadata"
                    muted
                    poster="https://cdn.shopify.com/s/files/1/0648/5157/4996/files/OP180_assembly_video_poster.jpg?v=1751617665">
                    <source
                        src="https://cdn.shopify.com/videos/c/o/v/6475e23fe64b4d1e8287aec8ecf44018.mp4"
                        type="video/mp4" />
                    <p>お使いのブラウザは動画再生に対応していません。</p>
                </video>
            </div>
        </div>
    </div>
</div>

<script>
// 响应式视频封面图片切换
function updateVideoPoster() {
    const videos = document.querySelectorAll('.assembly-video-item video');
    const isMobile = window.innerWidth <= 767;

    videos.forEach((video, index) => {
        const posterUrls = {
            0: { // Master
                desktop: 'https://cdn.shopify.com/s/files/1/0648/5157/4996/files/master_assembly_video_poster.jpg?v=1751617665',
                mobile: 'https://cdn.shopify.com/s/files/1/0648/5157/4996/files/master_assembly_video_poster_mobile.jpg?v=1751617665'
            },
            1: { // NUBI
                desktop: 'https://cdn.shopify.com/s/files/1/0648/5157/4996/files/NUBI_assembly_video_poster.jpg?v=1751617665',
                mobile: 'https://cdn.shopify.com/s/files/1/0648/5157/4996/files/NUBI_assembly_video_poster_mobile.jpg?v=1751617665'
            },
            2: { // OP180
                desktop: 'https://cdn.shopify.com/s/files/1/0648/5157/4996/files/OP180_assembly_video_poster.jpg?v=1751617665',
                mobile: 'https://cdn.shopify.com/s/files/1/0648/5157/4996/files/OP180_assembly_video_poster_mobile.jpg?v=1751617665'
            }
        };

        if (posterUrls[index]) {
            video.poster = isMobile ? posterUrls[index].mobile : posterUrls[index].desktop;
        }
    });
}

// 页面加载时执行
document.addEventListener('DOMContentLoaded', updateVideoPoster);

// 窗口大小改变时执行
window.addEventListener('resize', updateVideoPoster);
</script>
