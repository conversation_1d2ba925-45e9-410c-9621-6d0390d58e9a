{{ 'semantic-components.css' | asset_url | stylesheet_tag }}
{{ 'semantic-components.js' | asset_url | script_tag }}
{{ 'contact.css' | asset_url | stylesheet_tag }}
<div class="bouliesPageTitle black">
  <div
    class="bouliesPageTitleBackgroundImage"
    style="background-image:url(//cdn.shopify.com/s/files/1/2127/6275/files/contactbackground.jpg?14177718788931323076) "
  ></div>
  <h1 class="bouliesPageTitle_heading">お問い合わせとよくある質問</h1>
  <p class="bouliesPageTitle_intro">
    FAQ等の情報にて製品の確認を行っても改善されない場合には、サポートセンターまでお問合せください。
  </p>
  <div class="bouliesPageTitle_content">
    <div class="ui fluid search" style="max-width: 560px; margin: 0 auto;">
      <div class="fluid ui icon input">
        <input class="prompt" type="text" placeholder="Search question...">
        <i class="icon iconFont icon-search"></i>
      </div>
      <div class="results"></div>
    </div>
  </div>
</div>
<div class="wrapper">
  {% form 'contact' %}
    <div class="contact_form ui form {% if form.posted_successfully? %}success{% endif %} {% comment %}{% if form.errors %}error{% endif %}{% endcomment %}">
      <div class="contact_message">
        <div class="ui success message" style="margin: 80px auto !important;">
          <div class="header">Send Completed!</div>
          <p>{{ 'contact.form.post_success' | t }}</p>
        </div>
        {% if form.errors %}
          <div class="ui error message">
            <div class="header">Error occurred!</div>
            {{ form.errors | default_errors }}
          </div>
        {% endif %}
      </div>
      <input type="hidden" name="form_type" value="contact">
      <input type="hidden" name="utf8" value="✓">
      <div
        class="contact_section"
        {% if form.posted_successfully? %}
          style="display:none"
        {% endif %}
      >
        <p class="contact_enquiryTitle">解決する問題を選択してください</p>
        <div class="field required  contactForm-enquiryType">
          <div class="ui selection dropdown" style="background: rgba(0,0,0,.03);">
            <input type="hidden" class="formValue" name="contact[enquiry-type]" id="enquiryType" autocomplete="off">
            <i class="dropdown icon"></i>
            <div class="default text">解決する問題を選択してください</div>
            <div class="menu">
              <div class="item" data-value="Product">購入前のお問い合わせ</div>
              <div class="item" data-value="Bulk Order">まとめ買いサービスについて</div>
              <div class="item" data-value="Order">注文/配送サービス</div>
              <div class="item" data-value="Warranty">商品の不具合</div>
              <div class="item" data-value="Business">スポンサードのご相談</div>
              <div class="item" data-value="General">その他</div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="contact_section hiddenForm contact_additionalInfo"
        {% if form.posted_successfully? %}
          style="display:none"
        {% endif %}
      >
        <div class="contact_moreInfo">
          <div class="two fields">
            <div class="field required contactForm-cusotmerName">
              <label for="contact[name]">お名前</label>
              <input type="text" class="formValue" name="contact[name]" placeholder="" autocapitalize="words" value="">
            </div>
            <div class="field required contactForm-cusotmerEmail">
              <label for="contact[email]">メールアドレス</label>
              <input
                type="email"
                class="formValue"
                name="contact[email]"
                placeholder=""
                autocorrect="off"
                autocapitalize="off"
                value=""
              >
            </div>
          </div>
          <div class="field required disabled contactForm-cusotmerCountry">
            <label for="contact[country]">Which country are you in?</label>
            <div class="ui fluid search selection dropdown">
              <input type="hidden" class="formValue" name="contact[country]" autocomplete="off" disabled>
              <i class="dropdown icon"></i>
              <div class="default text">select country</div>
              <div class="menu">
                <div class="header">
                  <i class="tags icon"></i>
                  Most Used
                </div>
                <div class="item" data-value="Australia">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/au.svg">Australia
                </div>
                <div class="item" data-value="Canada">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/ca.svg">Canada
                </div>
                <div class="item" data-value="France">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/fr.svg">France
                </div>
                <div class="item" data-value="Germany">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/de.svg">Germany
                </div>
                <div class="item" data-value="Ireland">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/ie.svg">Ireland
                </div>
                <div class="item" data-value="Italy">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/it.svg">Italy
                </div>
                <div class="item" data-value="Japan">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/jp.svg">Japan
                </div>
                <div class="item" data-value="Spain">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/es.svg">Spain
                </div>
                <div class="item" data-value="Saudi Arabia">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/sa.svg">Saudi Arabia
                </div>
                <div class="item" data-value="United Kingdom">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/gb.svg">United
                  Kingdom
                </div>
                <div class="item" data-value="United States">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/us.svg">United
                  States
                </div>
                <div class="divider"></div>
                <div class="item" data-value="Argentina">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/ar.svg">Argentina
                </div>
                <div class="item" data-value="Austria">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/at.svg">Austria
                </div>
                <div class="item" data-value="Belgium">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/be.svg">Belgium
                </div>
                <div class="item" data-value="Brazil">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/br.svg">Brazil
                </div>
                <div class="item" data-value="Croatia">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/hr.svg">Croatia
                </div>
                <div class="item" data-value="Czech Republic">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/cz.svg">Czech
                  Republic
                </div>
                <div class="item" data-value="Egypt">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/eg.svg">Egypt
                </div>
                <div class="item" data-value="Denmark">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/dk.svg">Denmark
                </div>
                <div class="item" data-value="Finland">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/fi.svg">Finland
                </div>
                <div class="item" data-value="India">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/in.svg">India
                </div>
                <div class="item" data-value="Indonesia">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/id.svg">Indonesia
                </div>
                <div class="item" data-value="Iceland">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/is.svg">Iceland
                </div>
                <div class="item" data-value="Luxembourg">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/lu.svg">Luxembourg
                </div>
                <div class="item" data-value="Malaysia">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/my.svg">Malaysia
                </div>
                <div class="item" data-value="Mexico">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/mx.svg">Mexico
                </div>
                <div class="item" data-value="Monaco">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/mc.svg">Monaco
                </div>
                <div class="item" data-value="Netherlands">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/nl.svg">Netherlands
                </div>
                <div class="item" data-value="New Zealand">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/nz.svg">New Zealand
                </div>
                <div class="item" data-value="Norway">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/no.svg">Norway
                </div>
                <div class="item" data-value="Philippines">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/ph.svg">Philippines
                </div>
                <div class="item" data-value="Poland">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/pl.svg">Poland
                </div>
                <div class="item" data-value="Portugal">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/pt.svg">Portugal
                </div>
                <div class="item" data-value="Russia">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/ru.svg">Russia
                </div>
                <div class="item" data-value="Singapore">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/sg.svg">Singapore
                </div>
                <div class="item" data-value="Slovakia">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/sk.svg">Slovakia
                </div>
                <div class="item" data-value="South Korea">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/kr.svg">South Korea
                </div>
                <div class="item" data-value="Sweden">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/se.svg">Sweden
                </div>
                <div class="item" data-value="Switzerland">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/ch.svg">Switzerland
                </div>
                <div class="item" data-value="Thailand">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/th.svg">Thailand
                </div>
                <div class="item" data-value="Ukraine">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/ua.svg">Ukraine
                </div>
                <div class="item" data-value="Vietnam">
                  <img class="support-region-flag" src="https://cdn.shopify.com/static/images/flags/vn.svg">Vietnam
                </div>
                <div class="item" data-value="Other Country">Other Country</div>
              </div>
            </div>
          </div>
          <div class="field required disabled contactForm-product">
            <label for="contact[product]">商品モデル</label>
            <div class="ui fluid search selection dropdown">
              <input type="hidden" class="formValue" name="contact[product]" autocomplete="off" disabled>
              <i class="dropdown icon"></i>
              <div class="default text">商品モデルを選ぶ</div>
              <div class="menu">
                <div class="header">Boulies Chair</div>
                <div class="item" data-value="master">Master</div>
                <div class="item" data-value="master-rex">Master Rex</div>
                <div class="item" data-value="nubi">Nubi</div>
                <div class="item" data-value="op180">OP180</div>
              </div>
            </div>
          </div>
          <div class="field required disabled contactForm-purchaseChannel">
            <label for="contact[purchase-channel]">Where did you buy this item?</label>
            <div class="ui fluid selection dropdown">
              <input type="hidden" class="formValue" name="contact[purchase-channel]" autocomplete="off" disabled>
              <i class="dropdown icon"></i>
              <div class="default text">select purchase channel</div>
              <div class="menu">
                <div class="item" data-value="Boulies">Boulies Official (boulies.co.uk)</div>
                <div class="item" data-value="Amazon">Amazon</div>
                <div class="item" data-value="eBay">eBay</div>
                <div class="item" data-value="Other Channel">Other</div>
              </div>
            </div>
          </div>
          <div class="field required disabled contactForm-orderId">
            <label for="contact[order-id]">What is your order id / number?</label>
            <input
              type="text"
              class="formValue"
              name="contact[order-id]"
              placeholder="enter NONE if there is no order number"
              disabled
            >
          </div>
          <div class="field required contactForm-message">
            <label for="contact[message]">お問い合わせ内容</label>
            <textarea
              rows="6"
              type="text"
              class="formValue"
              name="contact[message]"
              placeholder="直販サイトからのご注文に関するお問い合わせには、必ず「ご注文番号」をご記入くださいますようお願いいたします。"
              value=""
            ></textarea>
          </div>
          <div class="field contactForm-files noneUploadContent uploadComponentNotReady">
            <label for="contact[files]">Send images or files to us (Optional)</label>
            <input class="formValue" name="contact[files]" type="hidden" value="" disabled>
            <div class="contact_uploadContainer">
              <div class="contact_uploadArea" id="contact_uploadArea">
                <span id="contact_uploadBtn">Click to browse or drop files here</span>
              </div>
              <div class="contact_uploadContentWraper">
                <div class="contact_uploadContentTitle">upload contents</div>
                <div class="contact_noFiles contact_componentNotReady">
                  Please finish the required form before uploading files.
                </div>
                <div class="contact_noFiles">
                  Upload related images, videos or files could let us help you more effectively.
                </div>
                <div class="contact_uploadContentList"></div>
              </div>
            </div>
          </div>
          <div style="font-size: 12px;text-align: center; margin-top: -1em;">
            <span style="color:#db2828;">*</span> These fields are mandatory
          </div>
        </div>
      </div>
      <div style="text-align:center;margin: -2em auto 6em;" class="contactForm-submit">
        <button
          type="submit"
          class="bouliesBTN bouliesBTN_normal bouliesBTN_disabled contact_submit"
          style="display:none;"
          disabled
        >
          Send Message
        </button>
        <a href="/pages/support-and-contact" class="bouliesBTN bouliesBTN_normal contact_sendOther"
          >Send Another Message</a
        >
      </div>
    </div>
  {% endform %}
  <hr style="width:90%;max-width: 1000px;border-top: 1px solid #000;border-bottom: none;margin-bottom: 80px;">
  <div class="bouliesPageContent">
    <div class="ui header" id="boulies32-faq">
      <div class="content">よくある質問</div>
    </div>
    <div class="ui fluid accordion">
      <h4 class="ui header">製品仕様について</h4>
      <div class="title" id="bouliesFAQ-1">
        <i class="dropdown icon"></i>Bouliesのチェアの各シリーズ、どんな違いがありますか？
      </div>
      <div class="content">
        <p>
          Master Series<br>
          家庭やオフィスでの使用に適した高級感な多機能チェアです。厳密なテストとたゆまぬ改良を重ねた結果、当社の自信作『Boulies
          Ultraflex PU』レザーを採用されました。『Boulies Ultraflex
          PU』はブリティッシュ貴族風レザーを採用し高級感を保つ時に手触りもスムーズに感じされます。臭い匂いがしなくて、耐久性も高いです。背もたれと座敷には高品質スポンジを使用し通気性が高くなり、夏でも快適に使用できます。本体はフルメタルフレームを採用し、ガスシリンダーもSGS認証Class
          4にアップデートし、そして炭化加工させ、業界最高規格のシリンダーとも言えます。
        </p>
        <p>
          Master Rex Series<br>
          バターのような滑らかな肌触りが、ゲーミングから仕事まで、あらゆるシーンで理想的な座り心地を提供します。背もたれをリクライニング可能、多様化する働き方と着座姿勢に合わせて、簡単なレバー操作で調節できます。静音PUキャスターを五つ付き、自由移動できます。座面の下に収納されているオットマンを引き出すことで簡単に休憩できます。リクライニングするとアームレストも同時に動いてくれるため、どんな角度でも肘を置き、リラックスすることができます。さらに揺りかごのようなロッキング機能を搭載し、優れた安定性と耐久性で、細かい部分まで妥協せず、更に品質向上と理想の商品を追求しています。
        </p>
        <p>
          OP180 Series<br>
          夏も涼しい！季節を問わず、1年中爽快な使い心地を味わえます。独創的なハイパフォーマンスカーのようなBoulies UNO™
          メッシュ素材を採用します。素材の品質にこだわることで、耐久性や快適性を追求しています。汗をかきやすい方や暑がりの方にお勧めです。可動式3Dアームレストでギターなど楽器演奏やデスクの下にチェアを収納時に便利。価格を抑えながらも、高性能・高品質ワークチェアのカテゴリーに属し、申し分のない座り心地と癒し×集中空間をお届けします。
        </p>
        <p>
          NUBI Series<br>
          日本人の体格や使用環境を分析し、オリジナルのゲーミングチェアです。省スペースでシンプルなデザインで背もたれは脊椎の自然なカーブに沿っており、最適なサポートを提供し、腰への負担を軽減します。学生の勉強椅子や学習椅子として、社会人の仕事や在宅ワーク用のオフィスチェアとしても最適のPCチェアです。シックで落ち着いたブラック調と洗練を尽くした造形が、皆様の使用する日常シーンに対して溶け込むように馴染みます。
        </p>
      </div>

      <div class="title" id="bouliesFAQ-2">
        <i class="dropdown icon"></i>Bouliesのチェア、自分に合いそうかどうか知りたいんだけど
      </div>
      <div class="content">
        <p>
          当社のチェアは、お客様の体重や身長の個人差を考慮し、最大限の調整機能を充実させた設計となっております。確認できない場合は、各商品詳細ページに記載の商品サイズ表をご参考になれば幸いです。
        </p>
      </div>

      <div class="title" id="bouliesFAQ-3"><i class="dropdown icon"></i>腰痛に効きますか?腰痛を予防できますか?</div>
      <div class="content">
        <p>
          本品は医療機器ではあリませんので、お答えいたしかれます。あくまで姿勢を正しく保つための商品です。ただし、良い姿勢は腰の負担を軽減させますので結果的に腰が支えられていることは考えられます。ただ効果を実感いただくまでの時間には、使用環境等を含めて個人差があリますので、一概には回答できかねます。また腰部へル二アや坐骨神経痛など腰や背中に疾患のある方は必ず医師にご相談の上使用してください。
        </p>
      </div>

      <div class="title" id="bouliesFAQ-4"><i class="dropdown icon"></i>どれくらい使えば効果かあリますか?</div>
      <div class="content">
        <p>
          本品は医療機器ではごさいませんので効果効能はお答えいたしかねます。<br>
          ただし、良い姿勢は腰の負担を軽減させますので結果的に腰が支えられていることは考えられます。効果を実感いただくまでの時間には、使用環境等を含めて個人差があリますので、-概には回答できかねます。继続してご使用してみてください。また、使いはじめは15分を目安に短い時間からお試しください。
        </p>
      </div>

      <h4 class="ui header">椅子故障について</h4>
      <p>
        統計によると、椅子故障の原因にその45%はインストールの誤りです。詳しくはビデオに従って、インストール手順をご確認してください。
      </p>
      <div class="title" id="bouliesFAQ-5">
        <i class="dropdown icon"></i>新品状態で一定の期間にご利用頂けると、ギシギシと音が出てますが、なぜですか?
      </div>
      <div class="content">
        <p>
          シリンダー固定台は新品状態で座部と摺り合わせ期間があります為ギシギシと音がでたら、シリンダー固定台を座部から完全に取り外し、すべてのネジがしっかり締められているよう改めて組み立てる必要ごさいます。
        </p>
      </div>

      <div class="title" id="bouliesFAQ-6">
        <i class="dropdown icon"></i>背もたれが手に負えないほど揺れて、固定できません。
      </div>
      <div class="content">
        <p>
          背もたれの両側にある4本のネジが締められていることを確認してください。締めてもまだ揺れている場合は、それに関するビデオをアフターセールスメールボックスに送信してください。弊社の技術サポートが問題を分析いたします。
        </p>
      </div>

      <div class="title" id="bouliesFAQ-7">
        <i class="dropdown icon"></i>椅子がリクライニングできず、リクライニング機能が機能しません。
      </div>
      <div class="content">
        <p>
          初めて使用するときは滑らかにならない場合がありますので、調整レバーをしっかりと引いてください。そうしでも効かない場合は弊社のアフターセールスメールボックスに送信してください。弊社の技術サポートが問題を分析いたします。
        </p>
      </div>

      <div class="title" id="bouliesFAQ-8"><i class="dropdown icon"></i>椅子は上がり降りできません。</div>
      <div class="content">
        <p>
          リフト機能を有効するには、ガスシリンダーとチルト構造ベースのぴったり合うことが必要です。ガスシリンダーとチルト構造ベースを分離してから再度取り付けてください。そして椅子に座って振ってガスシリンダーとチルト構造ベースが確実に組み合わされているようにしてください。そうしでも効かない場合は問題に関するビデオをアフターセールスメールボックスに送信してください。弊社の技術サポートが問題を分析いたします。
        </p>
      </div>

      <div class="title" id="bouliesFAQ-9">
        <i class="dropdown icon"></i>椅子が前に傾いているので、椅子から簡単に滑り落ちます。
      </div>
      <div class="content">
        <p>
          チルト構造ベースがの取り付け方向を確認してください。チルト構造ベースがは後方に傾く機能があり、逆に取り付けると前方に傾くようになります。
          調整ハンドルは通常右側にあります。
        </p>
      </div>

      <div class="title" id="bouliesFAQ-10">
        <i class="dropdown icon"></i>フットレストが挿入できません、または引き出せません。
      </div>
      <div class="content">
        <p>フットレストの出し入れ力を調整できます。 詳しくは、フットレストの取り付け手順をご覧ください。</p>
      </div>

      <div class="title" id="bouliesFAQ-11"><i class="dropdown icon"></i>椅子のサイドカバーが取り付けられません。</div>
      <div class="content">
        <p>写真/ビデオに従ってもう一度取り付けてください。</p>
      </div>

      <div class="title" id="bouliesFAQ-12">
        <i class="dropdown icon"></i>チェアのアクセサリーはどこで購入できますでしょうか？
      </div>
      <div class="content">
        <p>
          ご注文日から12ヶ月の間（注）、お買い上げ頂いた対象製品に製造不良により破損などが認められた場合、2年無償部品交換保証です。その際の送料は弊社が負担いたします。
        </p>
        <p>どうかご安心ください。何か問題がございましたら、ご遠慮なくこちらにご連絡ください。</p>
        <p>
          ただし、在庫切れ、販売終了等、弊社の都合で同一新品製品が調達できなかった場合、同等品の後継品に交換させていただくか、もしくは、システム上で製品代金を返金させていただく等の対応となる場合がございますので、あらかじめご了承ください。
        </p>
        <p>※なお、お客様過失による不具合の場合は、本保証の対象外とさせていただきます。</p>
        <p>※異音のある場合は、チルト構造ベースネジを緩めてからしっかりと締め直してください。</p>
      </div>
    </div>
  </div>

  <p class="contact_noticeInfo">
    最後に<br>
    カスタマーサポートにお問い合わせの際、問題箇所の写真とご注文番号を事前にご用意ください。問題点とご注文番号をお伝えいただければ、迅速に問題解決させて頂きます。
  </p>
  <p class="contact_otherInfo">
    保証対応につきまして、弊社の方針が変更される場合、当該ページも随時更新させていただきます。弊社の製品をご購入されたお客様におかれましては、随時弊社の返品ポリシーに関するページにアクセスいただき、最新の情報をご確認くださいますようお願い申し上げます。
  </p>
</div>

{{ 'contact.js' | asset_url | script_tag }}
<script>
  $('.ui.accordion').accordion();
  var localSearchContent = [
    {
      category: 'FAQ',
      title: 'Bouliesのチェアの各シリーズ、どんな違いがありますか',
      url: '#bouliesFAQ-1',
    },
    {
      category: 'FAQ',
      title: 'Bouliesのチェア、自分に合いそうかどうか知りたいんだけど',
      url: '#bouliesFAQ-2',
    },
    {
      category: 'FAQ',
      title: '腰痛に効きますか?腰痛を予防できますか',
      url: '#bouliesFAQ-3',
    },
    {
      category: 'FAQ',
      title: 'どれくらい使えば効果かあリますか',
      url: '#bouliesFAQ-4',
    },
    {
      category: 'FAQ',
      title: '新品状態で一定の期間にご利用頂けると、ギシギシと音が出てますが、なぜですか',
      url: '#bouliesFAQ-5',
    },
    {
      category: 'FAQ',
      title: '背もたれが手に負えないほど揺れて、固定できません',
      url: '#bouliesFAQ-6',
    },
    {
      category: 'FAQ',
      title: '椅子がリクライニングできず、リクライニング機能が機能しません',
      url: '#bouliesFAQ-7',
    },
    {
      category: 'FAQ',
      title: '椅子は上がり降りできません',
      url: '#bouliesFAQ-8',
    },
    {
      category: 'FAQ',
      title: '椅子が前に傾いているので、椅子から簡単に滑り落ちます',
      url: '#bouliesFAQ-9',
    },
    {
      category: 'FAQ',
      title: 'フットレストが挿入できません、または引き出せません',
      url: '#bouliesFAQ-10',
    },
    {
      category: 'FAQ',
      title: '椅子のサイドカバーが取り付けられません',
      url: '#bouliesFAQ-11',
    },
    {
      category: 'FAQ',
      title: 'チェアのアクセサリーはどこで購入できますでしょうか',
      url: '#bouliesFAQ-12',
    },
  ];
  $('.ui.search').search({
    type: 'category',
    source: localSearchContent,
    fullTextSearch: true,
    fields: {
      actionURL: 'url',
      action: 'action',
    },
    onSelect: function (result) {
      if (result.category == 'FAQ') {
        $('.ui.accordion').accordion('open', result.url);
      }
    },
  });
</script>
