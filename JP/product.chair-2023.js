var collectionGalleryList = $("#bt32-csb_pswpContainer").slick({
    slidesToShow: 3,
    slidesToScroll: 1,
    asNavFor: "#bt32-csb_mainImage",
    dots: false,
    arrows: false,
    centerMode: true,
    focusOnSelect: true,
    vertical: true,
    verticalSwiping: true,
    speed: 500,
    responsive: [
        {
            breakpoint: 1280,
            settings: {
                vertical: false,
                verticalSwiping: false,
            },
        },
    ],
});

var collectionGallery = $("#bt32-csb_mainImage").slick({
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    asNavFor: "#bt32-csb_pswpContainer",
    speed: 500,
});

var dynamicGallery = lightGallery(document.body, {
    licenseKey: "DA7E83CA-6E474542-97592C9F-8A8DFF9D",
    dynamic: true,
    controls: false,
    download: false,
    plugins: [lgZoom],
    dynamicEl: [],
});

/* ---  product functions --- */
var productInter = {
    addToCart: function (target) {
        if (target.disabled == true) return false;
        var chairData = new FormData(document.querySelector('form[action="/cart/add"]'));
        // create main item data
        var itemsData = {
            items: [
                {
                    id: chairData.get("id"),
                    quantity: chairData.get("quantity"),
                    properties: {
                        warranty: chairData.get("properties[warranty]"),
                        accessories: chairData.get("properties[accessories]"),
                    },
                },
            ],
        };

        if (chairData.get("properties[Estimated_to_ship_out]")) {
            itemsData.items[0].properties["Estimated_to_ship_out"] = chairData.get("properties[Estimated_to_ship_out]");
        }

        // set cart discount attributes
        var attributesData = {
            attributes: {
                cart_discount_code: "",
                cart_discount_amount: "",
            },
        };
        if (document.getElementById("bt34-mainPromotion").style["display"] == "block") {
            attributesData.attributes["cart_discount_code"] = chairData.get("attributes[cart_discount_code]");
            attributesData.attributes["cart_discount_amount"] = chairData.get("attributes[cart_discount_amount]");
        }

        // set addon item data
        document.querySelectorAll(".bt32-activedAddonItem.bt32-addonItem").forEach(function (ele) {
            itemsData.items.push({
                id: ele.querySelector("input[name=id]").value,
                quantity: 1,
            });
        });

        $.ajax({
            type: "POST",
            dataType: "JSON",
            url: "/cart/add.js",
            data: itemsData,
            timeout: 5000,
            success: function () {
                // update cart attributes
                $.post(
                    "/cart/update.js",
                    attributesData,
                    function (cartData) {
                        ajaxcart.updateCartItem(cartData);
                        target.classList.remove("bt20-csb_addingItem");
                        target.disabled == false;
                        ajaxcart.openCart();
                    },
                    "json"
                );
                // rdt('track', 'AddToCart');
                var current_option = document.querySelector('input[value="' + chairData.get("id") + '"]');
            },
            error: function (err) {
                alert(err.responseJSON.description);
                target.classList.remove("bt20-csb_addingItem");
                target.disabled == false;
            },
            beforeSend: function () {
                target.classList.add("bt20-csb_addingItem");
                target.disabled == true;
            },
        });
    },
    updatePreOrderInfo: function (data, quantity) {
        var preOrderInfoWrap = document.querySelector(".bt20-csb_preOrderWrap");
        var tempDOM = "";
        for (var i = 0; i < data.length - 1; i++) {
            tempDOM +=
                '<div class="bt20-csb_preOrderWaves"><div class="bt20-csb_preOrderInfo"><span class="bt20-csb_wave">' +
                (i + 1) +
                '</span><span class="bt20-csb_etsDate">' +
                data[i].ets +
                "</span></div></div>";
        }
        tempDOM +=
            '<div class="bt20-csb_preOrderWaves bt20-csb_currentWave"><div class="bt20-csb_preOrderInfo"><span class="bt20-csb_wave">' +
            data.length +
            '</span><span class="bt20-csb_etsDate">' +
            data[data.length - 1].ets +
            '</span></div><div class="bt20-csb_preOrderedStatus"><div class="bt20-csb_preOrderedDone" id="bt36-csb_preOrderProgress"></div></div></div>';
        preOrderInfoWrap.innerHTML = tempDOM;
        var orderStatus = Math.round(((data[data.length - 1].total_number - parseInt(quantity)) / data[data.length - 1].total_number) * 100) + "%";
        setTimeout(function () {
            document.getElementById("bt36-csb_preOrderProgress").innerHTML = orderStatus;
            document.getElementById("bt36-csb_preOrderProgress").style.width = orderStatus;
        }, 1000);
    },
    changeToThisOption: function (current_variant, stopPopstate, stopKlarnaReflash) {
        var cart = document.querySelector(".bt20-csb_cart"),
            notifymeContainer = document.querySelector(".bt32-notifymeContainer"),
            cartBtn = document.getElementById("bt20-csb_cartBtn"),
            inventory = document.querySelector(".bt20-csb_inventory");

        //update product gallery and gallery list
        var slickNumbers = collectionGallery.slick("getSlick").slideCount;
        for (var i = 0; i < slickNumbers; i++) {
            collectionGallery.slick("slickRemove", 0);
            collectionGalleryList.slick("slickRemove", 0);
        }
        var this_option_images = document.querySelectorAll(".image_storage_" + current_variant.id);
        for (var i = 0; i < this_option_images.length; i++) {
            var resizeImage = Boulies.imageResize(this_option_images[i].value, 900, "heightOnly");
            collectionGallery.slick(
                "slickAdd",
                '<a class="pswp_image" href="' +
                    this_option_images[i].value +
                    '" data-lg-size="600-900" target="_blank"><img class="bt32-collectionGalleryItem" src="' +
                    resizeImage +
                    '"></a>'
            );
            collectionGalleryList.slick("slickAdd", '<div><img src="' + resizeImage + '"></div>');
        }

        // update url history
        if (!stopPopstate) {
            var current_search = Boulies.getUrlkey() ? Boulies.getUrlkey() : {},
                _search = "?";
            current_search["variant"] = current_variant.value;
            for (var i in current_search) {
                _search += i + "=" + current_search[i] + "&";
            }
            history.pushState({}, "", _search.substr(0, _search.length - 1));
        }

        // console.log('current_variant:',current_variant)

        if (current_variant.dataset.variantMetadata == "null") return alert("Product Metadata Not Set!");
        var current_variant_metadata = JSON.parse(current_variant.dataset.variantMetadata);
        // update properties
        document.getElementById("itemProperties_warranty").value = current_variant_metadata.warranty;
        document.getElementById("itemProperties_accessories").value = current_variant_metadata.accessories;
        if (current_variant_metadata.pre_order.status == true) {
            document.getElementById("itemProperties_preOrderDescription").disabled = false;
            document.getElementById("itemProperties_preOrderDescription").value =
                current_variant_metadata.pre_order.waves[current_variant_metadata.pre_order.waves.length - 1].ets;
        } else {
            document.getElementById("itemProperties_preOrderDescription").disabled = true;
        }

        // Low inventory notifycation
        var _lin = document.getElementById("lowInventoryNotify");
        if (parseInt(current_variant.dataset.quantity) > parseInt(_lin.dataset.linTrigger) || _lin.dataset.lin == "disable") {
            _lin.innerHTML = "In stock";
        } else {
            if (_lin.dataset.lin == "text") {
                _lin.innerHTML = "Limited stock available";
            } else {
                _lin.innerHTML = "Only <b>" + current_variant.dataset.quantity + "</b> in stock";
            }
        }

        // update button
        if (current_variant.dataset.available == "true") {
            cart.style.display = "block";
            notifymeContainer.style.display = "none";
            if (current_variant_metadata.pre_order.status == true) {
                cart.classList.add("bt20-csb_proOrdering");
                if (current_variant.dataset.available == "true") {
                    cartBtn.classList.remove("bt20-csb_activeCartBtn", "bt20-csb_addingItem", "bt32-csb_preorderClosed");
                    cartBtn.classList.add("bt20-csb_preorderCartBtn");
                    cartBtn.disabled = false;
                    inventory.classList.remove("bt20-csb_inStock", "bt20-csb_outOfStock");
                } else {
                    cartBtn.classList.remove("bt20-csb_addingItem", "bt20-csb_activeCartBtn");
                    cartBtn.classList.add("bt20-csb_preorderCartBtn", "bt32-csb_preorderClosed");
                    cartBtn.disabled = true;
                    inventory.classList.remove("bt20-csb_inStock");
                    inventory.classList.add("bt20-csb_outOfStock");
                }
                productInter.updatePreOrderInfo(current_variant_metadata.pre_order.waves, current_variant.dataset.quantity);
            } else {
                cartBtn.classList.remove("bt20-csb_preorderCartBtn", "bt20-csb_addingItem", "bt32-csb_preorderClosed");
                cartBtn.classList.add("bt20-csb_activeCartBtn");
                cartBtn.disabled = false;
                inventory.classList.remove("bt20-csb_outOfStock");
                inventory.classList.add("bt20-csb_inStock");
                cart.classList.remove("bt20-csb_proOrdering");
            }
        } else {
            var notyfymeData = document.getElementById("bt32-notifyme_data");
            inventory.classList.remove("bt20-csb_inStock");
            inventory.classList.add("bt20-csb_outOfStock");
            cart.style.display = "none";
            notyfymeData.dataset.region = Boulies.setting.region;
            notyfymeData.dataset.itemName = current_variant.dataset.productSeries;
            notyfymeData.dataset.itemColor = current_variant.dataset.options;
            notyfymeData.dataset.itemSku = current_variant.id;
            notifymeContainer.style.display = "block";
        }

        // update addon items
        productInter.getAddonItems(current_variant_metadata.addon);

        // update promotion and set promotion counting
        var saleWrap = document.getElementById("bt34-mainPromotion");
        var _startDate_sub = new Date(current_variant_metadata.Sub_Promotion.start).getTime();
        var _endDate_sub = new Date(current_variant_metadata.Sub_Promotion.end).getTime();
        var _localDate = new Date().getTime();
        if (current_variant.dataset.saleStatus == "true") {
            document.getElementById("bt20-saleName").innerHTML = current_variant_metadata.Main_Promotion.name;
            document.getElementById("bt20-saleCode").innerHTML = current_variant_metadata.Main_Promotion.code;
            document.getElementById("bps_saleBannerBg").style["background-image"] = "url(" + current_variant_metadata.Main_Promotion.image + ")";
            document.getElementById("bt20-saleDiscount").innerHTML = current_variant_metadata.Main_Promotion.discount / 100;
            // update cart discount info
            document.getElementById("cart_discount_code").value = current_variant_metadata.Main_Promotion.code;
            document.getElementById("cart_discount_amount").value = current_variant_metadata.Main_Promotion.discount / 100;

            saleWrap.style.display = "block";

            // update price when on sale
            var sale_price = parseInt(current_variant.dataset.price) - current_variant_metadata.Main_Promotion.discount;
            document.getElementById("b5-collection__old_price").innerHTML = Boulies.formatPrice(parseInt(current_variant.dataset.price));
            document.getElementById("bt20-collection_price").innerHTML = Boulies.formatPrice(sale_price);
            document.getElementById("bt20-csb_pricesWrap").classList.add("b5-on-sale-price");
            // document.getElementById("Klarna_notify").dataset.purchaseAmount = sale_price;
            // if (!stopKlarnaReflash) {
            //     try {
            //         window.Klarna.OnsiteMessaging.refresh();
            //     } catch (err) {
            //         console.log(err);
            //     }
            // }
        } else {
            saleWrap.style.display = "none";

            // update price when not on sale
            document.getElementById("bt20-csb_pricesWrap").classList.remove("b5-on-sale-price");
            document.getElementById("bt20-collection_price").innerHTML = Boulies.formatPrice(parseInt(current_variant.dataset.price));
            // document.getElementById("Klarna_notify").dataset.purchaseAmount = parseInt(current_variant.dataset.price);
            // if (!stopKlarnaReflash) {
            //     try {
            //         window.Klarna.OnsiteMessaging.refresh();
            //     } catch (err) {
            //         console.log(err);
            //     }
            // }
        }
        if (_endDate_sub > _localDate && _startDate_sub < _localDate) {
            document.getElementById("sub_promotion_url__bt40").href = current_variant_metadata.Sub_Promotion.url;
            document.getElementById("sub_promotion_image__bt40").src = current_variant_metadata.Sub_Promotion.image;
            document.querySelector(".product_sub_promotion_wrapper__bt40").style.display = "block";
        } else {
            document.querySelector(".product_sub_promotion_wrapper__bt40").style.display = "none";
        }
    },
    variantsMap: {},
    makeVariantsMap: function () {
        var model_list = JSON.parse(document.querySelector("#product-model-list").dataset.list),
            final_price;
        model_list.forEach(function (model) {
            productInter.variantsMap[model] = {};
        });
        document.querySelectorAll(".bt51-variant").forEach(function (variant) {
            if (productInter.variantsMap[variant.dataset.option1][variant.dataset.option2] === undefined) {
                productInter.variantsMap[variant.dataset.option1][variant.dataset.option2] = [];
                if (variant.dataset.saleStatus == "true") {
                    final_price = parseInt(variant.dataset.price) - parseInt(variant.dataset.saleDiscount);
                } else {
                    final_price = parseInt(variant.dataset.price);
                }
                productInter.variantsMap[variant.dataset.option1][variant.dataset.option2].push({
                    sku: variant.id,
                    price: final_price,
                    model: variant.dataset.option1,
                    upholstery: variant.dataset.option2,
                    color: variant.dataset.option3,
                });
            } else {
                productInter.variantsMap[variant.dataset.option1][variant.dataset.option2].push({
                    sku: variant.id,
                    price: final_price,
                    model: variant.dataset.option1,
                    upholstery: variant.dataset.option2,
                    color: variant.dataset.option3,
                });
            }
        });
    },
    sortOptionsOnFirstLoad: function () {
        var models = Object.keys(productInter.variantsMap),
            upholsterys = [],
            models_HTML = "",
            upholsterys_HTML = "",
            default_variant = document.querySelector("input.bt51-variant:checked"),
            default_model,
            default_upholstery;

        if (default_variant) {
            default_model = default_variant.dataset.option1;
            default_upholstery = default_variant.dataset.option2;
        } else {
            var variants = document.querySelectorAll("input.bt51-variant"),
                default_option = document.getElementById("product-model-list").dataset.defaultOption.split(" / "),
                weighted_variants = [];
            for (var i = 0; i < variants.length; i++) {
                if (variants[i].dataset.option1 == default_option[0] && variants[i].dataset.option2 == default_option[1]) {
                    weighted_variants.push({
                        node: variants[i],
                        weight: 2,
                    });
                } else if (variants[i].dataset.option1 == default_option[0]) {
                    weighted_variants.push({
                        node: variants[i],
                        weight: 1,
                    });
                } else {
                    weighted_variants.push({
                        node: variants[i],
                        weight: 0,
                    });
                }
            }
            weighted_variants.sort(function (a, b) {
                return b.weight - a.weight;
            });
            weighted_variants[0].node.checked = true;
            default_model = weighted_variants[0].node.dataset.option1;
            default_upholstery = weighted_variants[0].node.dataset.option2;
        }

        for (var i = 0; i < models.length; i++) {
            var model_upholsterys = Object.keys(productInter.variantsMap[models[i]]);
            if (model_upholsterys.length == 0) continue;
            var is_checked = "",
                first_variant_preview = document.querySelector(".image_storage_" + productInter.variantsMap[models[i]][model_upholsterys[0]][0].sku),
                model_preview = "",
                low_price_list = [],
                low_price_HTML = "";
            if (first_variant_preview) {
                model_preview = 'style="background-image:url(' + Boulies.imageResize(first_variant_preview.value, 160, "heightOnly") + ')"';
            }
            for (var n = 0; n < model_upholsterys.length; n++) {
                low_price_list.push(
                    productInter.variantsMap[models[i]][model_upholsterys[n]].sort(function (a, b) {
                        return a.price - b.price;
                    })[0].price
                );
                if (upholsterys.indexOf(model_upholsterys[n]) !== -1) continue;
                upholsterys.push(model_upholsterys[n]);
            }
            low_price_HTML =
                "From " +
                Boulies.formatPrice(
                    low_price_list.sort(function (a, b) {
                        return a.price - b.price;
                    })[0]
                );
            if (models[i] == default_model) {
                is_checked = 'checked="checked"';
            }
            models_HTML +=
                '<input type="radio" ' +
                is_checked +
                ' class="bt51-model" name="product-model" id="bt51-model-' +
                i +
                '" value="' +
                models[i] +
                '"> <label for="bt51-model-' +
                i +
                '" class="bt51-model-display" data-value="' +
                models[i] +
                '"><div class="bt51-model-name-wrap"><span class="bt51-model-name">' +
                models[i] +
                '</span><span class="bt51-model-lowest-price">' +
                low_price_HTML +
                '</span></div><div class="bt51-model-image" ' +
                model_preview +
                "></div></label>";
        }

        for (var i = 0; i < upholsterys.length; i++) {
            var upholstery_preview = "",
                is_checked = "",
                storage_selector = document.querySelector('.upholstery_image_storage[data-option="' + upholsterys[i] + '"]');
            if (storage_selector) {
                upholstery_preview = 'style="background-image:url(' + storage_selector.value + ')"';
            }
            if (upholsterys[i] == default_upholstery) {
                is_checked = 'checked="checked"';
            }
            upholsterys_HTML +=
                '<input type="radio" ' +
                is_checked +
                ' class="bt51-upholstery" name="product-upholstery" id="bt51-upholstery-' +
                i +
                '" value="' +
                upholsterys[i] +
                '"> <label for="bt51-upholstery-' +
                i +
                '" class="bt51-upholstery-display" data-value="' +
                upholsterys[i] +
                '" ' +
                upholstery_preview +
                '><span class="bt51-upholstery-name">' +
                upholsterys[i] +
                "</span></label>";
        }

        document.getElementById("product-model-list").innerHTML = models_HTML;
        document.getElementById("product-upholstery-list").innerHTML = upholsterys_HTML;
    },
    changeUpholstery: function (stop_active_first_element) {
        var model_value = document.querySelector("input.bt51-model:checked").value;
        var upholstery_input = document.querySelectorAll(".bt51-upholstery");
        document.querySelectorAll(".bt51-upholstery-display").forEach(function (upholstery, index) {
            if (productInter.variantsMap[model_value][upholstery.dataset.value] === undefined) {
                upholstery.style.display = "none";
                upholstery_input[index].disabled = true;
            } else {
                upholstery.style.display = "block";
                upholstery_input[index].disabled = false;
            }
        });
        if (!stop_active_first_element) {
            document.querySelector("input.bt51-upholstery:enabled").checked = true;
            productInter.changeVariants(stop_active_first_element);
        } else {
            productInter.changeVariants(stop_active_first_element);
        }
    },
    changeVariants: function (stop_active_first_element) {
        var model_value = document.querySelector('input[name="product-model"]:checked').value;
        var upholstery_value = document.querySelector('input[name="product-upholstery"]:checked').value;
        var variant_input = document.querySelectorAll(".bt51-variant");
        document.querySelectorAll(".bt51-variant-display").forEach(function (variant, index) {
            if (variant.dataset.option1 == model_value && variant.dataset.option2 == upholstery_value) {
                variant.style.display = "block";
                variant_input[index].disabled = false;
            } else {
                variant.style.display = "none";
                variant_input[index].disabled = true;
            }
        });
        if (!stop_active_first_element) {
            document.querySelector("input.bt51-variant:enabled").checked = true;
            productInter.changeToThisOption(document.querySelector('input[name="id"]:checked'));
        } else {
            productInter.changeToThisOption(document.querySelector('input[name="id"]:checked'), false, stop_active_first_element);
        }
    },
    getAddonItems: function (addonItems) {
        if (!addonItems || addonItems.length < 1) return false;
        var addonSection = document.getElementById("bt32-addonSection");
        var itemList = addonSection.querySelector(".bt32-addonLists");
        if (itemList.dataset.addonList == addonItems.join()) return;
        itemList.innerHTML = "";
        addonItems.forEach(function (v) {
            $.getJSON("/products/" + v + ".js", function (item) {
                var tempDOM = document.createElement("div");
                tempDOM.setAttribute("class", "bt32-addonItem");
                var priceDom =
                    item.compare_at_price > item.price
                        ? '<span style="display: block;text-decoration: line-through;color: #909090;">' +
                          Boulies.formatPrice(item.compare_at_price) +
                          "</span>" +
                          Boulies.formatPrice(item.price)
                        : Boulies.formatPrice(item.price);
                tempDOM.innerHTML =
                    '<div class="bt32-addonCheckbox"><input type="checkbox" name="id" id="id_' +
                    item.variants[0].id +
                    '" value="' +
                    item.variants[0].id +
                    '"><label class="iconFont" for="id_' +
                    item.variants[0].id +
                    '"><svg viewBox="0 0 1249 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="12px" height="10px"><path d="M123.877278 460.145486l291.935697 232.573284L1142.743853 23.028984c0 0 48.79669-44.514733 91.361505-9.760596 12.67761 10.485525 27.308282 40.111693-5.665769 86.513444L469.504902 988.873523c0 0-58.225485 79.613251-127.299746-0.872745L14.716485 545.731146c0 0-38.875697-59.84832 9.743298-95.764545C40.865824 437.933722 78.176869 419.179917 123.877278 460.145486L123.877278 460.145486 123.877278 460.145486zM123.877278 460.145486" fill="transparent"></path></svg></label></div><div class="bt32-addonImage"><img src="' +
                    Boulies.imageResize(item.featured_image, 160) +
                    '" alt=""></div><div class="bt32-addonDetails"><div class="bt32-addonHeader"><h3 class="bt32-addonTitle">' +
                    item.title +
                    '</h3><div class="bt32-addonPrice">' +
                    priceDom +
                    '</div></div><div class="bt32-addonDescription">' +
                    item.description +
                    "</div></div>";
                itemList.appendChild(tempDOM);
            });
        });
        addonSection.style.display = "block";
        itemList.dataset.addonList = addonItems.join();
        addonSection.addEventListener("click", function (e) {
            var item = e.target.closest(".bt32-addonItem");
            if (!item) return;
            if (!addonSection.contains(item)) return;
            if (item.classList.toggle("bt32-activedAddonItem")) {
                item.querySelector("input[type=checkbox]").checked = true;
            } else {
                item.querySelector("input[type=checkbox]").checked = false;
            }
        });
    },
    notifyMe: function (target) {
        if (target.classList.contains("disabled")) return;
        var notifymeData = document.getElementById("bt32-notifyme_data");
        if (!/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/.test(notifymeData.value)) {
            alert("Please enter an valid email address!");
            return;
        }
        var requestData = JSON.parse(JSON.stringify(notifymeData.dataset));
        requestData["email"] = notifymeData.value;
        $.ajax({
            type: "POST",
            url: "https://app.boulies.com/notifyorders/notifyme",
            data: requestData,
            beforeSend: function () {
                target.classList.add("disabled");
                target.innerHTML = "Sending request...";
            },
            error: function (xhr, status, error) {
                alert(status);
                target.classList.remove("disabled");
                target.innerHTML = "Notify me";
            },
            success: function () {
                target.innerHTML = "We've received your request!";
            },
        });
    },
    init: function () {
        productInter.makeVariantsMap();

        productInter.sortOptionsOnFirstLoad();

        productInter.changeUpholstery(true);

        document.getElementById("bt20-csb_cartBtn").addEventListener("click", function (e) {
            e.preventDefault();
            productInter.addToCart(this);
        });

        document.getElementById("bt32-notifymeBtn").addEventListener("click", function (e) {
            e.preventDefault();
            productInter.notifyMe(this);
        });

        document.querySelectorAll(".bt51-model").forEach(function (model) {
            model.addEventListener("change", function () {
                productInter.changeUpholstery();
            });
        });

        document.querySelectorAll(".bt51-upholstery").forEach(function (upholstery) {
            upholstery.addEventListener("change", function () {
                productInter.changeVariants();
            });
        });

        document.querySelectorAll(".bt51-variant").forEach(function (option) {
            option.addEventListener("change", function () {
                productInter.changeToThisOption(this);
            });
        });

        document.querySelectorAll(".bt51-variant-display").forEach(function (option) {
            var storage_selector = '.color_image_storage[data-option="' + option.dataset.option2 + " / " + option.dataset.option3 + '"]';
            if (document.querySelector(storage_selector)) {
                option.style.backgroundImage = "url(" + document.querySelector(storage_selector).value + ")";
            }
        });

        window.addEventListener("popstate", (event) => {
            var url_param = Boulies.getUrlkey();
            if (!url_param || !url_param.variant) return false;
            var selected_variant = document.querySelector('input[value="' + url_param.variant + '"]');
            selected_variant.checked = true;
            productInter.changeToThisOption(selected_variant, true);
        });
    },
};

// ==============================================================
// Run Code After Document Ready
// ==============================================================
$(document).ready(function () {
    productInter.init();

    $(".bt20-csb_mainImage").on("click", ".pswp_image", function (e) {
        e.preventDefault();
        dynamicGallery.refresh([{ src: this.href }]);
        dynamicGallery.openGallery();
    });

    $(".dynamic-gallery-item").on("click", function (e) {
        e.preventDefault();
        dynamicGallery.refresh([{ src: this.href }]);
        dynamicGallery.openGallery();
    });

    var choose_guide = document.querySelector(".choose-guide-wrap__bt51");
    if (choose_guide) {
        choose_guide.addEventListener("click", function (e) {
            var trigger = e.target.closest(".choose-guide-header__bt51");
            if (!trigger) return;
            if (this.dataset.status == "is_actived") {
                this.dataset.status = "";
            } else {
                this.dataset.status = "is_actived";
            }
        });
    }

    if (Boulies.getUrlkey().judgeme_dynamic_form == "1") {
        var injectDOM = document.createElement("div");
        injectDOM.style.display = "inline-block";
        injectDOM.innerHTML =
            '<p style=" background-color: #fcfff5; color: #2c662d; margin-top: 10px; padding: 10px 20px; border: 1px solid #a3c293; border-radius: 4px; text-align: left; font-size: 14px; "> <b style=" display: block; margin-bottom: 2px; font-size: 16px; ">Add picture/video for a reward.</b> By submitting with picture/video, boulies will offer 6 months extended warranty as a bonus.</p> <p style=" text-align: left; padding: 4px 20px 0; font-size: 14px; color: #6e6e6e; ">* A notification email will send to you when successfully submit a picture/video review.</p>';
        var insert_notify = setInterval(function () {
            if (document.querySelector(".jdgm-form-dynamic__picture-upload-field")) {
                document.querySelector(".jdgm-form-dynamic__picture-upload-field").appendChild(injectDOM);
                clearInterval(insert_notify);
            }
        }, 1000);
    }

    if (!!document.querySelector(".bt51-bestof-wrap")) {
        var bestOfList = $(".bt51-bestofs").slick({
            slidesToShow: 3,
            dots: false,
            arrows: false,
            speed: 500,
            responsive: [
                {
                    breakpoint: 800,
                    settings: {
                        slidesToShow: 1,
                        dots: true,
                    },
                },
            ],
        });
    }
});
